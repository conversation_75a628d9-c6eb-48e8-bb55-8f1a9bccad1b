import axios from 'axios';

const API_URL = process.env.VUE_APP_API_URL || 'http://localhost:8001';

class StudyAssistantService {
  /**
   * Get user study statistics
   * @param {string} userId - User ID
   * @returns {Promise} - Promise with user study stats
   */
  async getStudyStats(userId) {
    try {
      const response = await axios.get(`${API_URL}/api/ai/study-stats/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Error in getStudyStats:', error);
      throw error;
    }
  }

  /**
   * Get focus areas with mastery levels and recommendations
   * @param {string} userId - User ID
   * @returns {Promise} - Promise with focus areas data
   */
  async getFocusAreas(userId) {
    try {
      const response = await axios.get(`${API_URL}/api/ai/focus-areas/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Error in getFocusAreas:', error);
      throw error;
    }
  }

  /**
   * Generate personalized study plan
   * @param {Object} payload - Plan configuration
   * @param {string} payload.goal - Study goal ('exam', 'residency', 'review', 'topic')
   * @param {number} payload.hoursPerWeek - Hours available per week
   * @param {number} payload.durationWeeks - Plan duration in weeks
   * @param {Array} payload.selectedSpecialties - Array of specialty IDs
   * @returns {Promise} - Promise with generated study plan
   */
  async generateStudyPlan(payload) {
    try {
      const response = await axios.post(`${API_URL}/api/ai/generate-study-plan`, payload);
      return response.data;
    } catch (error) {
      console.error('Error in generateStudyPlan:', error);
      throw error;
    }
  }

  /**
   * Save a generated study plan
   * @param {Object} plan - Study plan to save
   * @param {string} userId - User ID
   * @returns {Promise} - Promise with save confirmation
   */
  async saveStudyPlan(plan, userId) {
    try {
      const response = await axios.post(`${API_URL}/api/ai/save-study-plan`, { 
        plan, 
        userId 
      });
      return response.data;
    } catch (error) {
      console.error('Error in saveStudyPlan:', error);
      throw error;
    }
  }

  /**
   * Generate concept map for a specific topic
   * @param {string} topic - Topic to generate concept map for
   * @returns {Promise} - Promise with concept map data
   */
  async generateConceptMap(topic) {
    try {
      const response = await axios.post(`${API_URL}/api/ai/concept-map`, { topic });
      return response.data;
    } catch (error) {
      console.error('Error in generateConceptMap:', error);
      throw error;
    }
  }

  // New Study Plans API methods
  /**
   * Get all study plans
   * @param {Object} params - Query parameters
   * @returns {Promise} - Promise with study plans array
   */
  async getStudyPlans(params = {}) {
    try {
      const response = await axios.get(`${API_URL}/api/study-plans/`, { params });
      return response.data;
    } catch (error) {
      console.error('Error in getStudyPlans:', error);
      throw error;
    }
  }

  /**
   * Create a new study plan
   * @param {Object} planData - Study plan data
   * @returns {Promise} - Promise with created study plan
   */
  async createStudyPlan(planData) {
    try {
      const response = await axios.post(`${API_URL}/api/study-plans/`, planData);
      return response.data;
    } catch (error) {
      console.error('Error in createStudyPlan:', error);
      throw error;
    }
  }

  /**
   * Update an existing study plan
   * @param {string} planId - Plan ID
   * @param {Object} planData - Updated plan data
   * @returns {Promise} - Promise with updated study plan
   */
  async updateStudyPlan(planId, planData) {
    try {
      const response = await axios.put(`${API_URL}/api/study-plans/${planId}`, planData);
      return response.data;
    } catch (error) {
      console.error('Error in updateStudyPlan:', error);
      throw error;
    }
  }

  /**
   * Delete a study plan
   * @param {string} planId - Plan ID
   * @returns {Promise} - Promise with deletion confirmation
   */
  async deleteStudyPlan(planId) {
    try {
      const response = await axios.delete(`${API_URL}/api/study-plans/${planId}`);
      return response.data;
    } catch (error) {
      console.error('Error in deleteStudyPlan:', error);
      throw error;
    }
  }

  /**
   * Create a study session
   * @param {string} planId - Plan ID
   * @param {Object} sessionData - Session data
   * @returns {Promise} - Promise with created session
   */
  async createStudySession(planId, sessionData) {
    try {
      const response = await axios.post(`${API_URL}/api/study-plans/${planId}/sessions`, sessionData);
      return response.data;
    } catch (error) {
      console.error('Error in createStudySession:', error);
      throw error;
    }
  }

  /**
   * Get study sessions for a plan
   * @param {string} planId - Plan ID
   * @returns {Promise} - Promise with sessions array
   */
  async getStudySessions(planId) {
    try {
      const response = await axios.get(`${API_URL}/api/study-plans/${planId}/sessions`);
      return response.data;
    } catch (error) {
      console.error('Error in getStudySessions:', error);
      throw error;
    }
  }

  /**
   * Get study statistics overview
   * @returns {Promise} - Promise with statistics data
   */
  async getStudyStatsOverview() {
    try {
      const response = await axios.get(`${API_URL}/api/study-plans/stats/overview`);
      return response.data;
    } catch (error) {
      console.error('Error in getStudyStatsOverview:', error);
      throw error;
    }
  }
}

export default new StudyAssistantService();