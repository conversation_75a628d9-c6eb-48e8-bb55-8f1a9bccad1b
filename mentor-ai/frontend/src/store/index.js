import { createStore } from 'vuex'
import auth from './modules/auth'
import calendar from './modules/calendar'
import revisions from './modules/revisions'
import secondBrain from './modules/secondBrain'
import resumosNotas from './modules/resumosNotas'
// aiapps module removed
import studyStatus from './modules/studyStatus'
import provasSimulados from './modules/provasSimulados'
import resources from './modules/resources'
import ai from './modules/ai'
import subjects from './modules/subjects'
import createPersistedState from 'vuex-persistedstate'
import notifications from './modules/notifications'
import learningMetrics from './modules/learningMetrics'
import performance from './modules/performance'
import studyEnvironment from './modules/studyEnvironment'
import gamification from './modules/gamification'

export default createStore({
  state: {
    // Define your state here
  },
  mutations: {
    // Define your mutations here
  },
  actions: {
    // Define your actions here
  },
  modules: {
    auth,
    calendar,
    revisions,
    secondBrain,
    resumosNotas,
    // aiapps removed
    studyStatus,
    provasSimulados,
    resources,
    ai,
    subjects,
    notifications,
    learningMetrics,
    performance,
    studyEnvironment,
    gamification
  },
  plugins: [
    createPersistedState({
      paths: [
        'auth.isAuthenticated',
        'auth.user',
        'calendar.events',
        'calendar.pendingEvents',
        'revisions.allRevisions',
        'studyStatus.studyStatus',
        'revisionScheduler.items',
        'learningMetrics',
        'performance.performanceMetrics',
        'performance.studyGoals',
        'performance.achievements',
        'subjects.subjects',
        'studyEnvironment.userLevel',
        'studyEnvironment.userExperience',
        'studyEnvironment.totalStudyTime',
        'studyEnvironment.currentStreak',
        'studyEnvironment.goals',
        'studyEnvironment.notes',
        'studyEnvironment.tasks',
        'studyEnvironment.settings',
        'studyEnvironment.unlockedAchievements',
        'gamification.user',
        'gamification.stats',
        'gamification.dailyChallenges'
      ]
    })
  ]
}) 