<template>
  <div class="study-plan-view">
    <StudyPlanPageFinal v-if="!showCreationForm" @create-plan="showCreationForm = true" />
    <StudyPlanPage v-else @back="showCreationForm = false" @plan-created="handlePlanCreated" />
  </div>
</template>

<script>
import StudyPlanPage from '../components/StudyPlanPage.vue'
import StudyPlanPageFinal from '../components/StudyPlanPageFinal.vue'

export default {
  name: 'StudyPlanView',
  components: {
    StudyPlanPage,
    StudyPlanPageFinal
  },
  data() {
    return {
      showCreationForm: false
    }
  },
  methods: {
    handlePlanCreated() {
      // Return to the main view after plan is created
      this.showCreationForm = false
    }
  }
}
</script>

<style scoped>
.study-plan-view {
  min-height: 100vh;
}
</style>