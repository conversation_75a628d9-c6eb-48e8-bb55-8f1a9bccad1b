<template>
  <div class="subject-manager">
    <!-- Ultra Modern Background -->
    <div class="background-effects">
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
      <div class="gradient-orb orb-4"></div>
      <div class="floating-particles"></div>
      <div class="grid-overlay"></div>
      <div class="animated-lines">
        <div class="line line-1"></div>
        <div class="line line-2"></div>
        <div class="line line-3"></div>
      </div>
    </div>

    <!-- Modal Content -->
    <div class="modal-content">
      <!-- Close Button -->
      <button @click="$emit('close')" class="close-button">
        <div class="close-icon">
          <span></span>
          <span></span>
        </div>
      </button>

      <!-- Header Section -->
      <div class="header-section">
        <div class="header-background"></div>
        <div class="header-content">
          <div class="header-icon">
            <div class="icon-wrapper">
              <i class="fas fa-graduation-cap"></i>
              <div class="icon-ring ring-1"></div>
              <div class="icon-ring ring-2"></div>
              <div class="icon-glow"></div>
            </div>
          </div>
          <div class="header-text">
            <h1 class="main-title">
              <span class="title-gradient">Gerenciar</span>
              <span class="title-accent">Disciplinas</span>
            </h1>
            <p class="subtitle">Organize suas matérias e maximize seu aprendizado</p>
          </div>
          <div class="header-stats">
            <div class="stat-item">
              <div class="stat-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                <i class="fas fa-book"></i>
              </div>
              <div class="stat-info">
                <span class="stat-value">{{ subjects.length }}</span>
                <span class="stat-label">Disciplinas</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon" style="background: linear-gradient(135deg, #f093fb, #f5576c);">
                <i class="fas fa-calendar-check"></i>
              </div>
              <div class="stat-info">
                <span class="stat-value">{{ totalRevisions }}</span>
                <span class="stat-label">Revisões</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="content-container">
        <!-- Create New Subject Section -->
        <div class="create-section">
          <div class="section-header">
            <div class="section-icon">
              <i class="fas fa-plus-circle"></i>
            </div>
            <h2>Nova Disciplina</h2>
            <div class="section-line"></div>
          </div>

          <div class="form-container">
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label">
                  <i class="fas fa-font"></i>
                  Nome da Disciplina
                </label>
                <div class="input-wrapper">
                  <input
                    v-model="newSubject.name"
                    type="text"
                    placeholder="Ex: Matemática Avançada"
                    class="form-input"
                    @keyup.enter="addSubject"
                  />
                  <div class="input-glow"></div>
                </div>
              </div>

              <div class="form-group">
                <label class="form-label">
                  <i class="fas fa-palette"></i>
                  Cor do Tema
                </label>
                <div class="color-picker-container">
                  <div class="selected-color" :style="{ backgroundColor: newSubject.color }">
                    <i class="fas fa-check" v-if="newSubject.color"></i>
                  </div>
                  <div class="color-palette">
                    <button
                      v-for="color in colorPalette"
                      :key="color"
                      @click="newSubject.color = color"
                      class="color-option"
                      :class="{ selected: newSubject.color === color }"
                      :style="{ backgroundColor: color }"
                    >
                      <div class="color-inner"></div>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="form-actions">
              <button 
                @click="addSubject" 
                :disabled="!newSubject.name || !newSubject.color"
                class="create-button"
              >
                <div class="button-content">
                  <i class="fas fa-plus"></i>
                  <span>Criar Disciplina</span>
                </div>
                <div class="button-glow"></div>
                <div class="button-particles">
                  <span></span><span></span><span></span>
                </div>
              </button>
            </div>
          </div>
        </div>

        <!-- Subjects List Section -->
        <div class="subjects-section">
          <div class="section-header">
            <div class="section-icon">
              <i class="fas fa-layer-group"></i>
            </div>
            <h2>Disciplinas Cadastradas</h2>
            <div class="section-line"></div>
          </div>

          <div class="subjects-grid" v-if="subjects.length > 0">
            <transition-group name="subject-list" tag="div" class="grid-container">
              <div
                v-for="subject in subjects"
                :key="subject.id"
                class="subject-card"
                @mouseenter="hoveredSubject = subject.id"
                @mouseleave="hoveredSubject = null"
              >
                <div class="card-background" :style="{ background: `linear-gradient(135deg, ${subject.color}20, ${subject.color}10)` }"></div>
                <div class="card-glow" :style="{ background: `radial-gradient(circle at center, ${subject.color}30, transparent)` }"></div>
                
                <div class="card-header">
                  <div class="subject-icon" :style="{ backgroundColor: subject.color + '20' }">
                    <i class="fas fa-book-open" :style="{ color: subject.color }"></i>
                  </div>
                  <button @click="removeSubject(subject.id)" class="delete-button">
                    <i class="fas fa-trash-alt"></i>
                  </button>
                </div>

                <div class="card-content">
                  <h3 class="subject-name">{{ subject.name }}</h3>
                  <div class="subject-stats">
                    <div class="stat">
                      <i class="fas fa-clock"></i>
                      <span>{{ getSubjectRevisions(subject.id) }} revisões</span>
                    </div>
                    <div class="stat">
                      <i class="fas fa-calendar"></i>
                      <span>{{ getNextRevision(subject.id) }}</span>
                    </div>
                  </div>
                </div>

                <div class="card-footer">
                  <div class="progress-bar">
                    <div 
                      class="progress-fill" 
                      :style="{ 
                        width: getSubjectProgress(subject.id) + '%',
                        background: `linear-gradient(90deg, ${subject.color}, ${subject.color}dd)`
                      }"
                    ></div>
                  </div>
                  <span class="progress-text">{{ getSubjectProgress(subject.id) }}% concluído</span>
                </div>

                <div class="card-hover-effect" :style="{ '--mouse-x': mouseX + 'px', '--mouse-y': mouseY + 'px' }"></div>
              </div>
            </transition-group>
          </div>

          <!-- Empty State -->
          <div v-else class="empty-state">
            <div class="empty-icon">
              <i class="fas fa-folder-open"></i>
              <div class="empty-icon-bg"></div>
            </div>
            <h3>Nenhuma disciplina cadastrada</h3>
            <p>Comece adicionando sua primeira disciplina acima</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SubjectManager',
  data() {
    return {
      newSubject: {
        name: '',
        color: ''
      },
      hoveredSubject: null,
      mouseX: 0,
      mouseY: 0,
      colorPalette: [
        '#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe',
        '#fa709a', '#fee140', '#30cfd0', '#330867', '#a8edea', '#fed6e3',
        '#ff6b6b', '#4ecdc4', '#45b7d1', '#f7b731', '#5f27cd', '#00d2d3',
        '#ff9ff3', '#54a0ff', '#48dbfb', '#0abde3'
      ]
    }
  },
  computed: {
    subjects() {
      return this.$store.getters['subjects/allSubjects'] || [];
    },
    totalRevisions() {
      const events = this.$store.state.calendar?.events || [];
      return events.filter(e => e.isRevision).length;
    }
  },
  methods: {
    async addSubject() {
      if (!this.newSubject.name || !this.newSubject.color) return;

      const subject = {
        id: Date.now().toString(),
        ...this.newSubject,
        createdAt: new Date().toISOString()
      };

      await this.$store.dispatch('subjects/addSubject', subject);

      // Add success animation
      this.$nextTick(() => {
        const button = document.querySelector('.create-button');
        button.classList.add('success');
        setTimeout(() => button.classList.remove('success'), 1000);
      });

      // Reset form
      this.newSubject = {
        name: '',
        color: ''
      };
    },
    async removeSubject(id) {
      if (confirm('Tem certeza que deseja remover esta disciplina?')) {
        await this.$store.dispatch('subjects/deleteSubject', id);
      }
    },
    getSubjectRevisions(subjectId) {
      const events = this.$store.state.calendar?.events || [];
      return events.filter(e => e.subject === subjectId && e.isRevision).length;
    },
    getNextRevision(subjectId) {
      const events = this.$store.state.calendar?.events || [];
      const now = new Date();
      const nextRevision = events
        .filter(e => e.subject === subjectId && e.isRevision && new Date(e.start) > now)
        .sort((a, b) => new Date(a.start) - new Date(b.start))[0];
      
      if (nextRevision) {
        const date = new Date(nextRevision.start);
        const days = Math.ceil((date - now) / (1000 * 60 * 60 * 24));
        if (days === 0) return 'Hoje';
        if (days === 1) return 'Amanhã';
        return `Em ${days} dias`;
      }
      return 'Sem revisões';
    },
    getSubjectProgress(subjectId) {
      const events = this.$store.state.calendar?.events || [];
      const subjectEvents = events.filter(e => e.subject === subjectId && e.isRevision);
      const completedEvents = subjectEvents.filter(e => e.completed);
      return subjectEvents.length > 0 
        ? Math.round((completedEvents.length / subjectEvents.length) * 100)
        : 0;
    }
  },
  mounted() {
    // Track mouse position for card hover effects
    document.addEventListener('mousemove', (e) => {
      const cards = document.querySelectorAll('.subject-card');
      cards.forEach(card => {
        const rect = card.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        card.style.setProperty('--mouse-x', x);
        card.style.setProperty('--mouse-y', y);
      });
    });
  }
}
</script>

<style scoped>
.subject-manager {
  position: fixed;
  inset: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  animation: modalFadeIn 0.3s ease;
}

/* Ultra Modern Background */
.background-effects {
  position: fixed;
  inset: 0;
  overflow: hidden;
  background: #0a0b1e;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.4;
  animation: orbFloat 20s ease-in-out infinite;
}

.orb-1 {
  width: 600px;
  height: 600px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  top: -200px;
  left: -200px;
  animation-duration: 25s;
}

.orb-2 {
  width: 400px;
  height: 400px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  bottom: -100px;
  right: -100px;
  animation-duration: 30s;
  animation-delay: -5s;
}

.orb-3 {
  width: 500px;
  height: 500px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-duration: 35s;
  animation-delay: -10s;
}

.orb-4 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  top: 20%;
  right: 20%;
  animation-duration: 20s;
  animation-delay: -15s;
}

@keyframes orbFloat {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(50px, -50px) rotate(90deg); }
  50% { transform: translate(-30px, 50px) rotate(180deg); }
  75% { transform: translate(-50px, -30px) rotate(270deg); }
}

.floating-particles {
  position: absolute;
  inset: 0;
  background-image: 
    radial-gradient(2px 2px at 20% 30%, white, transparent),
    radial-gradient(2px 2px at 60% 70%, white, transparent),
    radial-gradient(1px 1px at 90% 10%, white, transparent),
    radial-gradient(1px 1px at 15% 80%, white, transparent);
  background-size: 500px 500px;
  background-position: 0 0, 100px 100px, 200px 200px, 300px 300px;
  animation: particlesFloat 100s linear infinite;
  opacity: 0.1;
}

@keyframes particlesFloat {
  from { transform: translateY(0); }
  to { transform: translateY(-500px); }
}

.grid-overlay {
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(255,255,255,0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255,255,255,0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  from { transform: translate(0, 0); }
  to { transform: translate(50px, 50px); }
}

.animated-lines {
  position: absolute;
  inset: 0;
  overflow: hidden;
}

.line {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.4), transparent);
  height: 1px;
  width: 200%;
  animation: lineMove 8s linear infinite;
}

.line-1 { top: 20%; animation-delay: 0s; }
.line-2 { top: 50%; animation-delay: 2s; }
.line-3 { top: 80%; animation-delay: 4s; }

@keyframes lineMove {
  from { transform: translateX(-100%); }
  to { transform: translateX(100%); }
}

/* Modal Content */
.modal-content {
  position: relative;
  width: 100%;
  max-width: 1200px;
  max-height: 90vh;
  background: rgba(13, 14, 31, 0.95);
  border-radius: 32px;
  overflow: hidden;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 24px 48px rgba(0, 0, 0, 0.4),
    inset 0 1px 1px rgba(255, 255, 255, 0.1);
  animation: modalSlideIn 0.4s ease;
}

@keyframes modalFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modalSlideIn {
  from { transform: translateY(50px) scale(0.95); opacity: 0; }
  to { transform: translateY(0) scale(1); opacity: 1; }
}

/* Close Button */
.close-button {
  position: absolute;
  top: 2rem;
  right: 2rem;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(90deg);
}

.close-icon {
  width: 20px;
  height: 20px;
  position: relative;
}

.close-icon span {
  position: absolute;
  width: 100%;
  height: 2px;
  background: white;
  border-radius: 2px;
  top: 50%;
  left: 0;
  transform-origin: center;
}

.close-icon span:first-child {
  transform: translateY(-50%) rotate(45deg);
}

.close-icon span:last-child {
  transform: translateY(-50%) rotate(-45deg);
}

/* Header Section */
.header-section {
  position: relative;
  padding: 3rem 3rem 2rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-background {
  position: absolute;
  inset: 0;
  background: 
    radial-gradient(circle at 20% 50%, rgba(102, 126, 234, 0.2), transparent 40%),
    radial-gradient(circle at 80% 50%, rgba(240, 147, 251, 0.2), transparent 40%);
  animation: headerPulse 10s ease-in-out infinite;
}

@keyframes headerPulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 0.8; }
}

.header-content {
  position: relative;
  display: flex;
  align-items: center;
  gap: 3rem;
  flex-wrap: wrap;
}

.header-icon {
  position: relative;
}

.icon-wrapper {
  position: relative;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-wrapper i {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  z-index: 3;
}

.icon-ring {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  border: 2px solid;
  opacity: 0.3;
}

.ring-1 {
  border-color: #667eea;
  animation: ringPulse 3s ease-in-out infinite;
}

.ring-2 {
  border-color: #764ba2;
  animation: ringPulse 3s ease-in-out infinite 0.5s;
  transform: scale(1.2);
}

@keyframes ringPulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.6; }
}

.icon-glow {
  position: absolute;
  inset: -20px;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.4), transparent);
  filter: blur(20px);
  animation: glowPulse 3s ease-in-out infinite;
}

@keyframes glowPulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 0.8; }
}

.header-text {
  flex: 1;
}

.main-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.title-gradient {
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.title-accent {
  color: white;
}

.subtitle {
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.1rem;
}

.header-stats {
  display: flex;
  gap: 2rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

/* Content Container */
.content-container {
  padding: 2rem 3rem;
  overflow-y: auto;
  max-height: calc(90vh - 200px);
}

.content-container::-webkit-scrollbar {
  width: 8px;
}

.content-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.content-container::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.3);
  border-radius: 4px;
}

.content-container::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.5);
}

/* Section Headers */
.section-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  position: relative;
}

.section-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 1.2rem;
}

.section-header h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin: 0;
}

.section-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, rgba(102, 126, 234, 0.3), transparent);
}

/* Create Section */
.create-section {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 24px;
  padding: 2rem;
  margin-bottom: 3rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.create-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.5), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.form-label i {
  color: #667eea;
}

.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(102, 126, 234, 0.5);
}

.input-glow {
  position: absolute;
  inset: -1px;
  border-radius: 16px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  opacity: 0;
  filter: blur(10px);
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.form-input:focus ~ .input-glow {
  opacity: 0.3;
}

/* Color Picker */
.color-picker-container {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.selected-color {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.color-palette {
  display: grid;
  grid-template-columns: repeat(11, 1fr);
  gap: 0.5rem;
  flex: 1;
}

@media (max-width: 768px) {
  .color-palette {
    grid-template-columns: repeat(6, 1fr);
  }
}

.color-option {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.color-option:hover {
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.color-option.selected {
  border-color: white;
  transform: scale(1.15);
}

.color-inner {
  position: absolute;
  inset: 2px;
  border-radius: 8px;
  background: inherit;
}

/* Create Button */
.form-actions {
  display: flex;
  justify-content: flex-end;
}

.create-button {
  position: relative;
  padding: 1rem 2.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.create-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.create-button:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.button-content {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  z-index: 2;
}

.button-glow {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.create-button:not(:disabled):hover .button-glow {
  opacity: 1;
}

.button-particles {
  position: absolute;
  inset: 0;
  overflow: hidden;
}

.button-particles span {
  position: absolute;
  width: 4px;
  height: 4px;
  background: white;
  border-radius: 50%;
  opacity: 0;
}

.create-button.success .button-particles span {
  animation: particleBurst 0.8s ease-out;
}

.button-particles span:nth-child(1) { top: 50%; left: 50%; }
.button-particles span:nth-child(2) { top: 50%; left: 50%; animation-delay: 0.1s; }
.button-particles span:nth-child(3) { top: 50%; left: 50%; animation-delay: 0.2s; }

@keyframes particleBurst {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }
  100% {
    transform: translate(calc(-50% + 30px), calc(-50% - 30px)) scale(1);
    opacity: 0;
  }
}

/* Subjects Section */
.subjects-section {
  margin-top: 3rem;
}

.subjects-grid {
  width: 100%;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

/* Subject Card */
.subject-card {
  position: relative;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 24px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  cursor: default;
}

.subject-card:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 255, 255, 0.2);
}

.card-background {
  position: absolute;
  inset: 0;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.subject-card:hover .card-background {
  opacity: 0.7;
}

.card-glow {
  position: absolute;
  inset: -50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.subject-card:hover .card-glow {
  opacity: 0.5;
}

.card-hover-effect {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%), rgba(255, 255, 255, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.subject-card:hover .card-hover-effect {
  opacity: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  position: relative;
  z-index: 2;
}

.subject-icon {
  width: 48px;
  height: 48px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.delete-button {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
}

.subject-card:hover .delete-button {
  opacity: 1;
}

.delete-button:hover {
  background: rgba(245, 87, 108, 0.2);
  border-color: rgba(245, 87, 108, 0.5);
  color: #f5576c;
}

.card-content {
  position: relative;
  z-index: 2;
  margin-bottom: 1.5rem;
}

.subject-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.75rem;
}

.subject-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
}

.stat i {
  color: rgba(255, 255, 255, 0.4);
}

.card-footer {
  position: relative;
  z-index: 2;
}

.progress-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.6s ease;
}

.progress-text {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
}

.empty-icon {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  margin-bottom: 2rem;
}

.empty-icon i {
  font-size: 4rem;
  color: rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
}

.empty-icon-bg {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.1), transparent);
  border-radius: 50%;
  animation: emptyPulse 3s ease-in-out infinite;
}

@keyframes emptyPulse {
  0%, 100% { transform: scale(1); opacity: 0.5; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

.empty-state h3 {
  font-size: 1.5rem;
  color: white;
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: rgba(255, 255, 255, 0.6);
}

/* Animations */
.subject-list-enter-active,
.subject-list-leave-active {
  transition: all 0.3s ease;
}

.subject-list-enter-from {
  opacity: 0;
  transform: scale(0.8);
}

.subject-list-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

.subject-list-move {
  transition: transform 0.3s ease;
}

/* Responsive */
@media (max-width: 768px) {
  .modal-content {
    max-width: 100%;
    max-height: 100vh;
    border-radius: 0;
  }

  .header-content {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }

  .header-stats {
    justify-content: center;
  }

  .main-title {
    font-size: 2rem;
    justify-content: center;
  }

  .content-container {
    padding: 1.5rem;
  }

  .grid-container {
    grid-template-columns: 1fr;
  }
}
</style>