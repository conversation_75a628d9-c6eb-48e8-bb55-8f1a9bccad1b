<template>
  <div class="metric-card" :class="[`color-${color}`, { clickable: clickable }]" @click="handleClick">
    <div class="metric-icon">
      <font-awesome-icon :icon="`fa-solid ${icon}`" />
    </div>
    <div class="metric-info">
      <div class="metric-value">{{ value }}{{ suffix }}</div>
      <div class="metric-label">{{ label }}</div>
    </div>
    <div class="metric-glow"></div>
  </div>
</template>

<script>
export default {
  name: 'MetricCard',
  props: {
    icon: {
      type: String,
      required: true
    },
    value: {
      type: [String, Number],
      required: true
    },
    suffix: {
      type: String,
      default: ''
    },
    label: {
      type: String,
      required: true
    },
    color: {
      type: String,
      default: 'primary'
    },
    clickable: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleClick() {
      if (this.clickable) {
        this.$emit('click');
      }
    }
  }
};
</script>

<style scoped>
.metric-card {
  position: relative;
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  min-width: 160px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.metric-card.clickable {
  cursor: pointer;
}

.metric-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.metric-icon {
  width: 40px;
  height: 40px;
  background: rgba(66, 185, 131, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: var(--color-primary);
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--color-text);
  line-height: 1;
  margin-bottom: 0.2rem;
}

.metric-label {
  font-size: 0.85rem;
  color: var(--color-text-secondary);
}

.metric-glow {
  position: absolute;
  top: 0;
  right: 0;
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, rgba(66, 185, 131, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(20px, -20px);
  pointer-events: none;
}

/* Color variations */
.color-primary .metric-icon {
  background: rgba(66, 185, 131, 0.1);
  color: var(--color-primary);
}

.color-primary:hover {
  border-color: var(--color-primary);
}

.color-primary .metric-glow {
  background: radial-gradient(circle, rgba(66, 185, 131, 0.2) 0%, transparent 70%);
}

.color-orange .metric-icon {
  background: rgba(251, 191, 36, 0.1);
  color: #fbbf24;
}

.color-orange:hover {
  border-color: #fbbf24;
}

.color-orange .metric-glow {
  background: radial-gradient(circle, rgba(251, 191, 36, 0.2) 0%, transparent 70%);
}

.color-blue .metric-icon {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.color-blue:hover {
  border-color: #3b82f6;
}

.color-blue .metric-glow {
  background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, transparent 70%);
}

.color-purple .metric-icon {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

.color-purple:hover {
  border-color: #8b5cf6;
}

.color-purple .metric-glow {
  background: radial-gradient(circle, rgba(139, 92, 246, 0.2) 0%, transparent 70%);
}

@media (max-width: 768px) {
  .metric-card {
    width: 100%;
  }
}
</style> 