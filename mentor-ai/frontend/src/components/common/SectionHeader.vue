<template>
  <div class="section-header">
    <div class="header-icon-wrapper" v-if="icon">
      <font-awesome-icon :icon="`fa-solid ${icon}`" class="header-icon" />
      <div class="icon-glow"></div>
    </div>
    <div class="header-text">
      <h2 class="section-title">{{ title }}</h2>
      <p class="section-subtitle" v-if="subtitle">{{ subtitle }}</p>
    </div>
    <div class="header-action" v-if="$slots.action">
      <slot name="action"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SectionHeader',
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      required: true
    },
    subtitle: {
      type: String,
      default: ''
    }
  }
};
</script>

<style scoped>
.section-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.header-icon-wrapper {
  position: relative;
  width: 48px;
  height: 48px;
}

.header-icon {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--color-primary);
  z-index: 2;
}

.icon-glow {
  position: absolute;
  inset: -8px;
  background: radial-gradient(circle, rgba(66, 185, 131, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: iconGlow 3s ease-in-out infinite;
}

@keyframes iconGlow {
  0%, 100% { transform: scale(0.8); opacity: 0.3; }
  50% { transform: scale(1.2); opacity: 0.6; }
}

.header-text {
  flex: 1;
}

.section-title {
  margin: 0 0 0.3rem 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--color-text);
}

.section-subtitle {
  margin: 0;
  font-size: 1rem;
  color: var(--color-text-secondary);
}

.header-action {
  margin-left: auto;
}

@media (max-width: 768px) {
  .section-header {
    flex-wrap: wrap;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
  
  .section-subtitle {
    font-size: 0.9rem;
  }
}
</style> 