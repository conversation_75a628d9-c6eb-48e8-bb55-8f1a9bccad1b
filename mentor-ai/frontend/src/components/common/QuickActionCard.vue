<template>
  <router-link :to="link" class="quick-action-card" :class="`color-${color}`">
    <div class="icon-container">
      <font-awesome-icon :icon="`fa-solid ${icon}`" class="icon" />
      <div class="icon-bg-glow"></div>
    </div>
    <h3>{{ title }}</h3>
    <p>{{ description }}</p>
    <div class="card-arrow">
      <font-awesome-icon icon="fa-solid fa-arrow-right" />
    </div>
  </router-link>
</template>

<script>
export default {
  name: 'QuickActionCard',
  props: {
    link: {
      type: String,
      required: true
    },
    icon: {
      type: String,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: true
    },
    color: {
      type: String,
      default: 'primary'
    }
  }
};
</script>

<style scoped>
.quick-action-card {
  background: var(--color-bg-secondary);
  padding: 1.8rem;
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  color: var(--color-text);
  border: 1px solid var(--color-border);
  position: relative;
  overflow: hidden;
  height: 100%;
  min-height: 280px;
}

.quick-action-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.icon-container {
  width: 70px;
  height: 70px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
}

.icon-bg-glow {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.quick-action-card:hover .icon-bg-glow {
  opacity: 0.3;
}

.quick-action-card:hover .icon-container {
  transform: scale(1.1);
}

.icon {
  font-size: 2rem;
  transition: all 0.3s ease;
  z-index: 2;
}

.quick-action-card h3 {
  margin: 0 0 0.8rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  transition: color 0.3s ease;
}

.quick-action-card p {
  color: var(--color-text-secondary);
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
}

.card-arrow {
  position: absolute;
  bottom: 1.2rem;
  right: 1.2rem;
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.3s ease;
  font-size: 1.1rem;
}

.quick-action-card:hover .card-arrow {
  opacity: 1;
  transform: translateX(0);
}

/* Color variations */
.color-primary .icon-container {
  background: rgba(66, 185, 131, 0.1);
}

.color-primary .icon {
  color: var(--color-primary);
}

.color-primary:hover {
  border-color: var(--color-primary);
}

.color-primary .card-arrow {
  color: var(--color-primary);
}

.color-purple .icon-container {
  background: rgba(139, 92, 246, 0.1);
}

.color-purple .icon {
  color: #8b5cf6;
}

.color-purple:hover {
  border-color: #8b5cf6;
}

.color-purple .card-arrow {
  color: #8b5cf6;
}

.color-blue .icon-container {
  background: rgba(59, 130, 246, 0.1);
}

.color-blue .icon {
  color: #3b82f6;
}

.color-blue:hover {
  border-color: #3b82f6;
}

.color-blue .card-arrow {
  color: #3b82f6;
}

.color-green .icon-container {
  background: rgba(16, 185, 129, 0.1);
}

.color-green .icon {
  color: #10b981;
}

.color-green:hover {
  border-color: #10b981;
}

.color-green .card-arrow {
  color: #10b981;
}

.color-orange .icon-container {
  background: rgba(251, 191, 36, 0.1);
}

.color-orange .icon {
  color: #fbbf24;
}

.color-orange:hover {
  border-color: #fbbf24;
}

.color-orange .card-arrow {
  color: #fbbf24;
}

.color-teal .icon-container {
  background: rgba(20, 184, 166, 0.1);
}

.color-teal .icon {
  color: #14b8a6;
}

.color-teal:hover {
  border-color: #14b8a6;
}

.color-teal .card-arrow {
  color: #14b8a6;
}

.color-indigo .icon-container {
  background: rgba(99, 102, 241, 0.1);
}

.color-indigo .icon {
  color: #6366f1;
}

.color-indigo:hover {
  border-color: #6366f1;
}

.color-indigo .card-arrow {
  color: #6366f1;
}

/* Animation effect */
.quick-action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    transparent 0%,
    rgba(255, 255, 255, 0.05) 50%,
    transparent 100%
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.quick-action-card:hover::before {
  transform: translateX(100%);
}

@media (max-width: 768px) {
  .quick-action-card {
    min-height: 220px;
  }
}
</style> 