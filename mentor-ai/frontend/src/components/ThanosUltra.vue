<template>
  <div class="thanos-ultra">
    <!-- Header -->
    <div class="thanos-header">
      <div class="header-content">
        <div class="header-left">
          <div class="thanos-logo">
            <i class="fas fa-infinity"></i>
            <h1>Thanos Ultra</h1>
          </div>
          <span class="version-badge">v2.0</span>
        </div>
        
        <div class="header-right">
          <button class="icon-btn" @click="toggleTheme" :title="`Modo ${isDarkMode ? 'Claro' : 'Escuro'}`">
            <i :class="`fas fa-${isDarkMode ? 'sun' : 'moon'}`"></i>
          </button>
          <button class="icon-btn" @click="showSettings = true" title="Configurações">
            <i class="fas fa-cog"></i>
          </button>
          <button class="icon-btn" @click="showAnalytics = true" title="Analytics">
            <i class="fas fa-chart-line"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="thanos-main">
      <!-- Sidebar -->
      <transition name="slide">
        <div v-if="showSidebar" class="thanos-sidebar">
          <div class="sidebar-header">
            <h3>Documentos</h3>
            <button class="icon-btn" @click="showUploadModal = true">
              <i class="fas fa-plus"></i>
            </button>
          </div>
          
          <!-- Search -->
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Buscar documentos..."
              @input="searchDocuments"
            />
          </div>
          
          <!-- Document List -->
          <div class="document-list">
            <div
              v-for="doc in filteredDocuments"
              :key="doc.id"
              :class="['document-item', { active: selectedDocument?.id === doc.id }]"
              @click="selectDocument(doc)"
            >
              <div class="doc-icon">
                <i :class="getDocumentIcon(doc.document_type)"></i>
              </div>
              <div class="doc-info">
                <h4>{{ doc.title }}</h4>
                <span class="doc-meta">
                  {{ doc.document_type }} • {{ formatDate(doc.created_at) }}
                </span>
                <div v-if="doc.tags?.length" class="doc-tags">
                  <span v-for="tag in doc.tags" :key="tag" class="tag">{{ tag }}</span>
                </div>
              </div>
              <button class="doc-menu" @click.stop="showDocMenu(doc)">
                <i class="fas fa-ellipsis-v"></i>
              </button>
            </div>
          </div>
        </div>
      </transition>
      
      <!-- Toggle Sidebar Button -->
      <button class="sidebar-toggle" @click="showSidebar = !showSidebar">
        <i :class="`fas fa-chevron-${showSidebar ? 'left' : 'right'}`"></i>
      </button>

      <!-- Chat Area -->
      <div class="thanos-chat">
        <!-- Document Header -->
        <div v-if="selectedDocument" class="document-header">
          <div class="doc-header-info">
            <i :class="getDocumentIcon(selectedDocument.document_type)"></i>
            <div>
              <h3>{{ selectedDocument.title }}</h3>
              <p>{{ selectedDocument.summary || 'Processando resumo...' }}</p>
            </div>
          </div>
          <div class="doc-header-actions">
            <button class="action-btn" @click="showDocumentInfo = true">
              <i class="fas fa-info-circle"></i>
              Info
            </button>
            <button class="action-btn" @click="downloadDocument">
              <i class="fas fa-download"></i>
              Baixar
            </button>
          </div>
        </div>

        <!-- Messages -->
        <div ref="messagesContainer" class="messages-container">
          <transition-group name="message" tag="div" class="messages">
            <!-- Welcome Message -->
            <div v-if="!messages.length && !selectedDocument" class="welcome-message">
              <div class="welcome-icon">
                <i class="fas fa-infinity"></i>
              </div>
              <h2>Bem-vindo ao Thanos Ultra</h2>
              <p>Faça upload de um documento ou selecione um existente para começar.</p>
              <div class="quick-actions">
                <button class="primary-btn" @click="showUploadModal = true">
                  <i class="fas fa-upload"></i>
                  Upload de Documento
                </button>
                <button class="secondary-btn" @click="showUrlModal = true">
                  <i class="fas fa-link"></i>
                  Processar URL
                </button>
              </div>
            </div>

            <!-- Chat Messages -->
            <div
              v-for="(message, index) in messages"
              :key="`msg-${index}`"
              :class="['message', message.role]"
            >
              <div class="message-avatar">
                <i :class="message.role === 'user' ? 'fas fa-user' : 'fas fa-infinity'"></i>
              </div>
              <div class="message-content">
                <div class="message-header">
                  <span class="message-role">{{ message.role === 'user' ? 'Você' : 'Thanos' }}</span>
                  <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                </div>
                <div class="message-text" v-html="formatMessage(message.content)"></div>
                <div v-if="message.chunks_used" class="message-meta">
                  <i class="fas fa-database"></i>
                  {{ message.chunks_used }} trechos relevantes utilizados
                </div>
              </div>
            </div>

            <!-- Typing Indicator -->
            <div v-if="isTyping" class="message assistant typing">
              <div class="message-avatar">
                <i class="fas fa-infinity"></i>
              </div>
              <div class="message-content">
                <div class="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
          </transition-group>
        </div>

        <!-- Input Area -->
        <div class="input-area">
          <div class="input-options">
            <label class="checkbox-label">
              <input type="checkbox" v-model="useRAG" />
              <span>Usar RAG</span>
            </label>
            <label class="checkbox-label">
              <input type="checkbox" v-model="streamingEnabled" />
              <span>Streaming</span>
            </label>
          </div>
          <div class="input-container">
            <textarea
              v-model="userInput"
              @keydown.enter.prevent="handleEnter"
              placeholder="Digite sua pergunta..."
              :disabled="isTyping"
              rows="1"
              ref="inputTextarea"
            ></textarea>
            <button
              @click="sendMessage"
              :disabled="!userInput.trim() || isTyping"
              class="send-btn"
            >
              <i class="fas fa-paper-plane"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Upload Modal -->
    <transition name="modal">
      <div v-if="showUploadModal" class="modal-overlay" @click.self="showUploadModal = false">
        <div class="modal">
          <div class="modal-header">
            <h3>Upload de Documento</h3>
            <button class="close-btn" @click="showUploadModal = false">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <div class="modal-body">
            <!-- File Drop Zone -->
            <div
              :class="['drop-zone', { dragging: isDragging }]"
              @drop="handleDrop"
              @dragover.prevent="isDragging = true"
              @dragleave="isDragging = false"
            >
              <i class="fas fa-cloud-upload-alt"></i>
              <p>Arraste um arquivo aqui ou clique para selecionar</p>
              <input
                type="file"
                ref="fileInput"
                @change="handleFileSelect"
                accept=".pdf,.txt,.csv,.png,.jpg,.jpeg"
                style="display: none"
              />
              <button class="select-file-btn" @click="$refs.fileInput.click()">
                Selecionar Arquivo
              </button>
            </div>

            <!-- Selected File -->
            <div v-if="selectedFile" class="selected-file">
              <i :class="getFileIcon(selectedFile.type)"></i>
              <div class="file-info">
                <p>{{ selectedFile.name }}</p>
                <span>{{ formatFileSize(selectedFile.size) }}</span>
              </div>
              <button @click="selectedFile = null" class="remove-file">
                <i class="fas fa-times"></i>
              </button>
            </div>

            <!-- Options -->
            <div class="upload-options">
              <div class="option-group">
                <label>Tags (separadas por vírgula)</label>
                <input v-model="uploadTags" type="text" placeholder="estudo, revisão, aprendizado" />
              </div>
              
              <div class="option-group">
                <label class="checkbox-label">
                  <input type="checkbox" v-model="uploadOptions.extractImages" />
                  <span>Extrair imagens (PDF)</span>
                </label>
                <label class="checkbox-label">
                  <input type="checkbox" v-model="uploadOptions.generateSummary" />
                  <span>Gerar resumo automático</span>
                </label>
                <label class="checkbox-label">
                  <input type="checkbox" v-model="uploadOptions.autoChunk" />
                  <span>Chunking inteligente</span>
                </label>
              </div>
            </div>
          </div>
          
          <div class="modal-footer">
            <button class="cancel-btn" @click="showUploadModal = false">Cancelar</button>
            <button
              class="primary-btn"
              @click="uploadDocument"
              :disabled="!selectedFile || uploading"
            >
              <i v-if="uploading" class="fas fa-spinner fa-spin"></i>
              {{ uploading ? 'Enviando...' : 'Enviar' }}
            </button>
          </div>
        </div>
      </div>
    </transition>

    <!-- URL Modal -->
    <transition name="modal">
      <div v-if="showUrlModal" class="modal-overlay" @click.self="showUrlModal = false">
        <div class="modal">
          <div class="modal-header">
            <h3>Processar URL</h3>
            <button class="close-btn" @click="showUrlModal = false">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <div class="modal-body">
            <div class="url-input-group">
              <label>URL do Site ou YouTube</label>
              <input
                v-model="urlInput"
                type="url"
                placeholder="https://exemplo.com ou https://youtube.com/watch?v=..."
                @input="detectUrlType"
              />
              <div v-if="detectedUrlType" class="url-type">
                <i :class="detectedUrlType === 'youtube' ? 'fab fa-youtube' : 'fas fa-globe'"></i>
                {{ detectedUrlType === 'youtube' ? 'Vídeo do YouTube' : 'Website' }}
              </div>
            </div>
            
            <div class="url-options">
              <label>Tags</label>
              <input v-model="urlTags" type="text" placeholder="Tags separadas por vírgula" />
            </div>
          </div>
          
          <div class="modal-footer">
            <button class="cancel-btn" @click="showUrlModal = false">Cancelar</button>
            <button
              class="primary-btn"
              @click="processUrl"
              :disabled="!urlInput || processingUrl"
            >
              <i v-if="processingUrl" class="fas fa-spinner fa-spin"></i>
              {{ processingUrl ? 'Processando...' : 'Processar' }}
            </button>
          </div>
        </div>
      </div>
    </transition>

    <!-- Settings Modal -->
    <transition name="modal">
      <div v-if="showSettings" class="modal-overlay" @click.self="showSettings = false">
        <div class="modal modal-large">
          <div class="modal-header">
            <h3>Configurações</h3>
            <button class="close-btn" @click="showSettings = false">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <div class="modal-body">
            <div class="settings-tabs">
              <button
                v-for="tab in settingsTabs"
                :key="tab.id"
                :class="['tab', { active: activeSettingsTab === tab.id }]"
                @click="activeSettingsTab = tab.id"
              >
                <i :class="tab.icon"></i>
                {{ tab.label }}
              </button>
            </div>
            
            <!-- AI Provider Settings -->
            <div v-if="activeSettingsTab === 'ai'" class="settings-content">
              <div class="setting-group">
                <label>Provedor de IA</label>
                <select v-model="aiSettings.provider">
                  <option value="openai">OpenAI</option>
                  <option value="anthropic">Anthropic</option>
                  <option value="groq">Groq</option>
                </select>
              </div>
              
              <div class="setting-group">
                <label>Modelo</label>
                <select v-model="aiSettings.model">
                  <option v-for="model in getModelsForProvider()" :key="model" :value="model">
                    {{ model }}
                  </option>
                </select>
              </div>
              
              <div class="setting-group">
                <label>API Key</label>
                <input
                  v-model="aiSettings.apiKey"
                  type="password"
                  placeholder="sk-..."
                />
              </div>
              
              <div class="setting-group">
                <label>Temperatura ({{ aiSettings.temperature }})</label>
                <input
                  v-model="aiSettings.temperature"
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                />
              </div>
              
              <div class="setting-group">
                <label>Max Tokens</label>
                <input
                  v-model.number="aiSettings.maxTokens"
                  type="number"
                  min="100"
                  max="4000"
                />
              </div>
            </div>
            
            <!-- Appearance Settings -->
            <div v-if="activeSettingsTab === 'appearance'" class="settings-content">
              <div class="setting-group">
                <label>Tema</label>
                <div class="theme-options">
                  <button
                    :class="['theme-option', { active: !isDarkMode }]"
                    @click="isDarkMode = false"
                  >
                    <i class="fas fa-sun"></i>
                    Claro
                  </button>
                  <button
                    :class="['theme-option', { active: isDarkMode }]"
                    @click="isDarkMode = true"
                  >
                    <i class="fas fa-moon"></i>
                    Escuro
                  </button>
                </div>
              </div>
              
              <div class="setting-group">
                <label>Tamanho da Fonte</label>
                <select v-model="fontSize">
                  <option value="small">Pequena</option>
                  <option value="medium">Média</option>
                  <option value="large">Grande</option>
                </select>
              </div>
              
              <div class="setting-group">
                <label class="checkbox-label">
                  <input type="checkbox" v-model="showTimestamps" />
                  <span>Mostrar horários nas mensagens</span>
                </label>
              </div>
            </div>
            
            <!-- Advanced Settings -->
            <div v-if="activeSettingsTab === 'advanced'" class="settings-content">
              <div class="setting-group">
                <label>Tamanho do Chunk</label>
                <input
                  v-model.number="advancedSettings.chunkSize"
                  type="number"
                  min="100"
                  max="2000"
                />
              </div>
              
              <div class="setting-group">
                <label>Overlap do Chunk</label>
                <input
                  v-model.number="advancedSettings.chunkOverlap"
                  type="number"
                  min="0"
                  max="500"
                />
              </div>
              
              <div class="setting-group">
                <label>Top K (Busca Semântica)</label>
                <input
                  v-model.number="advancedSettings.topK"
                  type="number"
                  min="1"
                  max="10"
                />
              </div>
              
              <div class="setting-group">
                <label>Threshold de Similaridade</label>
                <input
                  v-model.number="advancedSettings.similarityThreshold"
                  type="number"
                  min="0"
                  max="1"
                  step="0.1"
                />
              </div>
            </div>
          </div>
          
          <div class="modal-footer">
            <button class="cancel-btn" @click="showSettings = false">Fechar</button>
            <button class="primary-btn" @click="saveSettings">
              Salvar Configurações
            </button>
          </div>
        </div>
      </div>
    </transition>

    <!-- Analytics Modal -->
    <transition name="modal">
      <div v-if="showAnalytics" class="modal-overlay" @click.self="showAnalytics = false">
        <div class="modal modal-large">
          <div class="modal-header">
            <h3>Analytics</h3>
            <button class="close-btn" @click="showAnalytics = false">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <div class="modal-body">
            <div class="analytics-grid">
              <div class="stat-card">
                <i class="fas fa-comments"></i>
                <div class="stat-info">
                  <h4>Total de Mensagens</h4>
                  <p>{{ analytics.totalMessages || 0 }}</p>
                </div>
              </div>
              
              <div class="stat-card">
                <i class="fas fa-coins"></i>
                <div class="stat-info">
                  <h4>Tokens Utilizados</h4>
                  <p>{{ analytics.totalTokens || 0 }}</p>
                </div>
              </div>
              
              <div class="stat-card">
                <i class="fas fa-clock"></i>
                <div class="stat-info">
                  <h4>Tempo Médio de Resposta</h4>
                  <p>{{ analytics.avgResponseTime || 0 }}s</p>
                </div>
              </div>
              
              <div class="stat-card">
                <i class="fas fa-file-alt"></i>
                <div class="stat-info">
                  <h4>Documentos Processados</h4>
                  <p>{{ analytics.totalDocuments || 0 }}</p>
                </div>
              </div>
            </div>
            
            <!-- Usage Chart -->
            <div class="chart-container">
              <h4>Uso por Provedor</h4>
              <div class="usage-bars">
                <div
                  v-for="(count, provider) in analytics.usageByProvider"
                  :key="provider"
                  class="usage-bar"
                >
                  <span class="provider-name">{{ provider }}</span>
                  <div class="bar-wrapper">
                    <div
                      class="bar"
                      :style="{ width: `${(count / analytics.totalSessions) * 100}%` }"
                    ></div>
                  </div>
                  <span class="count">{{ count }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>

    <!-- Toast Notifications -->
    <transition-group name="toast" tag="div" class="toast-container">
      <div
        v-for="toast in toasts"
        :key="toast.id"
        :class="['toast', toast.type]"
        @click="removeToast(toast.id)"
      >
        <i :class="getToastIcon(toast.type)"></i>
        <span>{{ toast.message }}</span>
      </div>
    </transition-group>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import axios from 'axios'
import { marked } from 'marked'

export default {
  name: 'ThanosUltra',
  
  setup() {
    // State
    const isDarkMode = ref(true)
    const showSidebar = ref(true)
    const showUploadModal = ref(false)
    const showUrlModal = ref(false)
    const showSettings = ref(false)
    const showAnalytics = ref(false)
    const showDocumentInfo = ref(false)
    
    // Documents
    const documents = ref([])
    const selectedDocument = ref(null)
    const searchQuery = ref('')
    const filteredDocuments = ref([])
    
    // Chat
    const messages = ref([])
    const userInput = ref('')
    const isTyping = ref(false)
    const sessionId = ref(null)
    const useRAG = ref(true)
    const streamingEnabled = ref(true)
    
    // Upload
    const selectedFile = ref(null)
    const uploading = ref(false)
    const uploadTags = ref('')
    const uploadOptions = reactive({
      extractImages: false,
      generateSummary: true,
      autoChunk: true
    })
    const isDragging = ref(false)
    
    // URL Processing
    const urlInput = ref('')
    const urlTags = ref('')
    const processingUrl = ref(false)
    const detectedUrlType = ref('')
    
    // Settings
    const activeSettingsTab = ref('ai')
    const settingsTabs = [
      { id: 'ai', label: 'IA', icon: 'fas fa-robot' },
      { id: 'appearance', label: 'Aparência', icon: 'fas fa-palette' },
      { id: 'advanced', label: 'Avançado', icon: 'fas fa-cogs' }
    ]
    
    const aiSettings = reactive({
      provider: 'openai',
      model: 'gpt-4o-mini',
      apiKey: '',
      temperature: 0.7,
      maxTokens: 2000
    })
    
    const fontSize = ref('medium')
    const showTimestamps = ref(true)
    
    const advancedSettings = reactive({
      chunkSize: 1000,
      chunkOverlap: 200,
      topK: 5,
      similarityThreshold: 0.5
    })
    
    // Analytics
    const analytics = reactive({
      totalMessages: 0,
      totalTokens: 0,
      avgResponseTime: 0,
      totalDocuments: 0,
      totalSessions: 0,
      usageByProvider: {}
    })
    
    // Toast notifications
    const toasts = ref([])
    
    // WebSocket
    let ws = null
    
    // Refs
    const messagesContainer = ref(null)
    const inputTextarea = ref(null)
    const fileInput = ref(null)
    
    // API Configuration
    const API_BASE_URL = 'http://localhost:8001/api/thanos'
    
    // Methods
    const toggleTheme = () => {
      isDarkMode.value = !isDarkMode.value
      document.body.classList.toggle('dark', isDarkMode.value)
    }
    
    const getDocumentIcon = (type) => {
      const icons = {
        pdf: 'fas fa-file-pdf',
        txt: 'fas fa-file-alt',
        csv: 'fas fa-file-csv',
        image: 'fas fa-file-image',
        youtube: 'fab fa-youtube',
        site: 'fas fa-globe'
      }
      return icons[type] || 'fas fa-file'
    }
    
    const getFileIcon = (type) => {
      if (type.includes('pdf')) return 'fas fa-file-pdf'
      if (type.includes('text')) return 'fas fa-file-alt'
      if (type.includes('csv')) return 'fas fa-file-csv'
      if (type.includes('image')) return 'fas fa-file-image'
      return 'fas fa-file'
    }
    
    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('pt-BR')
    }
    
    const formatTime = (timestamp) => {
      if (!showTimestamps.value) return ''
      return new Date(timestamp).toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    const formatFileSize = (bytes) => {
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      if (bytes === 0) return '0 Bytes'
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
    }
    
    const formatMessage = (content) => {
      return marked(content)
    }
    
    const getToastIcon = (type) => {
      const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
      }
      return icons[type] || icons.info
    }
    
    const showToast = (message, type = 'info') => {
      const id = Date.now()
      toasts.value.push({ id, message, type })
      setTimeout(() => removeToast(id), 5000)
    }
    
    const removeToast = (id) => {
      toasts.value = toasts.value.filter(t => t.id !== id)
    }
    
    const getModelsForProvider = () => {
      const models = {
        openai: ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo'],
        anthropic: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
        groq: ['llama-3.1-70b-versatile', 'gemma2-9b-it', 'mixtral-8x7b-32768']
      }
      return models[aiSettings.provider] || []
    }
    
    // Document handling
    const loadDocuments = async () => {
      try {
        const response = await axios.get(`${API_BASE_URL}/documents`)
        documents.value = response.data
        filteredDocuments.value = documents.value
      } catch (error) {
        console.error('Error loading documents:', error)
      }
    }
    
    const searchDocuments = () => {
      if (!searchQuery.value) {
        filteredDocuments.value = documents.value
        return
      }
      
      const query = searchQuery.value.toLowerCase()
      filteredDocuments.value = documents.value.filter(doc =>
        doc.title.toLowerCase().includes(query) ||
        doc.tags?.some(tag => tag.toLowerCase().includes(query))
      )
    }
    
    const selectDocument = async (doc) => {
      selectedDocument.value = doc
      messages.value = []
      
      // Initialize session
      try {
        const formData = new FormData()
        formData.append('provider', aiSettings.provider)
        formData.append('model', aiSettings.model)
        formData.append('api_key', aiSettings.apiKey || 'dummy_api_key')
        formData.append('document_id', doc.id)
        formData.append('language', 'pt')
        formData.append('use_rag', useRAG.value)
        formData.append('temperature', aiSettings.temperature)
        formData.append('max_tokens', aiSettings.maxTokens)
        formData.append('streaming_enabled', streamingEnabled.value)
        
        const response = await axios.post(`${API_BASE_URL}/initialize`, formData)
        sessionId.value = response.data.session_id
        
        // Connect WebSocket if streaming enabled
        if (streamingEnabled.value && response.data.websocket_url) {
          connectWebSocket()
        }
        
        showToast('Sessão iniciada com sucesso', 'success')
      } catch (error) {
        console.error('Error initializing session:', error)
        showToast('Erro ao iniciar sessão', 'error')
      }
    }
    
    // File handling
    const handleDrop = (e) => {
      e.preventDefault()
      isDragging.value = false
      const files = e.dataTransfer.files
      if (files.length > 0) {
        selectedFile.value = files[0]
      }
    }
    
    const handleFileSelect = (e) => {
      const files = e.target.files
      if (files.length > 0) {
        selectedFile.value = files[0]
      }
    }
    
    const uploadDocument = async () => {
      if (!selectedFile.value) return
      
      uploading.value = true
      const formData = new FormData()
      formData.append('file', selectedFile.value)
      formData.append('document_type', getDocumentType(selectedFile.value))
      formData.append('options', JSON.stringify(uploadOptions))
      formData.append('tags', JSON.stringify(uploadTags.value.split(',').map(t => t.trim()).filter(t => t)))
      formData.append('extract_images', uploadOptions.extractImages)
      formData.append('generate_summary', uploadOptions.generateSummary)
      formData.append('auto_chunk', uploadOptions.autoChunk)
      
      try {
        const response = await axios.post(`${API_BASE_URL}/documents/upload`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        })
        
        documents.value.unshift(response.data)
        filteredDocuments.value = documents.value
        selectedDocument.value = response.data
        
        showUploadModal.value = false
        selectedFile.value = null
        uploadTags.value = ''
        
        showToast('Documento enviado com sucesso!', 'success')
        
        // Auto-select the uploaded document
        await selectDocument(response.data)
      } catch (error) {
        console.error('Upload error:', error)
        showToast('Erro ao enviar documento', 'error')
      } finally {
        uploading.value = false
      }
    }
    
    const getDocumentType = (file) => {
      const type = file.type
      if (type.includes('pdf')) return 'pdf'
      if (type.includes('text')) return 'txt'
      if (type.includes('csv')) return 'csv'
      if (type.includes('image')) return 'image'
      return 'txt'
    }
    
    // URL processing
    const detectUrlType = () => {
      if (urlInput.value.includes('youtube.com') || urlInput.value.includes('youtu.be')) {
        detectedUrlType.value = 'youtube'
      } else if (urlInput.value.startsWith('http')) {
        detectedUrlType.value = 'site'
      } else {
        detectedUrlType.value = ''
      }
    }
    
    const processUrl = async () => {
      if (!urlInput.value) return
      
      processingUrl.value = true
      
      try {
        const response = await axios.post(`${API_BASE_URL}/documents/url`, null, {
          params: {
            url: urlInput.value,
            document_type: detectedUrlType.value || 'site',
            tags: urlTags.value.split(',').map(t => t.trim()).filter(t => t)
          }
        })
        
        documents.value.unshift(response.data)
        filteredDocuments.value = documents.value
        
        showUrlModal.value = false
        urlInput.value = ''
        urlTags.value = ''
        
        showToast('URL processada com sucesso!', 'success')
        
        // Auto-select the processed document
        await selectDocument(response.data)
      } catch (error) {
        console.error('URL processing error:', error)
        showToast('Erro ao processar URL', 'error')
      } finally {
        processingUrl.value = false
      }
    }
    
    // Chat functionality
    const sendMessage = async () => {
      if (!userInput.value.trim() || !sessionId.value) return
      
      const message = userInput.value
      userInput.value = ''
      
      // Add user message
      messages.value.push({
        role: 'user',
        content: message,
        timestamp: new Date()
      })
      
      isTyping.value = true
      
      // Auto-scroll
      await nextTick()
      scrollToBottom()
      
      try {
        if (streamingEnabled.value && ws && ws.readyState === WebSocket.OPEN) {
          // Send via WebSocket
          ws.send(JSON.stringify({
            type: 'message',
            message: message
          }))
        } else {
          // Send via HTTP
          const response = await axios.post(
            `${API_BASE_URL}/conversations/${sessionId.value}/message`,
            {
              message: message,
              use_rag: useRAG.value,
              stream: false,
              language: 'pt',
              temperature: aiSettings.temperature,
              max_tokens: aiSettings.maxTokens
            }
          )
          
          messages.value.push({
            role: 'assistant',
            content: response.data.response,
            timestamp: new Date(),
            chunks_used: response.data.chunks_used
          })
        }
      } catch (error) {
        console.error('Error sending message:', error)
        showToast('Erro ao enviar mensagem', 'error')
      } finally {
        isTyping.value = false
        await nextTick()
        scrollToBottom()
      }
    }
    
    const handleEnter = (e) => {
      if (e.shiftKey) {
        // Allow line break with Shift+Enter
        return
      }
      sendMessage()
    }
    
    const scrollToBottom = () => {
      if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
      }
    }
    
    // WebSocket
    const connectWebSocket = () => {
      if (!sessionId.value) return
      
      const wsUrl = `ws://localhost:8001/api/thanos/ws/${sessionId.value}`
      ws = new WebSocket(wsUrl)
      
      ws.onopen = () => {
        console.log('WebSocket connected')
      }
      
      ws.onmessage = (event) => {
        const data = JSON.parse(event.data)
        
        if (data.type === 'chunk') {
          // Handle streaming chunk
          if (!messages.value.length || messages.value[messages.value.length - 1].role !== 'assistant') {
            messages.value.push({
              role: 'assistant',
              content: data.chunk,
              timestamp: new Date()
            })
          } else {
            messages.value[messages.value.length - 1].content += data.chunk
          }
          scrollToBottom()
        } else if (data.type === 'complete') {
          isTyping.value = false
          messages.value[messages.value.length - 1].tokens = data.tokens
        } else if (data.error) {
          showToast(data.error, 'error')
          isTyping.value = false
        }
      }
      
      ws.onerror = (error) => {
        console.error('WebSocket error:', error)
        showToast('Erro na conexão WebSocket', 'error')
      }
      
      ws.onclose = () => {
        console.log('WebSocket disconnected')
      }
    }
    
    // Settings
    const saveSettings = () => {
      localStorage.setItem('thanos-settings', JSON.stringify({
        ai: aiSettings,
        appearance: {
          isDarkMode: isDarkMode.value,
          fontSize: fontSize.value,
          showTimestamps: showTimestamps.value
        },
        advanced: advancedSettings
      }))
      
      showToast('Configurações salvas', 'success')
      showSettings.value = false
    }
    
    const loadSettings = () => {
      const saved = localStorage.getItem('thanos-settings')
      if (saved) {
        const settings = JSON.parse(saved)
        Object.assign(aiSettings, settings.ai || {})
        isDarkMode.value = settings.appearance?.isDarkMode ?? true
        fontSize.value = settings.appearance?.fontSize || 'medium'
        showTimestamps.value = settings.appearance?.showTimestamps ?? true
        Object.assign(advancedSettings, settings.advanced || {})
      }
    }
    
    // Analytics
    const loadAnalytics = async () => {
      try {
        const response = await axios.get(`${API_BASE_URL}/analytics`)
        Object.assign(analytics, response.data)
      } catch (error) {
        console.error('Error loading analytics:', error)
      }
    }
    
    // Document actions
    const downloadDocument = async () => {
      if (!selectedDocument.value) return
      
      try {
        // Implementation depends on backend
        showToast('Download iniciado', 'info')
      } catch (error) {
        console.error('Download error:', error)
        showToast('Erro ao baixar documento', 'error')
      }
    }
    
    const showDocMenu = (doc) => {
      // Implement document context menu
      console.log('Show menu for', doc)
    }
    
    // Lifecycle
    onMounted(() => {
      loadSettings()
      loadDocuments()
      loadAnalytics()
      document.body.classList.toggle('dark', isDarkMode.value)
      
      // Auto-resize textarea
      watch(userInput, () => {
        if (inputTextarea.value) {
          inputTextarea.value.style.height = 'auto'
          inputTextarea.value.style.height = inputTextarea.value.scrollHeight + 'px'
        }
      })
    })
    
    onUnmounted(() => {
      if (ws) {
        ws.close()
      }
    })
    
    return {
      // State
      isDarkMode,
      showSidebar,
      showUploadModal,
      showUrlModal,
      showSettings,
      showAnalytics,
      showDocumentInfo,
      
      // Documents
      documents,
      selectedDocument,
      searchQuery,
      filteredDocuments,
      
      // Chat
      messages,
      userInput,
      isTyping,
      sessionId,
      useRAG,
      streamingEnabled,
      
      // Upload
      selectedFile,
      uploading,
      uploadTags,
      uploadOptions,
      isDragging,
      
      // URL
      urlInput,
      urlTags,
      processingUrl,
      detectedUrlType,
      
      // Settings
      activeSettingsTab,
      settingsTabs,
      aiSettings,
      fontSize,
      showTimestamps,
      advancedSettings,
      
      // Analytics
      analytics,
      
      // Toast
      toasts,
      
      // Refs
      messagesContainer,
      inputTextarea,
      fileInput,
      
      // Methods
      toggleTheme,
      getDocumentIcon,
      getFileIcon,
      formatDate,
      formatTime,
      formatFileSize,
      formatMessage,
      getToastIcon,
      showToast,
      removeToast,
      getModelsForProvider,
      searchDocuments,
      selectDocument,
      handleDrop,
      handleFileSelect,
      uploadDocument,
      detectUrlType,
      processUrl,
      sendMessage,
      handleEnter,
      saveSettings,
      downloadDocument,
      showDocMenu
    }
  }
}
</script>

<style scoped>
/* CSS Variables */
:root {
  --primary: #6366f1;
  --primary-dark: #4f46e5;
  --secondary: #8b5cf6;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;
  
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --border: #e5e7eb;
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  --radius: 0.5rem;
  --radius-lg: 0.75rem;
  --transition: all 0.2s ease;
}

/* Dark mode variables */
:global(.dark) {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --border: #334155;
}

/* Base styles */
.thanos-ultra {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header */
.thanos-header {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border);
  padding: 1rem 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.thanos-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.thanos-logo i {
  font-size: 1.5rem;
  color: var(--primary);
}

.thanos-logo h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.version-badge {
  background: var(--primary);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.header-right {
  display: flex;
  gap: 0.5rem;
}

/* Icon buttons */
.icon-btn {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.icon-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Main layout */
.thanos-main {
  display: flex;
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* Sidebar */
.thanos-sidebar {
  width: 320px;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border);
}

.sidebar-header h3 {
  margin: 0;
  font-size: 1.125rem;
}

.search-box {
  position: relative;
  padding: 1rem 1.5rem;
}

.search-box i {
  position: absolute;
  left: 2.5rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
}

.search-box input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  background: var(--bg-tertiary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary);
}

/* Document list */
.document-list {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
}

.document-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  margin-bottom: 0.5rem;
  background: var(--bg-primary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  cursor: pointer;
  transition: var(--transition);
}

.document-item:hover {
  background: var(--bg-tertiary);
  border-color: var(--primary);
}

.document-item.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.document-item.active .doc-meta,
.document-item.active .tag {
  color: rgba(255, 255, 255, 0.8);
}

.doc-icon {
  width: 3rem;
  height: 3rem;
  background: var(--bg-tertiary);
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: var(--primary);
}

.document-item.active .doc-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.doc-info {
  flex: 1;
  min-width: 0;
}

.doc-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.doc-meta {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.doc-tags {
  display: flex;
  gap: 0.25rem;
  margin-top: 0.25rem;
  flex-wrap: wrap;
}

.tag {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  padding: 0.125rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.625rem;
}

.doc-menu {
  background: transparent;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 0.5rem;
}

/* Sidebar toggle */
.sidebar-toggle {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: var(--bg-secondary);
  border: 1px solid var(--border);
  border-left: none;
  border-radius: 0 var(--radius) var(--radius) 0;
  padding: 0.5rem;
  cursor: pointer;
  z-index: 10;
  transition: var(--transition);
}

.sidebar-toggle:hover {
  background: var(--bg-tertiary);
}

/* Chat area */
.thanos-chat {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.document-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border);
}

.doc-header-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.doc-header-info i {
  font-size: 2rem;
  color: var(--primary);
}

.doc-header-info h3 {
  margin: 0;
  font-size: 1.125rem;
}

.doc-header-info p {
  margin: 0.25rem 0 0 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.doc-header-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--bg-primary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  color: var(--text-primary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition);
}

.action-btn:hover {
  background: var(--bg-tertiary);
  border-color: var(--primary);
  color: var(--primary);
}

/* Messages */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

.messages {
  max-width: 900px;
  margin: 0 auto;
}

.welcome-message {
  text-align: center;
  padding: 4rem 2rem;
}

.welcome-icon {
  font-size: 5rem;
  color: var(--primary);
  margin-bottom: 2rem;
}

.welcome-message h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.welcome-message p {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.quick-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* Message styles */
.message {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  animation: messageSlide 0.3s ease;
}

@keyframes messageSlide {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-avatar {
  width: 2.5rem;
  height: 2.5rem;
  background: var(--bg-tertiary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.message.user .message-avatar {
  background: var(--primary);
  color: white;
}

.message.assistant .message-avatar {
  background: var(--secondary);
  color: white;
}

.message-content {
  flex: 1;
  background: var(--bg-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: 1rem 1.5rem;
}

.message.user .message-content {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.message-role {
  font-weight: 600;
  font-size: 0.875rem;
}

.message-time {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.message.user .message-time {
  color: rgba(255, 255, 255, 0.7);
}

.message-text {
  line-height: 1.6;
}

.message-text :deep(p) {
  margin: 0 0 0.5rem 0;
}

.message-text :deep(p:last-child) {
  margin-bottom: 0;
}

.message-text :deep(code) {
  background: var(--bg-tertiary);
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.message.user .message-text :deep(code) {
  background: rgba(255, 255, 255, 0.2);
}

.message-text :deep(pre) {
  background: var(--bg-tertiary);
  padding: 1rem;
  border-radius: var(--radius);
  overflow-x: auto;
  margin: 0.5rem 0;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.message.user .message-meta {
  color: rgba(255, 255, 255, 0.7);
}

/* Typing indicator */
.typing-indicator {
  display: flex;
  gap: 0.25rem;
  padding: 0.5rem 0;
}

.typing-indicator span {
  width: 0.5rem;
  height: 0.5rem;
  background: var(--text-tertiary);
  border-radius: 50%;
  animation: typing 1.4s ease-in-out infinite;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

/* Input area */
.input-area {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border);
  padding: 1.5rem;
}

.input-options {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.checkbox-label input[type="checkbox"] {
  cursor: pointer;
}

.input-container {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
}

.input-container textarea {
  flex: 1;
  padding: 0.75rem 1rem;
  background: var(--bg-primary);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: 1rem;
  resize: none;
  min-height: 44px;
  max-height: 120px;
  line-height: 1.5;
}

.input-container textarea:focus {
  outline: none;
  border-color: var(--primary);
}

.send-btn {
  width: 44px;
  height: 44px;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.send-btn:hover:not(:disabled) {
  background: var(--primary-dark);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Buttons */
.primary-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.primary-btn:hover:not(:disabled) {
  background: var(--primary-dark);
}

.primary-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.secondary-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.secondary-btn:hover {
  background: var(--bg-tertiary);
  border-color: var(--primary);
  color: var(--primary);
}

.cancel-btn {
  padding: 0.75rem 1.5rem;
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: none;
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.cancel-btn:hover {
  background: var(--bg-secondary);
}

/* Modals */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.modal-large {
  max-width: 800px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
}

.close-btn {
  background: transparent;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 0.5rem;
}

.close-btn:hover {
  color: var(--text-primary);
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid var(--border);
}

/* Drop zone */
.drop-zone {
  border: 2px dashed var(--border);
  border-radius: var(--radius-lg);
  padding: 3rem;
  text-align: center;
  transition: var(--transition);
}

.drop-zone.dragging {
  border-color: var(--primary);
  background: var(--bg-secondary);
}

.drop-zone i {
  font-size: 3rem;
  color: var(--text-tertiary);
  margin-bottom: 1rem;
}

.drop-zone p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.select-file-btn {
  padding: 0.5rem 1rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--transition);
}

.select-file-btn:hover {
  background: var(--bg-tertiary);
  border-color: var(--primary);
  color: var(--primary);
}

/* Selected file */
.selected-file {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  margin-top: 1rem;
}

.selected-file i {
  font-size: 1.5rem;
  color: var(--primary);
}

.file-info {
  flex: 1;
}

.file-info p {
  margin: 0;
  font-weight: 500;
}

.file-info span {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.remove-file {
  background: transparent;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 0.5rem;
}

.remove-file:hover {
  color: var(--error);
}

/* Upload options */
.upload-options {
  margin-top: 1.5rem;
}

.option-group {
  margin-bottom: 1rem;
}

.option-group label:not(.checkbox-label) {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.option-group input[type="text"] {
  width: 100%;
  padding: 0.5rem 1rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  color: var(--text-primary);
}

.option-group input[type="text"]:focus {
  outline: none;
  border-color: var(--primary);
}

/* URL input */
.url-input-group {
  margin-bottom: 1rem;
}

.url-input-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.url-input-group input {
  width: 100%;
  padding: 0.75rem 1rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.url-input-group input:focus {
  outline: none;
  border-color: var(--primary);
}

.url-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.url-type i {
  color: var(--primary);
}

/* Settings */
.settings-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
}

.tab:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.tab.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.setting-group select,
.setting-group input[type="number"],
.setting-group input[type="password"] {
  width: 100%;
  padding: 0.5rem 1rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  color: var(--text-primary);
}

.setting-group select:focus,
.setting-group input:focus {
  outline: none;
  border-color: var(--primary);
}

.setting-group input[type="range"] {
  width: 100%;
}

.theme-options {
  display: flex;
  gap: 0.5rem;
}

.theme-option {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  cursor: pointer;
  transition: var(--transition);
}

.theme-option:hover {
  background: var(--bg-tertiary);
}

.theme-option.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

/* Analytics */
.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
}

.stat-card i {
  font-size: 2rem;
  color: var(--primary);
}

.stat-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.stat-info p {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.chart-container {
  background: var(--bg-secondary);
  padding: 1.5rem;
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
}

.chart-container h4 {
  margin: 0 0 1rem 0;
}

.usage-bars {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.usage-bar {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.provider-name {
  width: 100px;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.bar-wrapper {
  flex: 1;
  height: 1.5rem;
  background: var(--bg-tertiary);
  border-radius: 0.75rem;
  overflow: hidden;
}

.bar {
  height: 100%;
  background: var(--primary);
  transition: width 0.5s ease;
}

.count {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

/* Toast notifications */
.toast-container {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 2000;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.toast {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: var(--bg-primary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  box-shadow: var(--shadow-lg);
  cursor: pointer;
  transition: var(--transition);
}

.toast:hover {
  transform: translateX(-0.25rem);
}

.toast i {
  font-size: 1.25rem;
}

.toast.success {
  border-color: var(--success);
}

.toast.success i {
  color: var(--success);
}

.toast.error {
  border-color: var(--error);
}

.toast.error i {
  color: var(--error);
}

.toast.warning {
  border-color: var(--warning);
}

.toast.warning i {
  color: var(--warning);
}

.toast.info {
  border-color: var(--info);
}

.toast.info i {
  color: var(--info);
}

/* Transitions */
.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(-100%);
}

.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-from {
  opacity: 0;
}

.modal-enter-active .modal,
.modal-leave-active .modal {
  transition: transform 0.3s ease;
}

.modal-enter-from .modal {
  transform: scale(0.9);
}

.modal-leave-to .modal {
  transform: scale(0.9);
}

.message-enter-active,
.message-leave-active {
  transition: all 0.3s ease;
}

.message-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.message-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}

.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

/* Responsive */
@media (max-width: 768px) {
  .thanos-sidebar {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 100;
    box-shadow: var(--shadow-lg);
  }
  
  .sidebar-toggle {
    left: auto;
    right: 1rem;
    top: 1rem;
  }
  
  .quick-actions {
    flex-direction: column;
  }
  
  .analytics-grid {
    grid-template-columns: 1fr;
  }
}

/* Font size variations */
.thanos-ultra.font-small {
  font-size: 0.875rem;
}

.thanos-ultra.font-large {
  font-size: 1.125rem;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--text-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}
</style>