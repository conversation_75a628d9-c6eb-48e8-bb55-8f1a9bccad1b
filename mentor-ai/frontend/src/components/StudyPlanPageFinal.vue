<template>
  <div class="study-plan-page">
    <!-- Creation Form Modal -->
    <transition name="modal-fade">
      <div v-if="showCreationForm" class="creation-modal-overlay" @click.self="closeCreationForm">
        <div class="creation-modal-container">
          <div class="creation-form-wrapper">
            <div class="creation-header">
              <h2>
                <i class="fas fa-plus-circle"></i>
                Criar Novo Plano de Estudos
              </h2>
              <button @click="closeCreationForm" class="close-btn">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <StudyPlanCreationForm @back="closeCreationForm" @plan-created="handlePlanCreated" />
          </div>
        </div>
      </div>
    </transition>
    
    <!-- Subject Manager Modal -->
    <transition name="modal-fade">
      <div v-if="showSubjectManager" class="subjects-modal-overlay" @click.self="showSubjectManager = false">
        <div class="subjects-modal-container">
          <div class="subjects-modal-content">
            <div class="subjects-modal-header">
              <div class="header-title-section">
                <div class="title-icon">
                  <i class="fas fa-graduation-cap"></i>
                </div>
                <div>
                  <h3>Gerenciar Disciplinas</h3>
                  <p>Organize suas matérias de estudo</p>
                </div>
              </div>
              <button @click="showSubjectManager = false" class="modal-close-btn">
                <i class="fas fa-times"></i>
              </button>
            </div>
            
            <div class="subjects-modal-body">
              <!-- Stats Overview -->
              <div class="subjects-stats">
                <div class="stat-box">
                  <div class="stat-icon">
                    <i class="fas fa-book"></i>
                  </div>
                  <div class="stat-content">
                    <span class="stat-number">{{ subjects.length }}</span>
                    <span class="stat-label">Disciplinas</span>
                  </div>
                </div>
                <div class="stat-box">
                  <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                  </div>
                  <div class="stat-content">
                    <span class="stat-number">{{ getTotalHours() }}h</span>
                    <span class="stat-label">Total de Horas</span>
                  </div>
                </div>
                <div class="stat-box">
                  <div class="stat-icon">
                    <i class="fas fa-tasks"></i>
                  </div>
                  <div class="stat-content">
                    <span class="stat-number">{{ getActiveSubjects() }}</span>
                    <span class="stat-label">Ativas</span>
                  </div>
                </div>
              </div>

              <!-- Add/Edit Subject Form -->
              <div class="subject-form-section">
                <div class="section-header-clean">
                  <h4>
                    <i :class="editingSubject ? 'fas fa-edit' : 'fas fa-plus-circle'"></i>
                    {{ editingSubject ? 'Editar' : 'Adicionar' }} Disciplina
                  </h4>
                  <button v-if="editingSubject" @click="cancelEdit" class="cancel-edit-btn">
                    <i class="fas fa-times"></i>
                    Cancelar Edição
                  </button>
                </div>
                <div class="subject-form-grid">
                  <div class="form-group-clean">
                    <label>Nome da Disciplina *</label>
                    <input 
                      v-model="newSubject.name" 
                      type="text" 
                      placeholder="Ex: Anatomia Humana"
                      class="form-input-clean"
                    />
                  </div>
                  <div class="form-group-clean">
                    <label>Código</label>
                    <input 
                      v-model="newSubject.code" 
                      type="text" 
                      placeholder="Ex: ANAT101"
                      class="form-input-clean"
                    />
                  </div>
                  <div class="form-group-clean">
                    <label>Professor</label>
                    <input 
                      v-model="newSubject.professor" 
                      type="text" 
                      placeholder="Nome do professor"
                      class="form-input-clean"
                    />
                  </div>
                  <div class="form-group-clean">
                    <label>Carga Horária</label>
                    <input 
                      v-model="newSubject.hours" 
                      type="number" 
                      placeholder="Horas"
                      class="form-input-clean"
                    />
                  </div>
                  <div class="form-group-clean full-width">
                    <label>Descrição</label>
                    <textarea 
                      v-model="newSubject.description" 
                      placeholder="Breve descrição da disciplina..."
                      class="form-input-clean"
                      rows="2"
                    ></textarea>
                  </div>
                  
                  <div class="form-group-clean full-width">
                    <label>Ícone e Cor</label>
                    <div class="icon-color-grid">
                      <button 
                        v-for="option in iconOptions" 
                        :key="option.value"
                        type="button"
                        @click="selectIconAndColor(option)"
                        :class="['icon-btn-option', { selected: newSubject.icon === option.value }]"
                        :title="option.label"
                      >
                        <div class="icon-preview" :style="{ background: option.color }">
                          <i :class="option.value"></i>
                        </div>
                      </button>
                      <button 
                        type="button"
                        @click="openColorPicker"
                        class="icon-btn-option custom"
                        title="Cor personalizada"
                      >
                        <div class="icon-preview" :style="{ background: customGradient }">
                          <i class="fas fa-palette"></i>
                        </div>
                      </button>
                    </div>
                  </div>
                  
                  <div class="form-actions-clean">
                    <button type="button" @click="resetForm" class="btn-clean secondary">
                      <i class="fas fa-undo"></i>
                      Limpar
                    </button>
                    <button @click="saveSubject" class="btn-clean primary">
                      <i :class="editingSubject ? 'fas fa-save' : 'fas fa-plus'"></i>
                      {{ editingSubject ? 'Salvar' : 'Adicionar' }}
                    </button>
                  </div>
                </div>
              </div>
              
              <!-- Existing Subjects List -->
              <div class="subjects-list-section-enhanced">
                <div class="section-header-enhanced">
                  <div class="section-title-group">
                    <h4>
                      <i class="fas fa-list"></i>
                      Disciplinas Cadastradas
                    </h4>
                    <div class="subject-counter">
                      <span class="counter-number">{{ filteredSubjects.length }}</span>
                      <span class="counter-label">{{ filteredSubjects.length === 1 ? 'disciplina' : 'disciplinas' }}</span>
                    </div>
                  </div>
                </div>
                <div class="list-controls-clean">
                  <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input 
                      v-model="searchQuery" 
                      type="text" 
                      placeholder="Buscar disciplina..."
                      class="search-input-clean"
                    />
                  </div>
                  <div class="sort-box">
                    <i class="fas fa-sort"></i>
                    <select v-model="sortBy" class="sort-select-clean">
                      <option value="name">Nome</option>
                      <option value="code">Código</option>
                      <option value="hours">Carga Horária</option>
                    </select>
                  </div>
                </div>
                
                <div class="subjects-grid-enhanced">
                  <div 
                    v-for="subject in filteredSubjects" 
                    :key="subject.id"
                    class="subject-card-enhanced"
                    :style="{ borderColor: subject.color || 'rgba(255,255,255,0.1)' }"
                  >
                    <div class="card-header-clean">
                      <div class="subject-icon-clean" :style="{ background: subject.color || 'rgba(102, 126, 234, 0.2)' }">
                        <i :class="subject.icon || 'fas fa-book-medical'"></i>
                      </div>
                      <div class="card-actions-clean">
                        <button @click="toggleSubjectStatus(subject)" class="action-btn-clean" :class="{ active: subject.active !== false }" title="Ativar/Desativar">
                          <i :class="subject.active !== false ? 'fas fa-check-circle' : 'fas fa-circle'"></i>
                        </button>
                        <button @click="editSubject(subject)" class="action-btn-clean" title="Editar">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button @click="deleteSubject(subject.id)" class="action-btn-clean delete" title="Excluir">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </div>
                    
                    <div class="card-body">
                      <h5>{{ subject.name }}</h5>
                      <div class="subject-details">
                        <span v-if="subject.code" class="detail-item">
                          <i class="fas fa-tag"></i> {{ subject.code }}
                        </span>
                        <span v-if="subject.professor" class="detail-item">
                          <i class="fas fa-user-tie"></i> {{ subject.professor }}
                        </span>
                        <span v-if="subject.hours" class="detail-item">
                          <i class="fas fa-clock"></i> {{ subject.hours }}h
                        </span>
                      </div>
                      <p v-if="subject.description" class="subject-description">
                        {{ subject.description }}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div v-if="filteredSubjects.length === 0" class="empty-state">
                  <i class="fas fa-search"></i>
                  <p>Nenhuma disciplina encontrada</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
    
    <!-- Color Picker Modal -->
    <transition name="modal-fade">
      <div v-if="showColorPicker" class="color-picker-overlay" @click.self="showColorPicker = false">
        <div class="color-picker-modal">
          <div class="color-picker-header">
            <h4>Escolher Cor Personalizada</h4>
            <button @click="showColorPicker = false" class="close-color-picker">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="color-picker-body">
            <div class="color-inputs">
              <div class="color-input-group">
                <label>Cor 1</label>
                <input 
                  type="color" 
                  v-model="customColor1"
                  @input="updateCustomGradient"
                  class="color-input"
                />
                <span class="color-hex">{{ customColor1 }}</span>
              </div>
              <div class="gradient-preview-arrow">
                <i class="fas fa-long-arrow-alt-right"></i>
              </div>
              <div class="color-input-group">
                <label>Cor 2</label>
                <input 
                  type="color" 
                  v-model="customColor2"
                  @input="updateCustomGradient"
                  class="color-input"
                />
                <span class="color-hex">{{ customColor2 }}</span>
              </div>
            </div>
            <div class="gradient-preview-large" :style="{ background: customGradient }">
              <span>Preview</span>
            </div>
            <div class="preset-gradients">
              <h5>Gradientes Sugeridos</h5>
              <div class="preset-grid">
                <button 
                  v-for="preset in presetGradients" 
                  :key="preset.id"
                  @click="applyPresetGradient(preset)"
                  class="preset-btn"
                  :style="{ background: preset.gradient }"
                  :title="preset.name"
                ></button>
              </div>
            </div>
            <div class="color-picker-actions">
              <button @click="showColorPicker = false" class="btn-clean secondary">
                Cancelar
              </button>
              <button @click="applyCustomColor" class="btn-clean primary">
                <i class="fas fa-check"></i>
                Aplicar
              </button>
            </div>
          </div>
        </div>
      </div>
    </transition>
    
    <!-- Ultra Modern Background -->
    <div class="background-effects">
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
      <div class="gradient-orb orb-4"></div>
    </div>

    <!-- Main Layout -->
    <div class="study-plan-layout">
      <!-- Scheduler Column -->
      <div class="scheduler-column">
        <!-- Header Section -->
        <div class="scheduler-header">
          <div class="header-content">
            <div class="header-left">
              <div class="header-icon">
                <div class="icon-wrapper">
                  <i class="fas fa-brain"></i>
                </div>
              </div>
              <div class="header-text">
                <h1 class="main-title">
                  <span class="title-gradient">Plano de</span>
                  <span class="title-accent">Estudos</span>
                </h1>
                <p class="subtitle">Otimize seu aprendizado com IA avançada</p>
              </div>
            </div>
        
            <div class="header-stats">
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="fas fa-book-open"></i>
                </div>
                <div class="stat-info">
                  <span class="stat-value">{{ totalSubjects }}</span>
                  <span class="stat-label">Matérias</span>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="fas fa-clock"></i>
                </div>
                <div class="stat-info">
                  <span class="stat-value">{{ studyHours }}h</span>
                  <span class="stat-label">Hoje</span>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-info">
                  <span class="stat-value">{{ efficiency }}%</span>
                  <span class="stat-label">Eficiência</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Main Content -->
        <div class="main-container">
          <!-- Content Grid -->
          <div class="content-grid">
            <!-- Left Column -->
            <div class="left-column">
              <!-- Active Plans Section with Tabs -->
              <section class="modern-card study-central">
                <div class="section-header-ultra">
                  <div class="header-clean">
                    <h2 class="title-clean">Central de Estudos</h2>
                    <button @click="showSubjectManager = true" class="manage-subjects-btn" title="Gerenciar Disciplinas">
                      <i class="fas fa-cog"></i>
                      <span>Gerenciar Disciplinas</span>
                    </button>
                  </div>
                  
                  <div class="tabs-container">
                    <div class="tab-navigation-modern">
                      <button 
                        @click="activeTab = 'plans'"
                        :class="['tab-btn-modern', { active: activeTab === 'plans' }]"
                      >
                        <div class="tab-content-wrapper">
                          <div class="tab-icon">
                            <i class="fas fa-clipboard-list"></i>
                            <div class="tab-badge" v-if="activePlans.length > 0">{{ activePlans.length }}</div>
                          </div>
                          <div class="tab-text">
                            <span class="tab-title">Planos Ativos</span>
                            <span class="tab-subtitle">{{ activePlans.length }} plano{{ activePlans.length !== 1 ? 's' : '' }}</span>
                          </div>
                        </div>
                      </button>
                      <button 
                        @click="activeTab = 'sessions'"
                        :class="['tab-btn-modern', { active: activeTab === 'sessions' }]"
                      >
                        <div class="tab-content-wrapper">
                          <div class="tab-icon">
                            <i class="fas fa-calendar-day"></i>
                            <div class="tab-badge sessions" v-if="todaySessionsCount > 0">{{ todaySessionsCount }}</div>
                          </div>
                          <div class="tab-text">
                            <span class="tab-title">Sessões Hoje</span>
                            <span class="tab-subtitle">{{ todaySessionsCount }} sessão{{ todaySessionsCount !== 1 ? 'ões' : '' }}</span>
                          </div>
                        </div>
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Tab Content -->
                <div class="tab-content-modern">
                  <!-- Plans Tab -->
                  <transition name="tab-fade" mode="out-in">
                  <div v-if="activeTab === 'plans'" class="tab-pane-modern" key="plans">
                    <div class="tab-pane-header">
                      <button @click="loadDefaultPlans" class="icon-btn" title="Resetar planos">
                        <i class="fas fa-sync"></i>
                      </button>
                      <button type="button" @click="createNewPlan" class="create-plan-btn">
                        <i class="fas fa-plus"></i>
                        <span>Novo Plano</span>
                      </button>
                    </div>

                    <div class="plans-grid">
                      <div 
                        v-for="plan in activePlans" 
                        :key="plan.id"
                        @click="selectPlan(plan)"
                        class="plan-card"
                        :class="{ 
                          'is-active': selectedPlan?.id === plan.id,
                          'is-expanded': expandedPlanId === plan.id
                        }"
                      >
                        <div class="plan-header">
                          <div class="plan-icon" :style="{ background: plan.color }">
                            <i :class="plan.icon"></i>
                          </div>
                          <div class="plan-info">
                            <h3>{{ plan.name }}</h3>
                            <p>{{ plan.description }}</p>
                          </div>
                          <div class="plan-actions">
                            <button @click.stop="quickEditPlan(plan)" class="icon-btn">
                              <i class="fas fa-edit"></i>
                            </button>
                            <button @click.stop="archivePlan(plan)" class="icon-btn">
                              <i class="fas fa-archive"></i>
                            </button>
                          </div>
                        </div>
                        
                        <div class="plan-stats">
                          <div class="stat-row">
                            <div class="plan-stat">
                              <i class="fas fa-tasks"></i>
                              <span>{{ plan.completedTasks }}/{{ plan.totalTasks }} tarefas</span>
                            </div>
                            <div class="plan-stat">
                              <i class="fas fa-clock"></i>
                              <span>{{ plan.estimatedTime }}h total</span>
                            </div>
                          </div>
                          
                          <div class="plan-progress">
                            <div class="progress-header">
                              <span>Progresso</span>
                              <span class="progress-percentage">{{ plan.progress || 0 }}%</span>
                            </div>
                            <div class="progress-bar">
                              <div 
                                class="progress-fill" 
                                :style="{ 
                                  width: (plan.progress || 0) + '%',
                                  background: plan.color
                                }"
                              ></div>
                            </div>
                            <div class="progress-details">
                              <span class="hours-completed">
                                <i class="fas fa-clock"></i>
                                {{ (plan.hoursCompleted || 0).toFixed(1) }}h de {{ plan.estimatedTime }}h
                              </span>
                            </div>
                          </div>
                          
                          <div class="plan-footer">
                            <div class="deadline">
                              <i class="fas fa-calendar-alt"></i>
                              <span>{{ formatDateShort(plan.deadline) }}</span>
                            </div>
                            <button @click.stop="startStudySession(plan)" class="start-btn">
                              <i class="fas fa-play"></i>
                              Iniciar Sessão
                            </button>
                          </div>
                          
                          <!-- Expanded Content -->
                          <transition name="expand">
                            <div v-if="expandedPlanId === plan.id" class="plan-expanded">
                              <div class="expanded-section">
                                <h4>Detalhes do Plano</h4>
                                <p>{{ plan.description }}</p>
                              </div>
                              
                              <div class="expanded-section">
                                <h4>Tarefas</h4>
                                <div class="tasks-list">
                                  <div v-if="plan.tasks && plan.tasks.length > 0" class="task-items">
                                    <div v-for="task in plan.tasks" :key="task.id" class="task-item">
                                      <i class="fas fa-check-circle"></i>
                                      <span>{{ task.name }}</span>
                                    </div>
                                  </div>
                                  <p v-else class="no-tasks">Nenhuma tarefa adicionada ainda</p>
                                </div>
                              </div>
                              
                              <div class="expanded-actions">
                                <button @click.stop="quickEditPlan(plan)" class="expanded-btn edit">
                                  <i class="fas fa-edit"></i>
                                  Editar Plano
                                </button>
                                <button @click.stop="startStudySession(plan)" class="expanded-btn primary">
                                  <i class="fas fa-play"></i>
                                  Iniciar Agora
                                </button>
                              </div>
                            </div>
                          </transition>
                        </div>
                      </div>
                    </div>

                    <!-- Empty State -->
                    <div v-if="activePlans.length === 0" class="empty-state">
                      <div class="empty-icon">
                        <i class="fas fa-clipboard-list"></i>
                      </div>
                      <h3>Nenhum plano criado ainda</h3>
                      <p>Comece criando seu primeiro plano de estudos</p>
                      <button type="button" @click="createNewPlan" class="create-plan-btn primary">
                        <i class="fas fa-plus"></i>
                        Criar Primeiro Plano
                      </button>
                    </div>
                  </div>

                  </transition>
                  
                  <!-- Sessions Tab -->
                  <transition name="tab-fade" mode="out-in">
                  <div v-if="activeTab === 'sessions'" class="tab-pane-modern" key="sessions">
                    <StudySessionsFixed :show-header="false" />
                  </div>
                  </transition>
                </div>
              </section>
            </div>

            <!-- Right Column -->
            <div class="right-column">
              <!-- Calendar Integration -->
              <section class="calendar-section">
                <div class="section-header">
                  <h2 class="section-heading">
                    <span class="title-gradient">Calendário</span>
                  </h2>
                </div>
                
                <div class="calendar-wrapper">
                  <SharedCalendar 
                    :events="calendarEvents"
                    :selected-date="selectedCalendarDate"
                    :use-vuex-store="false"
                    @date-selected="handleDateSelected"
                    @month-changed="handleMonthChanged"
                  />
                </div>
              </section>

              <!-- AI Insights -->
              <section class="insights-section">
                <div class="section-header">
                  <h2 class="section-heading">
                    <span class="title-gradient">Insights</span>
                    <span class="title-accent">IA</span>
                  </h2>
                  <button @click="refreshInsights" class="refresh-btn">
                    <i class="fas fa-sync-alt"></i>
                  </button>
                </div>
                
                <div class="insights-list">
                  <div 
                    v-for="insight in aiInsights" 
                    :key="insight.id"
                    class="insight-card"
                    :class="`insight-${insight.type}`"
                  >
                    <div class="insight-icon">
                      <i :class="insight.icon"></i>
                    </div>
                    <div class="insight-content">
                      <h4>{{ insight.title }}</h4>
                      <p>{{ insight.message }}</p>
                      <button v-if="insight.actionText" @click="insight.action" class="insight-action">
                        {{ insight.actionText }}
                        <i class="fas fa-arrow-right"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </section>

              <!-- Quick Notes -->
              <section class="notes-section">
                <div class="section-header">
                  <h3>Notas Rápidas</h3>
                </div>
                <textarea 
                  v-model="quickNotes"
                  placeholder="Digite suas anotações aqui..."
                  class="quick-notes"
                ></textarea>
                <button @click="saveNotes" class="save-notes-btn">
                  <i class="fas fa-save"></i>
                  Salvar
                </button>
              </section>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- New Plan Modal -->
    <div v-if="showNewPlanModal" class="modal-overlay" @click.self="showNewPlanModal = false">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ editingPlanId ? 'Editar' : 'Criar Novo' }} Plano de Estudos</h3>
          <button class="modal-close" @click="closeModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="modal-body">
          <form @submit.prevent="submitNewPlan">
            <div class="form-group">
              <label>
                <i class="fas fa-book"></i>
                Nome do Plano *
              </label>
              <input 
                v-model="newPlan.name" 
                type="text" 
                placeholder="Ex: Anatomia Humana"
                maxlength="50"
                required
              />
              <span class="char-count">{{ newPlan.name.length }}/50</span>
            </div>
            
            <div class="form-group">
              <label>
                <i class="fas fa-align-left"></i>
                Descrição *
              </label>
              <textarea 
                v-model="newPlan.description" 
                placeholder="Descreva os objetivos e conteúdo do plano"
                rows="3"
                maxlength="200"
                required
              ></textarea>
              <span class="char-count">{{ newPlan.description.length }}/200</span>
            </div>
            
            <div class="form-group">
              <label>Categoria</label>
              <select v-model="newPlan.category" class="form-input">
                <option value="">Selecione uma categoria</option>
                <option value="medicina">Medicina</option>
                <option value="biologia">Biologia</option>
                <option value="quimica">Química</option>
                <option value="fisica">Física</option>
                <option value="matematica">Matemática</option>
                <option value="outras">Outras</option>
              </select>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label>Data de Início</label>
                <input 
                  v-model="newPlan.startDate" 
                  type="date"
                />
              </div>
              
              <div class="form-group">
                <label>Prazo Final</label>
                <input 
                  v-model="newPlan.deadline" 
                  type="date"
                />
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label>Horas de Estudo por Dia</label>
                <input 
                  v-model.number="newPlan.dailyHours" 
                  type="number" 
                  min="0.5"
                  max="12"
                  step="0.5"
                  placeholder="2"
                />
              </div>
              
              <div class="form-group">
                <label>
                  <i class="fas fa-clock"></i>
                  Tempo Total Estimado (horas)
                </label>
                <div class="time-input-wrapper">
                  <input 
                    v-model.number="newPlan.estimatedTime" 
                    type="number" 
                    min="1"
                    placeholder="40"
                  />
                  <div class="time-suggestions">
                    <button type="button" @click="newPlan.estimatedTime = 20" class="suggestion-chip" :class="{ active: newPlan.estimatedTime === 20 }">20h</button>
                    <button type="button" @click="newPlan.estimatedTime = 40" class="suggestion-chip" :class="{ active: newPlan.estimatedTime === 40 }">40h</button>
                    <button type="button" @click="newPlan.estimatedTime = 60" class="suggestion-chip" :class="{ active: newPlan.estimatedTime === 60 }">60h</button>
                    <button type="button" @click="newPlan.estimatedTime = 80" class="suggestion-chip" :class="{ active: newPlan.estimatedTime === 80 }">80h</button>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="form-group">
              <label>Dificuldade</label>
              <div class="difficulty-selector">
                <button 
                  v-for="level in ['Fácil', 'Médio', 'Difícil']" 
                  :key="level"
                  type="button"
                  class="difficulty-option"
                  :class="{ selected: newPlan.difficulty === level }"
                  @click="newPlan.difficulty = level"
                >
                  {{ level }}
                </button>
              </div>
            </div>
            
            <div class="form-group">
              <label>
                <i class="fas fa-tasks"></i>
                Objetivos do Plano
              </label>
              <div class="objectives-list">
                <div v-for="(objective, index) in newPlan.objectives" :key="index" class="objective-item">
                  <input 
                    v-model="objective.text" 
                    type="text" 
                    placeholder="Digite um objetivo..."
                    class="objective-input"
                  />
                  <button 
                    type="button" 
                    @click="removeObjective(index)" 
                    class="objective-remove"
                    :disabled="newPlan.objectives.length === 1"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
                <button type="button" @click="addObjective" class="add-objective-btn">
                  <i class="fas fa-plus"></i>
                  Adicionar Objetivo
                </button>
              </div>
            </div>
            
            <div class="form-group">
              <label>Ícone</label>
              <div class="icon-selector">
                <button 
                  v-for="icon in ['fas fa-book', 'fas fa-brain', 'fas fa-flask', 'fas fa-heart', 'fas fa-pills', 'fas fa-dna']"
                  :key="icon"
                  type="button"
                  @click="newPlan.icon = icon"
                  :class="['icon-option', { selected: newPlan.icon === icon }]"
                >
                  <i :class="icon"></i>
                </button>
              </div>
            </div>
            
            <div class="form-group">
              <label>
                <i class="fas fa-palette"></i>
                Cor do Tema
              </label>
              <div class="color-section">
                <div class="preset-colors">
                  <h5>Cores Predefinidas</h5>
                  <div class="color-selector">
                    <button 
                      v-for="color in [
                        'linear-gradient(135deg, #667eea, #764ba2)',
                        'linear-gradient(135deg, #f093fb, #f5576c)',
                        'linear-gradient(135deg, #4facfe, #00f2fe)',
                        'linear-gradient(135deg, #fa709a, #fee140)',
                        'linear-gradient(135deg, #30cfd0, #330867)'
                      ]"
                      :key="color"
                      type="button"
                      @click="newPlan.color = color"
                      :class="['color-option', { selected: newPlan.color === color }]"
                      :style="{ background: color }"
                    >
                      <i v-if="newPlan.color === color" class="fas fa-check"></i>
                    </button>
                  </div>
                </div>
                
                <div class="custom-color">
                  <h5>Cor Personalizada</h5>
                  <div class="custom-color-inputs">
                    <div class="color-input-group">
                      <input 
                        type="color" 
                        v-model="customColor1"
                        @input="updateCustomGradient"
                        class="color-picker"
                      />
                      <span class="color-label">Cor 1</span>
                    </div>
                    <div class="gradient-arrow">
                      <i class="fas fa-long-arrow-alt-right"></i>
                    </div>
                    <div class="color-input-group">
                      <input 
                        type="color" 
                        v-model="customColor2"
                        @input="updateCustomGradient"
                        class="color-picker"
                      />
                      <span class="color-label">Cor 2</span>
                    </div>
                    <button 
                      type="button"
                      @click="applyCustomGradient"
                      class="apply-custom-btn"
                      :style="{ background: customGradient }"
                    >
                      <i class="fas fa-check"></i>
                      Aplicar
                    </button>
                  </div>
                  <div class="gradient-preview" :style="{ background: customGradient }">
                    <span>Preview</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="form-group">
              <label>
                <i class="fas fa-bell"></i>
                Notificações
              </label>
              <div class="notification-section">
                <div class="notification-toggle">
                  <label class="switch-label">
                    <input 
                      v-model="newPlan.notifications.enabled" 
                      type="checkbox"
                      class="switch-input"
                    />
                    <span class="switch-slider"></span>
                    <span class="switch-text">Ativar Notificações</span>
                  </label>
                </div>
                
                <div v-if="newPlan.notifications.enabled" class="notification-options">
                  <div class="notification-item">
                    <label class="checkbox-option">
                      <div class="checkbox-wrapper">
                        <input type="checkbox" v-model="newPlan.notifications.daily" id="daily-reminder">
                        <label for="daily-reminder" class="custom-checkbox"></label>
                      </div>
                      <div class="notification-content">
                        <span class="notification-title">Lembrete Diário</span>
                        <span class="notification-desc">Receba um lembrete todos os dias no horário definido</span>
                      </div>
                    </label>
                    <div class="notification-icon">
                      <i class="fas fa-calendar-day"></i>
                    </div>
                  </div>
                  
                  <div class="notification-item">
                    <label class="checkbox-option">
                      <div class="checkbox-wrapper">
                        <input type="checkbox" v-model="newPlan.notifications.deadline" id="deadline-reminder">
                        <label for="deadline-reminder" class="custom-checkbox"></label>
                      </div>
                      <div class="notification-content">
                        <span class="notification-title">Alerta de Prazo</span>
                        <span class="notification-desc">Notificação quando o prazo estiver se aproximando</span>
                      </div>
                    </label>
                    <div class="notification-icon">
                      <i class="fas fa-exclamation-triangle"></i>
                    </div>
                  </div>
                  
                  <div class="notification-item">
                    <label class="checkbox-option">
                      <div class="checkbox-wrapper">
                        <input type="checkbox" v-model="newPlan.notifications.progress" id="progress-update">
                        <label for="progress-update" class="custom-checkbox"></label>
                      </div>
                      <div class="notification-content">
                        <span class="notification-title">Relatório Semanal</span>
                        <span class="notification-desc">Resumo do seu progresso enviado semanalmente</span>
                      </div>
                    </label>
                    <div class="notification-icon">
                      <i class="fas fa-chart-line"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="modal-actions">
              <button type="button" @click="closeModal" class="btn-cancel">
                Cancelar
              </button>
              <button type="submit" class="btn-submit">
                <i :class="editingPlanId ? 'fas fa-save' : 'fas fa-plus'"></i>
                {{ editingPlanId ? 'Salvar Alterações' : 'Criar Plano' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import SharedCalendar from './SharedCalendar.vue'
import StudySessionsFixed from './StudySessionsFixed.vue'
import StudyPlanCreationForm from './StudyPlanCreationForm.vue'

export default {
  name: 'StudyPlanPageFinal',
  components: {
    SharedCalendar,
    StudySessionsFixed,
    StudyPlanCreationForm
  },
  setup() {
    const router = useRouter()
    const store = useStore()
    
    // State
    const selectedPlan = ref(null)
    const expandedPlanId = ref(null)
    const quickNotes = ref('')
    const activeTab = ref('plans')
    const showNewPlanModal = ref(false)
    const editingPlanId = ref(null)
    
    // Custom color states
    const customColor1 = ref('#667eea')
    const customColor2 = ref('#764ba2')
    const customGradient = ref('linear-gradient(135deg, #667eea, #764ba2)')
    
    // New Plan Form
    const newPlan = ref({
      name: '',
      description: '',
      category: '',
      startDate: '',
      deadline: '',
      dailyHours: '',
      estimatedTime: '',
      difficulty: 'Médio',
      objectives: [{ text: '' }],
      icon: 'fas fa-brain',
      color: 'linear-gradient(135deg, #667eea, #764ba2)',
      notifications: {
        enabled: true,
        daily: true,
        deadline: true,
        progress: false
      }
    })
    
    // Plans
    const activePlans = ref([])
    
    // AI Insights
    const aiInsights = ref([
      {
        id: 1,
        type: 'suggestion',
        icon: 'fas fa-lightbulb',
        title: 'Otimização de Horário',
        message: 'Baseado no seu desempenho, sugiro estudar Anatomia pela manhã.',
        actionText: 'Aplicar',
        action: () => console.log('Apply suggestion')
      },
      {
        id: 2,
        type: 'warning',
        icon: 'fas fa-exclamation-triangle',
        title: 'Revisão Pendente',
        message: 'Você tem 5 tópicos de Farmacologia para revisar esta semana.',
        actionText: 'Ver tópicos',
        action: () => console.log('View topics')
      },
      {
        id: 3,
        type: 'achievement',
        icon: 'fas fa-trophy',
        title: 'Meta Alcançada!',
        message: 'Você completou 10 horas de estudo esta semana. Parabéns!',
        actionText: 'Ver detalhes',
        action: () => console.log('View achievement')
      }
    ])
    
    // Calendar
    const selectedCalendarDate = ref(null)
    
    // Load data
    const loadPlans = () => {
      const savedPlans = localStorage.getItem('activePlans')
      if (savedPlans) {
        try {
          activePlans.value = JSON.parse(savedPlans)
          console.log('Loaded plans:', activePlans.value)
        } catch (e) {
          console.error('Error parsing saved plans:', e)
          // Load defaults on error
          loadDefaultPlans()
        }
      } else {
        loadDefaultPlans()
      }
    }
    
    const loadDefaultPlans = () => {
        // Default plans
        activePlans.value = [
          {
            id: 1,
            name: 'Anatomia Humana',
            description: 'Sistema musculoesquelético completo',
            icon: 'fas fa-bone',
            color: 'linear-gradient(135deg, #667eea, #764ba2)',
            completedTasks: 15,
            totalTasks: 25,
            estimatedTime: 40,
            deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            progress: 60
          },
          {
            id: 2,
            name: 'Farmacologia',
            description: 'Bases farmacológicas e farmacocinética',
            icon: 'fas fa-pills',
            color: 'linear-gradient(135deg, #f093fb, #f5576c)',
            completedTasks: 8,
            totalTasks: 20,
            estimatedTime: 30,
            deadline: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(),
            progress: 40
          },
          {
            id: 3,
            name: 'Neurociências',
            description: 'Sistema nervoso central e periférico',
            icon: 'fas fa-brain',
            color: 'linear-gradient(135deg, #4facfe, #00f2fe)',
            completedTasks: 12,
            totalTasks: 30,
            estimatedTime: 50,
            deadline: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(),
            progress: 40
          }
        ]
        // Save default plans
        localStorage.setItem('activePlans', JSON.stringify(activePlans.value))
    }
    
    // Computed
    const totalSubjects = computed(() => activePlans.value.length)
    const studyHours = computed(() => 2.5) // Placeholder
    const efficiency = computed(() => 85) // Placeholder
    
    const todaySessionsCount = computed(() => {
      // This would come from your actual data
      return 3
    })
    
    const calendarEvents = computed(() => {
      const events = []
      
      activePlans.value.forEach(plan => {
        if (plan.deadline) {
          events.push({
            id: `deadline-${plan.id}`,
            date: plan.deadline,
            title: `Prazo: ${plan.name}`,
            color: plan.color || '#667eea',
            type: 'deadline'
          })
        }
      })
      
      return events
    })
    
    // Methods
    const selectPlan = (plan) => {
      selectedPlan.value = plan
      // Toggle expansion
      if (expandedPlanId.value === plan.id) {
        expandedPlanId.value = null
      } else {
        expandedPlanId.value = plan.id
      }
    }
    
    const showCreationForm = ref(false)
    const showSubjectManager = ref(false)
    
    // Subject Management
    const subjects = ref([])
    const searchQuery = ref('')
    const sortBy = ref('name')
    const editingSubject = ref(null)
    const newSubject = ref({
      name: '',
      code: '',
      icon: '',
      color: '',
      professor: '',
      hours: '',
      description: '',
      active: true
    })
    
    const iconOptions = ref([
      // Sistemas do Corpo
      { value: 'fas fa-brain', label: 'Neurociências', color: 'linear-gradient(135deg, #667eea, #764ba2)' },
      { value: 'fas fa-bone', label: 'Anatomia', color: 'linear-gradient(135deg, #f093fb, #f5576c)' },
      { value: 'fas fa-heartbeat', label: 'Cardiologia', color: 'linear-gradient(135deg, #fa709a, #fee140)' },
      { value: 'fas fa-lungs', label: 'Pneumologia', color: 'linear-gradient(135deg, #55a3ff, #5468ff)' },
      { value: 'fas fa-tooth', label: 'Odontologia', color: 'linear-gradient(135deg, #f8b500, #ff6b6b)' },
      { value: 'fas fa-eye', label: 'Oftalmologia', color: 'linear-gradient(135deg, #00cec9, #6c5ce7)' },
      { value: 'fas fa-baby', label: 'Pediatria', color: 'linear-gradient(135deg, #ffd89b, #19547b)' },
      { value: 'fas fa-hand-holding-heart', label: 'Cuidados', color: 'linear-gradient(135deg, #ff758c, #ff7eb3)' },
      
      // Especialidades Médicas
      { value: 'fas fa-pills', label: 'Farmacologia', color: 'linear-gradient(135deg, #4facfe, #00f2fe)' },
      { value: 'fas fa-microscope', label: 'Patologia', color: 'linear-gradient(135deg, #30cfd0, #330867)' },
      { value: 'fas fa-virus', label: 'Microbiologia', color: 'linear-gradient(135deg, #a8edea, #fed6e3)' },
      { value: 'fas fa-dna', label: 'Genética', color: 'linear-gradient(135deg, #ffecd2, #fcb69f)' },
      { value: 'fas fa-shield-virus', label: 'Imunologia', color: 'linear-gradient(135deg, #ff9a9e, #fecfef)' },
      { value: 'fas fa-radiation', label: 'Radiologia', color: 'linear-gradient(135deg, #f77062, #fe5196)' },
      { value: 'fas fa-allergies', label: 'Alergologia', color: 'linear-gradient(135deg, #fddb92, #d1fdff)' },
      { value: 'fas fa-biohazard', label: 'Infectologia', color: 'linear-gradient(135deg, #fc466b, #3f5efb)' },
      
      // Procedimentos e Práticas
      { value: 'fas fa-user-md', label: 'Clínica', color: 'linear-gradient(135deg, #fbc2eb, #a6c1ee)' },
      { value: 'fas fa-stethoscope', label: 'Semiologia', color: 'linear-gradient(135deg, #ffeaa7, #ff6b6b)' },
      { value: 'fas fa-syringe', label: 'Prática', color: 'linear-gradient(135deg, #dfe6e9, #74b9ff)' },
      { value: 'fas fa-procedures', label: 'Cirurgia', color: 'linear-gradient(135deg, #ee9ca7, #ffdde1)' },
      { value: 'fas fa-ambulance', label: 'Emergência', color: 'linear-gradient(135deg, #ff512f, #dd2476)' },
      { value: 'fas fa-first-aid', label: 'Primeiros Socorros', color: 'linear-gradient(135deg, #11998e, #38ef7d)' },
      { value: 'fas fa-hospital', label: 'Hospitalar', color: 'linear-gradient(135deg, #2193b0, #6dd5ed)' },
      { value: 'fas fa-clinic-medical', label: 'Clínica Médica', color: 'linear-gradient(135deg, #cc2b5e, #753a88)' },
      
      // Outros
      { value: 'fas fa-book-medical', label: 'Geral', color: 'linear-gradient(135deg, #84fab0, #8fd3f4)' },
      { value: 'fas fa-notes-medical', label: 'Prontuário', color: 'linear-gradient(135deg, #667db6, #0082c8)' },
      { value: 'fas fa-user-nurse', label: 'Enfermagem', color: 'linear-gradient(135deg, #ee0979, #ff6a00)' },
      { value: 'fas fa-capsules', label: 'Farmácia', color: 'linear-gradient(135deg, #4568dc, #b06ab3)' },
      { value: 'fas fa-vials', label: 'Laboratório', color: 'linear-gradient(135deg, #20bf6b, #26de81)' },
      { value: 'fas fa-x-ray', label: 'Diagnóstico', color: 'linear-gradient(135deg, #eb3349, #f45c43)' }
    ])
    
    const showColorPicker = ref(false)
    
    const presetGradients = ref([
      { id: 1, name: 'Sunset', gradient: 'linear-gradient(135deg, #ff6b6b, #feca57)' },
      { id: 2, name: 'Ocean', gradient: 'linear-gradient(135deg, #0984e3, #74b9ff)' },
      { id: 3, name: 'Forest', gradient: 'linear-gradient(135deg, #00b894, #55efc4)' },
      { id: 4, name: 'Lavender', gradient: 'linear-gradient(135deg, #a29bfe, #d63031)' },
      { id: 5, name: 'Mint', gradient: 'linear-gradient(135deg, #00d2d3, #54a0ff)' },
      { id: 6, name: 'Rose', gradient: 'linear-gradient(135deg, #ff9ff3, #feca57)' },
      { id: 7, name: 'Night', gradient: 'linear-gradient(135deg, #2c3e50, #3498db)' },
      { id: 8, name: 'Fire', gradient: 'linear-gradient(135deg, #f39c12, #e74c3c)' }
    ])

    const createNewPlan = () => {
      showCreationForm.value = true
    }

    const closeCreationForm = () => {
      showCreationForm.value = false
    }

    const handlePlanCreated = (newPlan) => {
      // Add the new plan to the active plans
      activePlans.value.push(newPlan)
      // Save to localStorage
      localStorage.setItem('activePlans', JSON.stringify(activePlans.value))
      // Close the creation form
      showCreationForm.value = false
      // Show success message or notification
      console.log('New plan created successfully:', newPlan.name)
    }
    
    const submitNewPlan = () => {
      if (!newPlan.value.name.trim()) {
        alert('Por favor, insira um nome para o plano')
        return
      }
      
      if (editingPlanId.value) {
        // Modo de edição - atualizar plano existente
        const index = activePlans.value.findIndex(p => p.id === editingPlanId.value)
        if (index > -1) {
          // Preservar dados importantes do plano original
          const originalPlan = activePlans.value[index]
          activePlans.value[index] = {
            ...originalPlan,
            ...newPlan.value,
            id: originalPlan.id,
            progress: originalPlan.progress,
            tasks: originalPlan.tasks || [],
            completedTasks: originalPlan.completedTasks || 0,
            totalTasks: originalPlan.totalTasks || 0,
            createdAt: originalPlan.createdAt
          }
        }
      } else {
        // Modo de criação - criar novo plano
        const plan = {
          id: Date.now(),
          ...newPlan.value,
          progress: 0,
          completedTasks: 0,
          totalTasks: 0,
          tasks: [],
          hoursCompleted: 0,
          createdAt: new Date().toISOString()
        }
        
        activePlans.value.push(plan)
        
        // Selecionar o novo plano
        selectedPlan.value = plan
      }
      
      // Salvar no localStorage
      localStorage.setItem('activePlans', JSON.stringify(activePlans.value))
      
      // Limpar formulário e fechar modal
      newPlan.value = {
        name: '',
        description: '',
        deadline: '',
        estimatedTime: '',
        icon: 'fas fa-brain',
        color: 'linear-gradient(135deg, #667eea, #764ba2)'
      }
      editingPlanId.value = null
      showNewPlanModal.value = false
    }
    
    const quickEditPlan = (plan) => {
      // Preencher o formulário com os dados do plano existente
      newPlan.value = {
        name: plan.name,
        description: plan.description,
        category: plan.category || '',
        startDate: plan.startDate ? new Date(plan.startDate).toISOString().split('T')[0] : '',
        deadline: plan.deadline ? new Date(plan.deadline).toISOString().split('T')[0] : '',
        dailyHours: plan.dailyHours || '',
        estimatedTime: plan.estimatedTime || '',
        difficulty: plan.difficulty || 'Médio',
        objectives: plan.objectives && plan.objectives.length > 0 ? [...plan.objectives] : [{ text: '' }],
        icon: plan.icon || 'fas fa-brain',
        color: plan.color || 'linear-gradient(135deg, #667eea, #764ba2)',
        notifications: plan.notifications || {
          enabled: true,
          daily: true,
          deadline: true,
          progress: false
        }
      }
      
      // Guardar o ID do plano sendo editado
      editingPlanId.value = plan.id
      
      // Abrir o modal
      showNewPlanModal.value = true
    }
    
    const archivePlan = (plan) => {
      if (confirm(`Deseja arquivar o plano "${plan.name}"?`)) {
        const index = activePlans.value.findIndex(p => p.id === plan.id)
        if (index > -1) {
          activePlans.value.splice(index, 1)
          localStorage.setItem('activePlans', JSON.stringify(activePlans.value))
        }
      }
    }
    
    const startStudySession = (plan) => {
      selectedPlan.value = plan
      router.push('/pomodoro')
    }
    
    const formatDateShort = (date) => {
      if (!date) return ''
      try {
        const d = new Date(date)
        return `${d.getDate()} ${d.toLocaleDateString('pt-BR', { month: 'short' })}`
      } catch (error) {
        return ''
      }
    }
    
    const handleDateSelected = (date) => {
      selectedCalendarDate.value = date
    }
    
    const handleMonthChanged = (newMonth) => {
      console.log('Month changed:', newMonth)
    }
    
    const refreshInsights = () => {
      console.log('Refresh insights')
    }
    
    const saveNotes = () => {
      localStorage.setItem('quickNotes', quickNotes.value)
      console.log('Notes saved')
    }
    
    const updateCustomGradient = () => {
      customGradient.value = `linear-gradient(135deg, ${customColor1.value}, ${customColor2.value})`
    }
    
    const applyCustomGradient = () => {
      newPlan.value.color = customGradient.value
    }
    
    const addObjective = () => {
      newPlan.value.objectives.push({ text: '' })
    }
    
    const removeObjective = (index) => {
      if (newPlan.value.objectives.length > 1) {
        newPlan.value.objectives.splice(index, 1)
      }
    }
    
    // Subject Management Methods
    const loadSubjects = () => {
      const savedSubjects = localStorage.getItem('subjects')
      if (savedSubjects) {
        subjects.value = JSON.parse(savedSubjects)
      } else {
        // Default subjects with enhanced data
        subjects.value = [
          { 
            id: 1, 
            name: 'Anatomia Humana', 
            code: 'ANAT101', 
            icon: 'fas fa-bone',
            color: 'linear-gradient(135deg, #f093fb, #f5576c)',
            professor: 'Dr. Silva',
            hours: 80,
            description: 'Estudo da estrutura do corpo humano',
            active: true
          },
          { 
            id: 2, 
            name: 'Farmacologia', 
            code: 'FARM201', 
            icon: 'fas fa-pills',
            color: 'linear-gradient(135deg, #4facfe, #00f2fe)',
            professor: 'Dra. Santos',
            hours: 60,
            description: 'Estudo dos medicamentos e seus efeitos',
            active: true
          },
          { 
            id: 3, 
            name: 'Neurociências', 
            code: 'NEURO301', 
            icon: 'fas fa-brain',
            color: 'linear-gradient(135deg, #667eea, #764ba2)',
            professor: 'Dr. Oliveira',
            hours: 90,
            description: 'Sistema nervoso e suas funções',
            active: true
          },
          { 
            id: 4, 
            name: 'Fisiologia', 
            code: 'FISIO101', 
            icon: 'fas fa-heartbeat',
            color: 'linear-gradient(135deg, #fa709a, #fee140)',
            professor: 'Dra. Costa',
            hours: 75,
            description: 'Funcionamento dos sistemas do corpo',
            active: true
          }
        ]
        saveSubjects()
      }
    }
    
    const saveSubjects = () => {
      localStorage.setItem('subjects', JSON.stringify(subjects.value))
    }
    
    // Removed addSubject - functionality merged into saveSubject
    
    const resetForm = () => {
      newSubject.value = {
        name: '',
        code: '',
        icon: '',
        color: '',
        professor: '',
        hours: '',
        description: '',
        active: true
      }
    }
    
    const selectIconAndColor = (option) => {
      newSubject.value.icon = option.value
      newSubject.value.color = option.color
    }
    
    const openColorPicker = () => {
      showColorPicker.value = true
    }
    
    const applyCustomColor = () => {
      newSubject.value.color = customGradient.value
      showColorPicker.value = false
    }
    
    const applyPresetGradient = (preset) => {
      const colors = preset.gradient.match(/#[a-fA-F0-9]{6}/g)
      if (colors && colors.length >= 2) {
        customColor1.value = colors[0]
        customColor2.value = colors[1]
        updateCustomGradient()
      }
    }
    
    const toggleSubjectStatus = (subject) => {
      subject.active = subject.active === false ? true : false
      saveSubjects()
    }
    
    const getTotalHours = () => {
      return subjects.value.reduce((total, subject) => total + (parseInt(subject.hours) || 0), 0)
    }
    
    const getActiveSubjects = () => {
      return subjects.value.filter(s => s.active !== false).length
    }
    
    const filteredSubjects = computed(() => {
      let filtered = subjects.value
      
      // Search filter
      if (searchQuery.value) {
        filtered = filtered.filter(subject => 
          subject.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
          (subject.code && subject.code.toLowerCase().includes(searchQuery.value.toLowerCase())) ||
          (subject.professor && subject.professor.toLowerCase().includes(searchQuery.value.toLowerCase()))
        )
      }
      
      // Sort
      filtered.sort((a, b) => {
        switch(sortBy.value) {
          case 'code':
            return (a.code || '').localeCompare(b.code || '')
          case 'hours':
            return (b.hours || 0) - (a.hours || 0)
          default:
            return a.name.localeCompare(b.name)
        }
      })
      
      return filtered
    })
    
    const editSubject = (subject) => {
      editingSubject.value = subject
      newSubject.value = {
        name: subject.name,
        code: subject.code || '',
        icon: subject.icon || '',
        color: subject.color || '',
        professor: subject.professor || '',
        hours: subject.hours || '',
        description: subject.description || '',
        active: subject.active !== false
      }
      // Scroll to the form
      setTimeout(() => {
        const formSection = document.querySelector('.subject-form-section')
        if (formSection) {
          formSection.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
      }, 100)
    }
    
    const cancelEdit = () => {
      editingSubject.value = null
      resetForm()
    }
    
    const saveSubject = () => {
      if (!newSubject.value.name.trim()) {
        alert('Por favor, insira o nome da disciplina')
        return
      }
      
      if (editingSubject.value) {
        // Update existing subject
        const index = subjects.value.findIndex(s => s.id === editingSubject.value.id)
        if (index > -1) {
          subjects.value[index] = {
            ...subjects.value[index],
            name: newSubject.value.name,
            code: newSubject.value.code,
            icon: newSubject.value.icon || subjects.value[index].icon || 'fas fa-book-medical',
            color: newSubject.value.color || subjects.value[index].color || 'linear-gradient(135deg, #84fab0, #8fd3f4)',
            professor: newSubject.value.professor,
            hours: newSubject.value.hours || 0,
            description: newSubject.value.description,
            active: newSubject.value.active !== undefined ? newSubject.value.active : subjects.value[index].active
          }
        }
        editingSubject.value = null
      } else {
        // Add new subject
        const newId = subjects.value.length > 0 
          ? Math.max(...subjects.value.map(s => s.id)) + 1 
          : 1
        
        subjects.value.push({
          id: newId,
          name: newSubject.value.name,
          code: newSubject.value.code,
          icon: newSubject.value.icon || 'fas fa-book-medical',
          color: newSubject.value.color || 'linear-gradient(135deg, #84fab0, #8fd3f4)',
          professor: newSubject.value.professor,
          hours: newSubject.value.hours || 0,
          description: newSubject.value.description,
          active: true
        })
      }
      
      saveSubjects()
      resetForm()
    }
    
    const deleteSubject = (id) => {
      if (confirm('Tem certeza que deseja excluir esta disciplina?')) {
        subjects.value = subjects.value.filter(s => s.id !== id)
        saveSubjects()
      }
    }
    
    const closeModal = () => {
      showNewPlanModal.value = false
      editingPlanId.value = null
      // Limpar formulário
      newPlan.value = {
        name: '',
        description: '',
        category: '',
        startDate: '',
        deadline: '',
        dailyHours: '',
        estimatedTime: '',
        difficulty: 'Médio',
        objectives: [{ text: '' }],
        icon: 'fas fa-brain',
        color: 'linear-gradient(135deg, #667eea, #764ba2)',
        notifications: {
          enabled: true,
          daily: true,
          deadline: true,
          progress: false
        }
      }
    }
    
    // Removida função duplicada submitNewPlan - já está definida acima
    
    // Lifecycle
    onMounted(() => {
      loadPlans()
      loadSubjects()
      
      const savedNotes = localStorage.getItem('quickNotes')
      if (savedNotes) {
        quickNotes.value = savedNotes
      }
    })
    
    return {
      // State
      selectedPlan,
      expandedPlanId,
      editingPlanId,
      quickNotes,
      activeTab,
      activePlans,
      aiInsights,
      selectedCalendarDate,
      showNewPlanModal,
      newPlan,
      showCreationForm,
      customColor1,
      customColor2,
      customGradient,
      
      // Computed
      totalSubjects,
      studyHours,
      efficiency,
      todaySessionsCount,
      calendarEvents,
      
      // Methods
      selectPlan,
      createNewPlan,
      quickEditPlan,
      closeCreationForm,
      handlePlanCreated,
      archivePlan,
      startStudySession,
      formatDateShort,
      handleDateSelected,
      handleMonthChanged,
      refreshInsights,
      saveNotes,
      loadDefaultPlans,
      submitNewPlan,
      closeModal,
      updateCustomGradient,
      applyCustomGradient,
      addObjective,
      removeObjective,
      showSubjectManager,
      subjects,
      newSubject,
      editSubject,
      deleteSubject,
      searchQuery,
      sortBy,
      iconOptions,
      filteredSubjects,
      resetForm,
      selectIconAndColor,
      toggleSubjectStatus,
      getTotalHours,
      getActiveSubjects,
      editingSubject,
      cancelEdit,
      saveSubject,
      showColorPicker,
      presetGradients,
      openColorPicker,
      applyCustomColor,
      applyPresetGradient
    }
  }
}
</script>

<style scoped>
/* Base Layout */
.study-plan-page {
  min-height: 100vh;
  background: #0f0e17;
  color: #fffffe;
  position: relative;
  overflow-x: hidden;
}

/* Background Effects */
.background-effects {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.gradient-orb {
  position: absolute;
  width: 800px;
  height: 800px;
  border-radius: 50%;
  filter: blur(120px);
  opacity: 0.1;
  animation: float 20s ease-in-out infinite;
}

.orb-1 {
  top: -400px;
  left: -400px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  animation-delay: 0s;
}

.orb-2 {
  top: 50%;
  right: -400px;
  background: linear-gradient(135deg, #f093fb, #f5576c);
  animation-delay: 5s;
}

.orb-3 {
  bottom: -400px;
  left: 20%;
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  animation-delay: 10s;
}

.orb-4 {
  top: 30%;
  left: 50%;
  background: linear-gradient(135deg, #fa709a, #fee140);
  animation-delay: 15s;
}

@keyframes float {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  33% {
    transform: translate(100px, -100px) rotate(120deg);
  }
  66% {
    transform: translate(-100px, 100px) rotate(240deg);
  }
}

/* Layout Structure */
.study-plan-layout {
  position: relative;
  z-index: 1;
  padding: 2rem;
}

.scheduler-column {
  max-width: 1600px;
  margin: 0 auto;
}

/* Header */
.scheduler-header {
  margin-bottom: 3rem;
  animation: slideDown 0.6s ease-out;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.header-icon {
  position: relative;
}

.icon-wrapper {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.4);
}

.main-title {
  font-size: 3rem;
  font-weight: 800;
  margin: 0 0 0.5rem;
  letter-spacing: -0.02em;
}

.title-gradient {
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.title-accent {
  color: rgba(255, 255, 255, 0.95);
}

.subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

/* Header Stats */
.header-stats {
  display: flex;
  gap: 1.5rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem 1.75rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.stat-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.stat-label {
  display: block;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.5);
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 2rem;
}

@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .tab-navigation {
    flex-wrap: wrap;
  }
  
  .tab-btn span {
    display: none;
  }
  
  .tab-btn i {
    font-size: 1.25rem;
  }
}

/* Modern Card */
.modern-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
  margin-bottom: 2rem;
  animation: fadeInUp 0.6s ease-out;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.section-heading {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

/* Tab Navigation */
.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.tab-navigation {
  display: flex;
  gap: 0.5rem;
  padding: 0.25rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1.25rem;
  background: transparent;
  border: none;
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-btn:hover {
  background: rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.9);
}

.tab-btn.active {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
}

.tab-btn i {
  font-size: 1rem;
}

/* Tab Content */
.tab-content {
  margin-top: 2rem;
}

.tab-pane {
  animation: fadeIn 0.3s ease;
}

.tab-pane-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

/* Plans Section */
.create-plan-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white !important;
  border: none;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 10;
  text-decoration: none;
}

.create-plan-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.plans-grid {
  display: grid;
  gap: 1.5rem;
}

.plan-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.plan-card:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.plan-card.is-active {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.5);
}

.plan-card.is-expanded {
  background: rgba(102, 126, 234, 0.15);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

/* Expanded Content */
.plan-expanded {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  animation: expandIn 0.3s ease;
}

.expanded-section {
  margin-bottom: 1.5rem;
}

.expanded-section h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.expanded-section p {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.6;
  margin: 0;
}

.tasks-list {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  padding: 1rem;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
}

.task-item i {
  color: #4ade80;
  font-size: 1rem;
}

.no-tasks {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.4);
  font-style: italic;
  margin: 0;
}

.expanded-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.expanded-btn {
  flex: 1;
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.expanded-btn.edit {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
}

.expanded-btn.edit:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

.expanded-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.expanded-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

/* Expand transition */
.expand-enter-active,
.expand-leave-active {
  transition: all 0.3s ease;
  max-height: 500px;
  overflow: hidden;
}

.expand-enter-from,
.expand-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

@keyframes expandIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.plan-header {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.plan-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.plan-info {
  flex: 1;
}

.plan-info h3 {
  margin: 0 0 0.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
}

.plan-info p {
  margin: 0;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.plan-actions {
  display: flex;
  gap: 0.5rem;
}

.icon-btn {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.icon-btn:active {
  transform: scale(0.95);
}

/* Estilo específico para o botão de arquivar */
.plan-actions .icon-btn:last-child:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.3);
}

.plan-stats {
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-row {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.plan-stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.plan-progress {
  margin: 1rem 0;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.progress-percentage {
  color: #667eea;
  font-weight: 600;
}

.progress-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.progress-details {
  margin-top: 0.5rem;
  font-size: 0.8125rem;
  color: rgba(255, 255, 255, 0.6);
}

.hours-completed {
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.hours-completed i {
  font-size: 0.75rem;
  color: #667eea;
}

.plan-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
}

.deadline {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.start-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid rgba(102, 126, 234, 0.5);
  border-radius: 8px;
  color: #667eea;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.start-btn:hover {
  background: rgba(102, 126, 234, 0.3);
  transform: translateY(-1px);
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 3rem;
}

.empty-icon {
  font-size: 4rem;
  color: rgba(255, 255, 255, 0.2);
  margin-bottom: 1rem;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin: 0 0 0.5rem;
  color: rgba(255, 255, 255, 0.8);
}

.empty-state p {
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 1.5rem;
}

.empty-state .create-plan-btn {
  margin: 0 auto;
}

/* Right Column Sections */
.calendar-section,
.insights-section,
.notes-section {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.calendar-wrapper {
  margin-top: 1rem;
}

.refresh-btn {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.insight-card {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
}

.insight-card.insight-suggestion {
  border-color: rgba(102, 126, 234, 0.3);
  background: rgba(102, 126, 234, 0.05);
}

.insight-card.insight-warning {
  border-color: rgba(251, 191, 36, 0.3);
  background: rgba(251, 191, 36, 0.05);
}

.insight-card.insight-achievement {
  border-color: rgba(74, 222, 128, 0.3);
  background: rgba(74, 222, 128, 0.05);
}

.insight-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  flex-shrink: 0;
}

.insight-content {
  flex: 1;
}

.insight-content h4 {
  margin: 0 0 0.5rem;
  font-size: 1rem;
  color: white;
}

.insight-content p {
  margin: 0 0 0.75rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.insight-action {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: #667eea;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.insight-action:hover {
  gap: 0.75rem;
}

.quick-notes {
  width: 100%;
  min-height: 150px;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  font-family: inherit;
  font-size: 0.875rem;
  resize: vertical;
  margin-bottom: 1rem;
}

.quick-notes::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.save-notes-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: auto;
  padding: 0.625rem 1.25rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: white;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-notes-btn:hover {
  background: rgba(255, 255, 255, 0.15);
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-content {
  background: #1a1a2e;
  border-radius: 24px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: fadeInUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.modal-close {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.modal-body {
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
}

.form-group label i {
  font-size: 1rem;
  color: #667eea;
  opacity: 0.7;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group select option {
  background: #1a1a2e;
  color: white;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.08);
  border-color: #667eea;
}

/* Character Counter */
.char-count {
  display: block;
  text-align: right;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.4);
  margin-top: 0.25rem;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.icon-selector,
.color-selector {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.icon-option {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid transparent;
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.icon-option:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.icon-option.selected {
  border-color: #667eea;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.color-option {
  width: 48px;
  height: 48px;
  border: 2px solid transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.selected {
  border-color: white;
  box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.2);
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

.btn-cancel,
.btn-submit {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-cancel {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.6);
}

.btn-cancel:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

.btn-submit {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-submit:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

/* Difficulty Selector */
.difficulty-selector {
  display: flex;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.difficulty-option {
  flex: 1;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.difficulty-option:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.difficulty-option.selected {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  color: #667eea;
}

/* Color Section Improvements */
.color-section {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 16px;
  padding: 1.5rem;
  margin-top: 0.5rem;
}

.preset-colors h5,
.custom-color h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.color-option {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
}

.color-option i {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.color-option.selected i {
  opacity: 1;
}

.custom-color {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.custom-color-inputs {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.color-input-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.color-picker {
  width: 60px;
  height: 60px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: transparent;
  padding: 5px;
}

.color-picker:hover {
  border-color: #667eea;
  transform: scale(1.05);
}

.color-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

.gradient-arrow {
  color: rgba(255, 255, 255, 0.3);
  font-size: 1.5rem;
}

.apply-custom-btn {
  flex: 1;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.apply-custom-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.gradient-preview {
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  margin-top: 1rem;
}

/* Notification Section Improvements */
.notification-section {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 16px;
  padding: 1.5rem;
  margin-top: 0.5rem;
}

/* Switch Toggle */
.notification-toggle {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.switch-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 1rem;
}

.switch-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-slider {
  position: relative;
  width: 48px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  transition: all 0.3s ease;
}

.switch-slider::before {
  content: '';
  position: absolute;
  width: 18px;
  height: 18px;
  left: 3px;
  top: 3px;
  background: white;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.switch-input:checked + .switch-slider {
  background: #667eea;
}

.switch-input:checked + .switch-slider::before {
  transform: translateX(24px);
}

.switch-text {
  font-size: 0.9375rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
}

.notification-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.notification-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.notification-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateX(5px);
}

.checkbox-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  flex: 1;
  margin: 0;
  padding: 0;
}

.checkbox-wrapper {
  position: relative;
}

.checkbox-wrapper input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.custom-checkbox {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  display: block;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.custom-checkbox::after {
  content: '';
  position: absolute;
  left: 8px;
  top: 4px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.checkbox-wrapper input[type="checkbox"]:checked + .custom-checkbox {
  background: #667eea;
  border-color: #667eea;
}

.checkbox-wrapper input[type="checkbox"]:checked + .custom-checkbox::after {
  opacity: 1;
}

.notification-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.notification-title {
  font-size: 0.9375rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.notification-desc {
  font-size: 0.8125rem;
  color: rgba(255, 255, 255, 0.5);
  line-height: 1.4;
}

.notification-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 1.125rem;
}

.create-plan-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white !important;
  border: none;
  padding: 1rem 2rem;
  font-size: 1rem;
}

/* Time Input Wrapper */
.time-input-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.time-suggestions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.suggestion-chip {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.suggestion-chip:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.suggestion-chip.active {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  color: #667eea;
}

/* Objectives List */
.objectives-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.objective-item {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.objective-input {
  flex: 1;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.objective-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.08);
  border-color: #667eea;
}

.objective-remove {
  width: 36px;
  height: 36px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 8px;
  color: #ef4444;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.objective-remove:hover:not(:disabled) {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
}

.objective-remove:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.add-objective-btn {
  align-self: flex-start;
  padding: 0.625rem 1.25rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 10px;
  color: #667eea;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.add-objective-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateY(-1px);
}

/* Study Central Modern Design */
.study-central {
  overflow: visible;
}

.section-header-ultra {
  margin-bottom: 1.5rem;
}

.header-clean {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 2rem;
  border: none;
  border-bottom: none;
}

.header-clean::after,
.header-clean::before {
  display: none !important;
}

.title-clean {
  font-size: 1.75rem;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.95);
  margin: 0;
  letter-spacing: -0.01em;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  text-decoration: none;
  border: none;
  border-bottom: none;
}

.title-clean::after,
.title-clean::before {
  display: none !important;
}

/* Manage Subjects Button */
.manage-subjects-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
  font-weight: 500;
}

.manage-subjects-btn:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

.manage-subjects-btn i {
  transition: transform 0.3s ease;
}

.manage-subjects-btn:hover i {
  transform: rotate(90deg);
}

/* Modern Tabs Design */
.tabs-container {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 0.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-navigation-modern {
  display: flex;
  gap: 0.5rem;
  position: relative;
}


.tab-btn-modern {
  flex: 1;
  padding: 1rem;
  background: transparent;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.tab-content-wrapper {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.tab-icon {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.tab-icon i {
  font-size: 1.25rem;
  color: #666;
  transition: color 0.3s ease;
}

.tab-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5576c;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(245, 87, 108, 0.3);
}

.tab-badge.sessions {
  background: #4facfe;
  box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
}

.tab-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.tab-title {
  font-size: 1rem;
  font-weight: 600;
  color: #666;
  transition: color 0.3s ease;
}

.tab-subtitle {
  font-size: 0.875rem;
  color: #999;
  margin-top: 0.125rem;
}

.tab-btn-modern.active .tab-icon {
  background: rgba(255, 255, 255, 0.2);
}

.tab-btn-modern.active .tab-icon i,
.tab-btn-modern.active .tab-title {
  color: white;
}

.tab-btn-modern.active .tab-subtitle {
  color: rgba(255, 255, 255, 0.8);
}

.tab-btn-modern:hover:not(.active) .tab-icon {
  background: rgba(255, 255, 255, 0.15);
}

.tab-btn-modern:hover:not(.active) .tab-icon i,
.tab-btn-modern:hover:not(.active) .tab-title {
  color: #667eea;
}

/* Tab Content Modern */
.tab-content-modern {
  margin-top: 2rem;
}

.tab-pane-modern {
  animation: fadeInUp 0.4s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tab Fade Transitions */
.tab-fade-enter-active,
.tab-fade-leave-active {
  transition: all 0.3s ease;
}

.tab-fade-enter-from {
  opacity: 0;
  transform: translateX(-10px);
}

.tab-fade-leave-to {
  opacity: 0;
  transform: translateX(10px);
}

/* Creation Form Modal Styles */
.creation-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 2rem;
}

.creation-modal-container {
  background: transparent;
  width: 100%;
  max-width: 1400px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

/* Modal fade transition */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.modal-fade-enter-active .creation-modal-container {
  animation: modalSlideIn 0.3s ease-out;
}

.modal-fade-leave-active .creation-modal-container {
  animation: modalSlideOut 0.3s ease-in;
}

@keyframes modalSlideIn {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Clean Subject Manager Modal */
.subjects-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 2rem;
}

.subjects-modal-container {
  width: 100%;
  max-width: 1000px;
  max-height: 85vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

.subjects-modal-content {
  background: #1a1a2e;
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.subjects-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.title-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
}

.subjects-modal-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: white;
}

.subjects-modal-header p {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0.25rem 0 0;
}

.modal-close-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.modal-close-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: #ef4444;
  color: #ef4444;
  transform: rotate(90deg);
}

.subjects-modal-body {
  padding: 2rem 2.5rem;
}

/* Stats Overview */
.subjects-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-box {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  padding: 1.25rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.stat-box:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-1px);
}

.stat-icon {
  width: 44px;
  height: 44px;
  background: rgba(102, 126, 234, 0.15);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 1.125rem;
}

.stat-content {
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  line-height: 1;
}

.stat-label {
  display: block;
  font-size: 0.8125rem;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 0.25rem;
}

/* Clean Subject Form Section */
.subject-form-section {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 20px;
  padding: 1.75rem;
  margin-bottom: 2rem;
}

.section-header-clean {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-header-clean h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-header-clean h4 i {
  color: #667eea;
  font-size: 1rem;
}

.subject-count {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.05);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-weight: 500;
}

.cancel-edit-btn {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 1rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 10px;
  color: #ef4444;
  font-size: 0.8125rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-edit-btn:hover {
  background: rgba(239, 68, 68, 0.2);
}

/* Enhanced Section Header */
.section-header-enhanced {
  margin-bottom: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 1rem;
}

.section-title-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-header-enhanced h4 {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-header-enhanced h4 i {
  color: #667eea;
  font-size: 1.125rem;
}

.subject-counter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
}

.counter-number {
  font-size: 1.125rem;
  font-weight: 700;
  color: #667eea;
}

.counter-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

/* Form Grid */
.subject-form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.form-group-clean {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
}

.form-group-clean.full-width {
  grid-column: 1 / -1;
}

.form-group-clean label {
  font-size: 0.8125rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
}

.form-input-clean {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: white;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.form-input-clean:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.08);
  border-color: #667eea;
}

.form-input-clean::placeholder {
  color: rgba(255, 255, 255, 0.3);
}

textarea.form-input-clean {
  resize: vertical;
  min-height: 60px;
}

/* Icon Color Grid */
.icon-color-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 0.5rem;
  max-height: 200px;
  overflow-y: auto;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
}

.icon-btn-option {
  width: 100%;
  aspect-ratio: 1;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  overflow: hidden;
}

.icon-btn-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.icon-btn-option.selected {
  border: 2px solid #667eea;
}

.icon-btn-option.custom {
  border-style: dashed;
}

.icon-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
}

/* Form Actions */
.form-actions-clean {
  grid-column: 1 / -1;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.btn-clean {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.625rem 1.25rem;
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.btn-clean.secondary {
  background: rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.7);
}

.btn-clean.secondary:hover {
  background: rgba(255, 255, 255, 0.12);
  color: white;
}

.btn-clean.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-clean.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.section-header h4 {
  font-size: 1.375rem;
  font-weight: 600;
  margin: 0;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-header h4 i {
  color: #667eea;
}

.subject-form-enhanced {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.form-group-enhanced {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group-enhanced.full-width {
  grid-column: 1 / -1;
}

.form-group-enhanced label {
  font-size: 0.9rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
}

.form-input-enhanced {
  width: 100%;
  padding: 1rem 1.25rem;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 14px;
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-input-enhanced:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.1);
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input-enhanced::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

textarea.form-input-enhanced {
  resize: vertical;
  min-height: 80px;
}

/* Icon Color Selector */
.icon-color-selector {
  margin-top: 0.5rem;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 0.75rem;
}

.icon-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
}

.icon-option:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.icon-option.selected {
  border-color: transparent;
  color: white;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.icon-option i {
  font-size: 1.75rem;
}

.icon-option span {
  font-size: 0.75rem;
  text-align: center;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.btn-secondary {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.75rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

.btn-primary-large {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 2rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 14px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary-large:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
}

/* Clean Subjects List */
.subjects-list-section-enhanced {
  margin-top: 1.5rem;
}

/* Clean List Controls */
.list-controls-clean {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.search-box {
  flex: 1;
  position: relative;
}

.search-box i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.4);
  font-size: 0.875rem;
}

.search-input-clean {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  color: white;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.search-input-clean:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.05);
  border-color: #667eea;
}

.search-input-clean::placeholder {
  color: rgba(255, 255, 255, 0.3);
}

.sort-box {
  position: relative;
}

.sort-box i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.4);
  font-size: 0.875rem;
  pointer-events: none;
}

.sort-select-clean {
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  color: white;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  appearance: none;
  padding-right: 2.5rem;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgba(255,255,255,0.4)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
}

.sort-select-clean:focus {
  outline: none;
  background-color: rgba(255, 255, 255, 0.05);
  border-color: #667eea;
}

.sort-select-clean option {
  background: #1a1a2e;
  color: white;
}

/* Color Picker Modal */
.color-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;
  padding: 2rem;
}

.color-picker-modal {
  background: #1a1a2e;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  width: 100%;
  max-width: 500px;
  overflow: hidden;
}

.color-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.color-picker-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  color: white;
}

.close-color-picker {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-color-picker:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.color-picker-body {
  padding: 1.5rem;
}

.color-inputs {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.color-input-group {
  flex: 1;
  text-align: center;
}

.color-input-group label {
  display: block;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.5rem;
}

.color-input {
  width: 100%;
  height: 50px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  cursor: pointer;
  background: transparent;
}

.color-hex {
  display: block;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 0.5rem;
  font-family: monospace;
}

.gradient-preview-arrow {
  color: rgba(255, 255, 255, 0.3);
  font-size: 1.5rem;
}

.gradient-preview-large {
  height: 100px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gradient-preview-large span {
  color: white;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.preset-gradients h5 {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 0.75rem;
}

.preset-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.preset-btn {
  aspect-ratio: 1;
  border-radius: 8px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.preset-btn:hover {
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.color-picker-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.subjects-grid-enhanced {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.subject-card-enhanced {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.subject-card-enhanced:hover {
  background: rgba(255, 255, 255, 0.04);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.card-header-clean {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.25rem 1.25rem 0;
}

.subject-icon-clean {
  width: 56px;
  height: 56px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.card-actions-clean {
  display: flex;
  gap: 0.375rem;
}

.action-btn-clean {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
}

.action-btn-clean:hover {
  background: rgba(255, 255, 255, 0.08);
  color: white;
}

.action-btn-clean.active {
  background: rgba(74, 222, 128, 0.15);
  border-color: rgba(74, 222, 128, 0.3);
  color: #4ade80;
}

.action-btn-clean.delete:hover {
  background: rgba(239, 68, 68, 0.15);
  border-color: rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

.card-body {
  padding: 1rem 1.25rem 1.25rem;
}

.card-body h5 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: white;
}

.subject-details {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

.detail-item i {
  font-size: 0.625rem;
  opacity: 0.6;
}

.subject-description {
  font-size: 0.8125rem;
  color: rgba(255, 255, 255, 0.4);
  line-height: 1.5;
  margin: 0;
}

.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: rgba(255, 255, 255, 0.4);
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

@media (max-width: 768px) {
  .subjects-modal-container {
    max-width: 100%;
    padding: 0;
  }
  
  .subjects-modal-content {
    border-radius: 24px 24px 0 0;
  }
  
  .subjects-stats {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .icon-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .subjects-grid-enhanced {
    grid-template-columns: 1fr;
  }
  
  .list-controls {
    flex-direction: column;
  }
  
  .search-input {
    max-width: 100%;
  }
}

@keyframes modalSlideOut {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-50px);
    opacity: 0;
  }
}
</style>