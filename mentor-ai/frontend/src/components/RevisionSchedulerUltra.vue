<template>
  <div class="revision-ultra-container">
    <!-- Neural Background Animation -->
    <div class="neural-background">
      <canvas ref="neuralCanvas"></canvas>
      <div class="quantum-particles">
        <div v-for="i in 50" :key="`quantum-${i}`" :class="`quantum-particle particle-${i % 5}`"></div>
      </div>
    </div>

    <!-- Main Content Wrapper -->
    <div class="content-wrapper">
      <!-- Ultra Header -->
      <header class="ultra-header">
        <div class="header-glass">
          <div class="header-main">
            <div class="logo-section">
              <div class="neural-logo">
                <font-awesome-icon icon="brain" class="brain-3d" />
                <div class="neural-pulse"></div>
              </div>
              <div class="title-group">
                <h1 class="glitch-text" data-text="Sistema Neural de Revisões">
                  Sistema Neural de Revisões
                </h1>
                <p class="subtitle-animated">
                  <span class="text-gradient">Algoritmo Quântico de Aprendizagem Adaptativa</span>
                </p>
              </div>
            </div>
            
            <!-- Live Stats Dashboard -->
            <div class="live-stats-grid">
              <div class="stat-card glass-morph" v-for="stat in liveStats" :key="stat.id">
                <div class="stat-icon-wrapper">
                  <font-awesome-icon :icon="stat.icon" class="stat-icon" />
                </div>
                <div class="stat-content">
                  <div class="stat-value" :data-value="stat.value">
                    <span class="count-up">{{ stat.displayValue }}</span>
                    <span class="stat-unit">{{ stat.unit }}</span>
                  </div>
                  <div class="stat-label">{{ stat.label }}</div>
                  <div class="stat-trend" :class="stat.trend">
                    <font-awesome-icon :icon="stat.trend === 'up' ? 'arrow-up' : 'arrow-down'" />
                    <span>{{ stat.change }}%</span>
                  </div>
                </div>
                <div class="stat-chart">
                  <svg viewBox="0 0 100 40" class="mini-chart">
                    <polyline :points="stat.chartData" fill="none" stroke="url(#gradient)" stroke-width="2"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>

          <!-- AI Assistant Widget -->
          <div class="ai-assistant-widget">
            <div class="ai-avatar">
              <div class="ai-core"></div>
              <div class="ai-rings">
                <div class="ring ring-1"></div>
                <div class="ring ring-2"></div>
                <div class="ring ring-3"></div>
              </div>
            </div>
            <div class="ai-message">
              <p>{{ aiMessage }}</p>
              <div class="ai-suggestions">
                <button v-for="suggestion in aiSuggestions" 
                        :key="suggestion.id"
                        @click="applySuggestion(suggestion)"
                        class="suggestion-pill">
                  {{ suggestion.text }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Command Center -->
      <section class="command-center">
        <div class="center-grid">
          <!-- Neural Input System -->
          <div class="neural-input-section glass-panel">
            <div class="section-header">
              <h2 class="section-title">
                <font-awesome-icon icon="network-wired" />
                Neural Input System
              </h2>
              <div class="mode-selector">
                <button v-for="mode in inputModes" 
                        :key="mode.id"
                        :class="['mode-btn', { active: activeMode === mode.id }]"
                        @click="setActiveMode(mode.id)">
                  <font-awesome-icon :icon="mode.icon" />
                  <span>{{ mode.label }}</span>
                </button>
              </div>
            </div>

            <!-- Dynamic Input Forms -->
            <transition name="morph" mode="out-in">
              <!-- Theoretical Study Form -->
              <form v-if="activeMode === 'theoretical'" 
                    @submit.prevent="processTheoreticalStudy"
                    class="neural-form">
                <div class="form-field floating">
                  <input type="text" 
                         v-model="theoreticalInput.subject"
                         required
                         class="neural-input"
                         id="subject">
                  <label for="subject" class="floating-label">
                    <font-awesome-icon icon="book-medical" />
                    Matéria / Tópico
                  </label>
                  <div class="field-glow"></div>
                </div>

                <div class="form-row">
                  <div class="form-field floating">
                    <input type="datetime-local" 
                           v-model="theoreticalInput.studyTime"
                           required
                           class="neural-input">
                    <label class="floating-label">
                      <font-awesome-icon icon="clock" />
                      Data e Hora do Estudo
                    </label>
                  </div>

                  <div class="form-field">
                    <label class="field-label">Complexidade Cognitiva</label>
                    <div class="complexity-selector">
                      <div v-for="level in complexityLevels" 
                           :key="level.value"
                           :class="['complexity-option', { 
                             selected: theoreticalInput.complexity === level.value 
                           }]"
                           @click="theoreticalInput.complexity = level.value">
                        <div class="complexity-visual">
                          <div class="complexity-bars">
                            <div v-for="i in 5" 
                                 :key="i" 
                                 :class="['bar', { active: i <= level.value }]"></div>
                          </div>
                        </div>
                        <span class="complexity-label">{{ level.label }}</span>
                        <span class="complexity-desc">{{ level.description }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Advanced Options -->
                <div class="advanced-options">
                  <div class="option-group">
                    <label class="switch-label">
                      <input type="checkbox" v-model="theoreticalInput.useAI" class="neural-switch">
                      <span class="switch-slider"></span>
                      <span class="switch-text">Análise IA Avançada</span>
                    </label>
                    <label class="switch-label">
                      <input type="checkbox" v-model="theoreticalInput.collaborative" class="neural-switch">
                      <span class="switch-slider"></span>
                      <span class="switch-text">Estudo Colaborativo</span>
                    </label>
                  </div>

                  <div class="tags-input">
                    <label class="field-label">
                      <font-awesome-icon icon="tags" />
                      Tags Contextuais
                    </label>
                    <div class="tags-container">
                      <span v-for="tag in theoreticalInput.tags" 
                            :key="tag" 
                            class="tag-chip">
                        {{ tag }}
                        <button @click="removeTag(tag)" type="button" class="tag-remove">×</button>
                      </span>
                      <input type="text" 
                             @keydown.enter.prevent="addTag"
                             v-model="newTag"
                             placeholder="Adicionar tag..."
                             class="tag-input">
                    </div>
                  </div>
                </div>

                <!-- AI Prediction Display -->
                <div v-if="theoreticalInput.complexity" class="prediction-display glass-inner">
                  <h3 class="prediction-title">
                    <font-awesome-icon icon="chart-line" />
                    Previsão Neural
                  </h3>
                  <div class="prediction-metrics">
                    <div class="metric">
                      <span class="metric-label">Primeiro Contato</span>
                      <span class="metric-value">{{ calculateFirstContact() }}</span>
                    </div>
                    <div class="metric">
                      <span class="metric-label">Taxa de Retenção Esperada</span>
                      <span class="metric-value">{{ expectedRetention }}%</span>
                    </div>
                    <div class="metric">
                      <span class="metric-label">Tempo Ótimo de Revisão</span>
                      <span class="metric-value">{{ optimalRevisionTime }}</span>
                    </div>
                  </div>
                  <div class="retention-curve">
                    <canvas ref="retentionCanvas"></canvas>
                  </div>
                </div>

                <button type="submit" class="submit-btn-ultra">
                  <span class="btn-text">Processar Estudo</span>
                  <div class="btn-glow"></div>
                  <div class="btn-particles">
                    <span></span><span></span><span></span>
                  </div>
                </button>
              </form>

              <!-- Practical Review Form -->
              <form v-else-if="activeMode === 'practical'"
                    @submit.prevent="processPracticalReview"
                    class="neural-form">
                <div class="form-field">
                  <label class="field-label">
                    <font-awesome-icon icon="brain" />
                    Selecionar Matéria para Revisão
                  </label>
                  <div class="subject-selector">
                    <div v-for="subject in pendingSubjects" 
                         :key="subject.id"
                         :class="['subject-card', { 
                           selected: practicalInput.subjectId === subject.id,
                           urgent: subject.isUrgent
                         }]"
                         @click="selectSubject(subject)">
                      <div class="subject-header">
                        <h4>{{ subject.name }}</h4>
                        <span class="subject-date">{{ formatDate(subject.nextReview) }}</span>
                      </div>
                      <div class="subject-stats">
                        <div class="stat-mini">
                          <font-awesome-icon icon="history" />
                          <span>{{ subject.reviewCount }} revisões</span>
                        </div>
                        <div class="stat-mini">
                          <font-awesome-icon icon="percentage" />
                          <span>{{ subject.averageScore }}% média</span>
                        </div>
                      </div>
                      <div class="urgency-indicator" v-if="subject.isUrgent">
                        <font-awesome-icon icon="exclamation-triangle" />
                        Revisão urgente
                      </div>
                    </div>
                  </div>
                </div>

                <div class="performance-input-section" v-if="practicalInput.subjectId">
                  <h3 class="subsection-title">Registro de Desempenho</h3>
                  
                  <div class="questions-input">
                    <div class="input-group">
                      <label class="field-label">Total de Questões</label>
                      <div class="number-input-fancy">
                        <button @click="practicalInput.totalQuestions--" 
                                :disabled="practicalInput.totalQuestions <= 10"
                                type="button"
                                class="number-btn">
                          <font-awesome-icon icon="minus" />
                        </button>
                        <input type="number" 
                               v-model.number="practicalInput.totalQuestions"
                               min="10"
                               max="100"
                               class="number-display">
                        <button @click="practicalInput.totalQuestions++" 
                                :disabled="practicalInput.totalQuestions >= 100"
                                type="button"
                                class="number-btn">
                          <font-awesome-icon icon="plus" />
                        </button>
                      </div>
                    </div>

                    <div class="input-group">
                      <label class="field-label">Questões Corretas</label>
                      <div class="slider-input">
                        <input type="range" 
                               v-model.number="practicalInput.correctAnswers"
                               :max="practicalInput.totalQuestions"
                               min="0"
                               class="neural-slider">
                        <div class="slider-value">{{ practicalInput.correctAnswers }}</div>
                      </div>
                    </div>
                  </div>

                  <!-- Live Performance Visualization -->
                  <div class="performance-viz">
                    <div class="circular-progress">
                      <svg viewBox="0 0 200 200">
                        <circle cx="100" cy="100" r="90" class="progress-bg"/>
                        <circle cx="100" cy="100" r="90" 
                                class="progress-fill"
                                :stroke-dasharray="`${performancePercentage * 5.65} 565`"/>
                      </svg>
                      <div class="progress-center">
                        <span class="percentage-large">{{ performancePercentage }}%</span>
                        <span class="percentage-label">Desempenho</span>
                      </div>
                    </div>

                    <div class="performance-analysis">
                      <div class="analysis-item" :class="performanceLevel">
                        <font-awesome-icon :icon="performanceIcon" />
                        <span>{{ performanceMessage }}</span>
                      </div>
                      <div class="next-interval">
                        <font-awesome-icon icon="calendar-alt" />
                        <span>Próxima revisão: {{ nextRevisionDate }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- Mood and Context Factors -->
                  <div class="context-factors">
                    <h4 class="factors-title">Fatores Contextuais</h4>
                    <div class="factors-grid">
                      <div class="factor-item">
                        <label class="factor-label">Estado Mental</label>
                        <div class="mood-selector">
                          <button v-for="mood in moods" 
                                  :key="mood.id"
                                  :class="['mood-btn', { 
                                    active: practicalInput.mood === mood.id 
                                  }]"
                                  @click="practicalInput.mood = mood.id"
                                  type="button">
                            <font-awesome-icon :icon="mood.icon" />
                          </button>
                        </div>
                      </div>

                      <div class="factor-item">
                        <label class="factor-label">Nível de Energia</label>
                        <div class="energy-meter">
                          <div v-for="i in 5" 
                               :key="i"
                               :class="['energy-bar', { 
                                 active: i <= practicalInput.energy 
                               }]"
                               @click="practicalInput.energy = i"></div>
                        </div>
                      </div>

                      <div class="factor-item">
                        <label class="factor-label">Tempo de Estudo</label>
                        <select v-model="practicalInput.studyDuration" class="neural-select">
                          <option value="15">15 minutos</option>
                          <option value="30">30 minutos</option>
                          <option value="45">45 minutos</option>
                          <option value="60">1 hora</option>
                          <option value="90">1h30</option>
                          <option value="120">2 horas</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                <button type="submit" 
                        class="submit-btn-ultra"
                        :disabled="!practicalInput.subjectId">
                  <span class="btn-text">Registrar Revisão</span>
                  <div class="btn-glow"></div>
                </button>
              </form>

              <!-- Analytics Mode -->
              <div v-else-if="activeMode === 'analytics'" class="analytics-panel">
                <div class="analytics-header">
                  <h3 class="analytics-title">Central de Análise Neural</h3>
                  <div class="time-selector">
                    <button v-for="period in timePeriods" 
                            :key="period.id"
                            :class="['period-btn', { active: selectedPeriod === period.id }]"
                            @click="selectedPeriod = period.id">
                      {{ period.label }}
                    </button>
                  </div>
                </div>

                <!-- 3D Performance Visualization -->
                <div class="visualization-3d">
                  <canvas ref="viz3d"></canvas>
                </div>

                <!-- Metrics Grid -->
                <div class="metrics-grid">
                  <div v-for="metric in analyticsMetrics" 
                       :key="metric.id"
                       class="metric-card glass-inner">
                    <div class="metric-header">
                      <font-awesome-icon :icon="metric.icon" />
                      <h4>{{ metric.title }}</h4>
                    </div>
                    <div class="metric-value">{{ metric.value }}</div>
                    <div class="metric-chart">
                      <component :is="metric.chartComponent" :data="metric.chartData" />
                    </div>
                    <div class="metric-insight">
                      <font-awesome-icon icon="lightbulb" />
                      {{ metric.insight }}
                    </div>
                  </div>
                </div>

                <!-- Heatmap Calendar -->
                <div class="heatmap-section">
                  <h3 class="heatmap-title">Mapa de Calor de Atividades</h3>
                  <div class="heatmap-calendar">
                    <div class="heatmap-months">
                      <span v-for="month in heatmapMonths" :key="month">{{ month }}</span>
                    </div>
                    <div class="heatmap-grid">
                      <div v-for="week in 52" :key="`week-${week}`" class="heatmap-week">
                        <div v-for="day in 7" 
                             :key="`day-${week}-${day}`"
                             :class="['heatmap-day', getHeatmapIntensity(week, day)]"
                             :title="getHeatmapTooltip(week, day)">
                        </div>
                      </div>
                    </div>
                    <div class="heatmap-legend">
                      <span>Menos</span>
                      <div class="legend-squares">
                        <div v-for="i in 5" :key="i" :class="`intensity-${i}`"></div>
                      </div>
                      <span>Mais</span>
                    </div>
                  </div>
                </div>
              </div>
            </transition>
          </div>

          <!-- Smart Revision Queue -->
          <div class="revision-queue glass-panel">
            <div class="queue-header">
              <h2 class="section-title">
                <font-awesome-icon icon="layer-group" />
                Fila Neural de Revisões
              </h2>
              <div class="queue-filters">
                <button v-for="filter in queueFilters" 
                        :key="filter.id"
                        :class="['filter-btn', { active: activeFilter === filter.id }]"
                        @click="activeFilter = filter.id">
                  {{ filter.label }}
                  <span class="filter-count">{{ filter.count }}</span>
                </button>
              </div>
            </div>

            <div class="queue-timeline">
              <div class="timeline-now">
                <div class="now-indicator"></div>
                <span class="now-label">Agora</span>
              </div>

              <transition-group name="queue-item" tag="div" class="queue-items">
                <div v-for="item in filteredQueueItems" 
                     :key="item.id"
                     :class="['queue-item', item.priority, { overdue: item.isOverdue }]"
                     @click="startRevision(item)">
                  <div class="item-timeline">
                    <div class="timeline-dot"></div>
                    <div class="timeline-line"></div>
                  </div>
                  
                  <div class="item-content">
                    <div class="item-header">
                      <h4 class="item-title">{{ item.subject }}</h4>
                      <span class="item-time">{{ formatRelativeTime(item.scheduledFor) }}</span>
                    </div>
                    
                    <div class="item-meta">
                      <div class="meta-item">
                        <font-awesome-icon icon="history" />
                        <span>{{ item.reviewCount }}ª revisão</span>
                      </div>
                      <div class="meta-item">
                        <font-awesome-icon icon="chart-line" />
                        <span>{{ item.lastScore }}% último</span>
                      </div>
                      <div class="meta-item">
                        <font-awesome-icon icon="brain" />
                        <span>{{ item.retentionEstimate }}% retenção</span>
                      </div>
                    </div>

                    <div class="item-actions">
                      <button @click.stop="startRevision(item)" class="action-btn primary">
                        <font-awesome-icon icon="play" />
                        Iniciar
                      </button>
                      <button @click.stop="postponeRevision(item)" class="action-btn secondary">
                        <font-awesome-icon icon="clock" />
                        Adiar
                      </button>
                      <button @click.stop="showItemDetails(item)" class="action-btn ghost">
                        <font-awesome-icon icon="info-circle" />
                      </button>
                    </div>

                    <div v-if="item.aiRecommendation" class="ai-recommendation">
                      <font-awesome-icon icon="robot" />
                      <span>{{ item.aiRecommendation }}</span>
                    </div>
                  </div>
                </div>
              </transition-group>
            </div>
          </div>
        </div>
      </section>

      <!-- Gamification Section -->
      <section class="gamification-section">
        <div class="section-glass">
          <h2 class="section-title">
            <font-awesome-icon icon="trophy" />
            Sistema de Conquistas Neurais
          </h2>

          <div class="gamification-grid">
            <!-- Level Progress -->
            <div class="level-card glass-inner">
              <div class="level-info">
                <div class="level-badge">
                  <div class="badge-glow"></div>
                  <span class="level-number">{{ userLevel }}</span>
                </div>
                <div class="level-details">
                  <h3>{{ levelTitle }}</h3>
                  <p>{{ levelDescription }}</p>
                </div>
              </div>
              <div class="xp-progress">
                <div class="xp-bar">
                  <div class="xp-fill" :style="{ width: xpProgress + '%' }">
                    <div class="xp-glow"></div>
                  </div>
                </div>
                <div class="xp-text">
                  <span>{{ currentXP }} / {{ nextLevelXP }} XP</span>
                  <span class="xp-gain">+{{ lastXPGain }} XP</span>
                </div>
              </div>
            </div>

            <!-- Achievements -->
            <div class="achievements-showcase">
              <div v-for="achievement in recentAchievements" 
                   :key="achievement.id"
                   :class="['achievement-card', achievement.rarity, { 
                     unlocked: achievement.unlocked,
                     'newly-unlocked': achievement.isNew
                   }]">
                <div class="achievement-icon">
                  <font-awesome-icon :icon="achievement.icon" />
                  <div class="achievement-shine" v-if="achievement.isNew"></div>
                </div>
                <div class="achievement-info">
                  <h4>{{ achievement.title }}</h4>
                  <p>{{ achievement.description }}</p>
                  <div class="achievement-progress" v-if="!achievement.unlocked">
                    <div class="progress-mini">
                      <div class="progress-fill" :style="{ width: achievement.progress + '%' }"></div>
                    </div>
                    <span>{{ achievement.progress }}%</span>
                  </div>
                </div>
                <div class="achievement-reward" v-if="achievement.unlocked">
                  <font-awesome-icon icon="gem" />
                  <span>{{ achievement.reward }}</span>
                </div>
              </div>
            </div>

            <!-- Streaks and Challenges -->
            <div class="streaks-challenges">
              <div class="streak-card">
                <div class="streak-flame">
                  <font-awesome-icon icon="fire" />
                  <span class="streak-number">{{ currentStreak }}</span>
                </div>
                <h4>Sequência de Dias</h4>
                <p>Melhor: {{ bestStreak }} dias</p>
                <div class="streak-calendar">
                  <div v-for="day in streakDays" 
                       :key="day.date"
                       :class="['streak-day', { active: day.studied }]"></div>
                </div>
              </div>

              <div class="daily-challenges">
                <h4>Desafios Diários</h4>
                <div v-for="challenge in dailyChallenges" 
                     :key="challenge.id"
                     :class="['challenge-item', { completed: challenge.completed }]">
                  <font-awesome-icon :icon="challenge.icon" />
                  <span>{{ challenge.title }}</span>
                  <div class="challenge-progress">
                    <span>{{ challenge.current }}/{{ challenge.target }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Advanced Settings Float -->
      <div class="settings-float" @click="toggleSettings">
        <font-awesome-icon icon="cog" class="settings-icon" />
      </div>

      <!-- Settings Panel -->
      <transition name="slide-panel">
        <div v-if="showSettings" class="settings-panel">
          <div class="panel-header">
            <h3>Configurações Neurais</h3>
            <button @click="toggleSettings" class="close-btn">
              <font-awesome-icon icon="times" />
            </button>
          </div>
          
          <div class="settings-content">
            <!-- Algorithm Settings -->
            <div class="settings-section">
              <h4>Algoritmo de Espaçamento</h4>
              <div class="setting-item">
                <label>Fator de Dificuldade Base</label>
                <input type="range" v-model="settings.difficultyFactor" min="1" max="5" step="0.1">
                <span>{{ settings.difficultyFactor }}</span>
              </div>
              <div class="setting-item">
                <label>Multiplicador de Intervalo</label>
                <input type="range" v-model="settings.intervalMultiplier" min="0.5" max="3" step="0.1">
                <span>{{ settings.intervalMultiplier }}x</span>
              </div>
            </div>

            <!-- Notification Settings -->
            <div class="settings-section">
              <h4>Notificações Inteligentes</h4>
              <label class="switch-label">
                <input type="checkbox" v-model="settings.notifications.enabled" class="neural-switch">
                <span class="switch-slider"></span>
                <span class="switch-text">Notificações Ativadas</span>
              </label>
              <label class="switch-label">
                <input type="checkbox" v-model="settings.notifications.smart" class="neural-switch">
                <span class="switch-slider"></span>
                <span class="switch-text">Horários Inteligentes</span>
              </label>
            </div>

            <!-- AI Settings -->
            <div class="settings-section">
              <h4>Inteligência Artificial</h4>
              <label class="switch-label">
                <input type="checkbox" v-model="settings.ai.predictive" class="neural-switch">
                <span class="switch-slider"></span>
                <span class="switch-text">Análise Preditiva</span>
              </label>
              <label class="switch-label">
                <input type="checkbox" v-model="settings.ai.recommendations" class="neural-switch">
                <span class="switch-slider"></span>
                <span class="switch-text">Recomendações Personalizadas</span>
              </label>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { gsap } from '@/composables/useGSAP'

// Composables
import { useSpacingAlgorithm } from '@/composables/useSpacingAlgorithm'
import { useAIAssistant } from '@/composables/useAIAssistant'
import { useGamification } from '@/composables/useGamification'
import { useNotifications } from '@/composables/useNotifications'

export default {
  name: 'RevisionSchedulerUltra',
  
  setup() {
    const store = useStore()
    const router = useRouter()
    
    // Refs
    const neuralCanvas = ref(null)
    const retentionCanvas = ref(null)
    const viz3d = ref(null)
    
    // State
    const state = reactive({
      // Input modes
      activeMode: 'theoretical',
      inputModes: [
        { id: 'theoretical', label: 'Estudo Teórico', icon: 'book' },
        { id: 'practical', label: 'Revisão Prática', icon: 'tasks' },
        { id: 'analytics', label: 'Análise Neural', icon: 'chart-line' }
      ],
      
      // Theoretical input
      theoreticalInput: {
        subject: '',
        studyTime: '',
        complexity: null,
        tags: [],
        useAI: true,
        collaborative: false
      },
      
      // Practical input
      practicalInput: {
        subjectId: null,
        totalQuestions: 30,
        correctAnswers: 0,
        mood: 'neutral',
        energy: 3,
        studyDuration: 30
      },
      
      // Live stats
      liveStats: [
        {
          id: 'retention',
          icon: 'brain',
          label: 'Taxa de Retenção',
          value: 87,
          displayValue: 87,
          unit: '%',
          trend: 'up',
          change: 5.2,
          chartData: '0,35 20,25 40,30 60,22 80,28 100,20'
        },
        {
          id: 'reviews',
          icon: 'calendar-check',
          label: 'Revisões Hoje',
          value: 12,
          displayValue: 12,
          unit: '',
          trend: 'up',
          change: 20,
          chartData: '0,20 20,22 40,25 60,28 80,35 100,40'
        },
        {
          id: 'streak',
          icon: 'fire',
          label: 'Sequência',
          value: 15,
          displayValue: 15,
          unit: 'dias',
          trend: 'up',
          change: 0,
          chartData: '0,30 20,30 40,30 60,30 80,30 100,30'
        },
        {
          id: 'efficiency',
          icon: 'rocket',
          label: 'Eficiência',
          value: 94,
          displayValue: 94,
          unit: '%',
          trend: 'up',
          change: 8.7,
          chartData: '0,25 20,30 40,32 60,35 80,38 100,40'
        }
      ],
      
      // AI Assistant
      aiMessage: 'Analisando seus padrões de estudo...',
      aiSuggestions: [
        { id: 1, text: 'Revisar Anatomia agora' },
        { id: 2, text: 'Fazer pausa de 15min' },
        { id: 3, text: 'Alternar para questões' }
      ],
      
      // Complexity levels
      complexityLevels: [
        { value: 1, label: 'Básico', description: 'Conceitos fundamentais' },
        { value: 2, label: 'Intermediário', description: 'Aplicação prática' },
        { value: 3, label: 'Avançado', description: 'Análise complexa' },
        { value: 4, label: 'Expert', description: 'Casos clínicos' },
        { value: 5, label: 'Mestre', description: 'Pesquisa avançada' }
      ],
      
      // Settings
      showSettings: false,
      settings: {
        difficultyFactor: 2.5,
        intervalMultiplier: 1.0,
        notifications: {
          enabled: true,
          smart: true
        },
        ai: {
          predictive: true,
          recommendations: true
        }
      }
    })
    
    // Composables
    const { calculateNextInterval, getRetentionCurve } = useSpacingAlgorithm()
    const { getAISuggestion, analyzePerformance } = useAIAssistant()
    const { updateXP, checkAchievements } = useGamification()
    const { scheduleNotification } = useNotifications()
    
    // Computed
    const performancePercentage = computed(() => {
      if (state.practicalInput.totalQuestions === 0) return 0
      return Math.round((state.practicalInput.correctAnswers / state.practicalInput.totalQuestions) * 100)
    })
    
    const expectedRetention = computed(() => {
      if (!state.theoreticalInput.complexity) return 0
      return Math.max(95 - (state.theoreticalInput.complexity * 5), 70)
    })
    
    // Methods
    const initNeuralBackground = () => {
      if (!neuralCanvas.value) return
      
      const canvas = neuralCanvas.value
      const ctx = canvas.getContext('2d')
      
      // Set canvas size
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
      
      // Neural network nodes
      const nodes = []
      const connections = []
      
      // Generate nodes
      for (let i = 0; i < 50; i++) {
        nodes.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          vx: (Math.random() - 0.5) * 0.5,
          vy: (Math.random() - 0.5) * 0.5,
          radius: Math.random() * 3 + 1
        })
      }
      
      // Animation loop
      const animate = () => {
        ctx.fillStyle = 'rgba(15, 23, 42, 0.1)'
        ctx.fillRect(0, 0, canvas.width, canvas.height)
        
        // Update and draw nodes
        nodes.forEach((node, i) => {
          // Update position
          node.x += node.vx
          node.y += node.vy
          
          // Bounce off walls
          if (node.x < 0 || node.x > canvas.width) node.vx *= -1
          if (node.y < 0 || node.y > canvas.height) node.vy *= -1
          
          // Draw connections
          nodes.slice(i + 1).forEach(otherNode => {
            const distance = Math.sqrt(
              Math.pow(node.x - otherNode.x, 2) + 
              Math.pow(node.y - otherNode.y, 2)
            )
            
            if (distance < 150) {
              ctx.beginPath()
              ctx.moveTo(node.x, node.y)
              ctx.lineTo(otherNode.x, otherNode.y)
              ctx.strokeStyle = `rgba(99, 102, 241, ${1 - distance / 150})`
              ctx.lineWidth = 0.5
              ctx.stroke()
            }
          })
          
          // Draw node
          ctx.beginPath()
          ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2)
          ctx.fillStyle = 'rgba(99, 102, 241, 0.8)'
          ctx.fill()
        })
        
        requestAnimationFrame(animate)
      }
      
      animate()
    }
    
    const calculateFirstContact = () => {
      if (!state.theoreticalInput.studyTime || !state.theoreticalInput.complexity) {
        return 'Selecione a complexidade'
      }
      
      const studyDate = new Date(state.theoreticalInput.studyTime)
      const daysToAdd = state.theoreticalInput.complexity <= 2 ? 2 : 1
      studyDate.setDate(studyDate.getDate() + daysToAdd)
      
      return studyDate.toLocaleDateString('pt-BR', {
        weekday: 'long',
        day: 'numeric',
        month: 'long'
      })
    }
    
    const processTheoreticalStudy = async () => {
      // Validate and process the theoretical study
      const studyData = {
        ...state.theoreticalInput,
        timestamp: new Date(),
        userId: store.getters['auth/userId']
      }
      
      try {
        // Save to backend
        await store.dispatch('revisions/createTheoreticalStudy', studyData)
        
        // Update gamification
        updateXP(50)
        checkAchievements('theoretical_study')
        
        // Schedule first review
        const firstReviewDate = new Date(state.theoreticalInput.studyTime)
        firstReviewDate.setDate(firstReviewDate.getDate() + (state.theoreticalInput.complexity <= 2 ? 2 : 1))
        
        scheduleNotification({
          title: 'Hora da Primeira Revisão!',
          body: `Revise: ${state.theoreticalInput.subject}`,
          scheduledFor: firstReviewDate
        })
        
        // Reset form
        state.theoreticalInput = {
          subject: '',
          studyTime: '',
          complexity: null,
          tags: [],
          useAI: true,
          collaborative: false
        }
        
        // Show success animation
        showSuccessAnimation()
        
      } catch (error) {
        console.error('Error processing theoretical study:', error)
        showErrorMessage('Erro ao processar estudo teórico')
      }
    }
    
    const processPracticalReview = async () => {
      const reviewData = {
        ...state.practicalInput,
        performance: performancePercentage.value,
        timestamp: new Date(),
        userId: store.getters['auth/userId']
      }
      
      try {
        // Calculate next interval using advanced algorithm
        const nextInterval = calculateNextInterval({
          previousInterval: 7, // Get from subject data
          performance: performancePercentage.value,
          complexity: 3, // Get from subject data
          mood: state.practicalInput.mood,
          energy: state.practicalInput.energy
        })
        
        // Save review
        await store.dispatch('revisions/createPracticalReview', {
          ...reviewData,
          nextInterval
        })
        
        // Update gamification
        const xpGained = Math.round(performancePercentage.value * 0.8)
        updateXP(xpGained)
        
        if (performancePercentage.value >= 80) {
          checkAchievements('high_performance')
        }
        
        // Reset form
        state.practicalInput = {
          subjectId: null,
          totalQuestions: 30,
          correctAnswers: 0,
          mood: 'neutral',
          energy: 3,
          studyDuration: 30
        }
        
        showSuccessAnimation()
        
      } catch (error) {
        console.error('Error processing practical review:', error)
        showErrorMessage('Erro ao processar revisão prática')
      }
    }
    
    // Helper methods
    const showSuccessAnimation = () => {
      // Implementar animação de sucesso
      console.log('Success!')
    }
    
    const showErrorMessage = (message) => {
      console.error(message)
      // Implementar notificação de erro
    }
    
    const initRetentionChart = () => {
      // Implementar gráfico de retenção
      if (retentionCanvas.value) {
        const ctx = retentionCanvas.value.getContext('2d')
        // Desenhar gráfico simples
      }
    }
    
    const init3DVisualization = () => {
      // Implementar visualização 3D simplificada
      if (viz3d.value) {
        const ctx = viz3d.value.getContext('2d')
        // Desenhar visualização
      }
    }
    
    const startLiveStatsAnimation = () => {
      // Animar estatísticas
      state.liveStats.forEach(stat => {
        const targetValue = stat.value
        let currentValue = 0
        const increment = targetValue / 50
        
        const timer = setInterval(() => {
          currentValue += increment
          if (currentValue >= targetValue) {
            currentValue = targetValue
            clearInterval(timer)
          }
          stat.displayValue = Math.round(currentValue)
        }, 30)
      })
    }
    
    // Additional methods
    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('pt-BR')
    }
    
    const formatRelativeTime = (date) => {
      const now = new Date()
      const target = new Date(date)
      const diff = target - now
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (days === 0) return 'Hoje'
      if (days === 1) return 'Amanhã'
      if (days === -1) return 'Ontem'
      if (days > 0) return `Em ${days} dias`
      return `${Math.abs(days)} dias atrás`
    }
    
    const addTag = () => {
      if (state.newTag && !state.theoreticalInput.tags.includes(state.newTag)) {
        state.theoreticalInput.tags.push(state.newTag)
        state.newTag = ''
      }
    }
    
    const removeTag = (tag) => {
      const index = state.theoreticalInput.tags.indexOf(tag)
      if (index > -1) {
        state.theoreticalInput.tags.splice(index, 1)
      }
    }
    
    // Lifecycle
    onMounted(() => {
      initNeuralBackground()
      initRetentionChart()
      init3DVisualization()
      startLiveStatsAnimation()
      
      // Initialize user data
      const { initializeUserData } = useGamification()
      initializeUserData()
    })
    
    onUnmounted(() => {
      // Cleanup animations
    })
    
    return {
      // Refs
      neuralCanvas,
      retentionCanvas,
      viz3d,
      
      // Reactive state
      state,
      
      // Computed
      performancePercentage,
      expectedRetention,
      
      // Methods
      calculateFirstContact,
      processTheoreticalStudy,
      processPracticalReview,
      formatDate,
      formatRelativeTime,
      addTag,
      removeTag,
      
      // From state (for template access)
      activeMode: computed(() => state.activeMode),
      inputModes: computed(() => state.inputModes),
      theoreticalInput: computed(() => state.theoreticalInput),
      practicalInput: computed(() => state.practicalInput),
      liveStats: computed(() => state.liveStats),
      aiMessage: computed(() => state.aiMessage),
      aiSuggestions: computed(() => state.aiSuggestions),
      complexityLevels: computed(() => state.complexityLevels),
      showSettings: computed(() => state.showSettings),
      settings: computed(() => state.settings),
      newTag: ref(''),
      
      // Additional methods needed by template
      setActiveMode: (mode) => { state.activeMode = mode },
      applySuggestion: (suggestion) => { console.log('Apply suggestion:', suggestion) },
      selectSubject: (subject) => { state.practicalInput.subjectId = subject.id },
      startRevision: (item) => { console.log('Start revision:', item) },
      postponeRevision: (item) => { console.log('Postpone revision:', item) },
      showItemDetails: (item) => { console.log('Show details:', item) },
      toggleSettings: () => { state.showSettings = !state.showSettings },
      
      // Mock data
      pendingSubjects: computed(() => [
        {
          id: 1,
          name: 'Anatomia Cardiovascular',
          nextReview: new Date(),
          reviewCount: 3,
          averageScore: 85,
          isUrgent: true
        },
        {
          id: 2,
          name: 'Farmacologia Básica',
          nextReview: new Date(Date.now() + 86400000),
          reviewCount: 2,
          averageScore: 78,
          isUrgent: false
        }
      ]),
      
      optimalRevisionTime: computed(() => '25 minutos'),
      performanceLevel: computed(() => {
        const perf = performancePercentage.value
        if (perf >= 90) return 'excellent'
        if (perf >= 70) return 'good'
        if (perf >= 50) return 'fair'
        return 'poor'
      }),
      performanceIcon: computed(() => {
        const level = performancePercentage.value
        if (level >= 90) return 'star'
        if (level >= 70) return 'thumbs-up'
        if (level >= 50) return 'meh'
        return 'frown'
      }),
      performanceMessage: computed(() => {
        const level = performancePercentage.value
        if (level >= 90) return 'Excelente desempenho!'
        if (level >= 70) return 'Bom trabalho!'
        if (level >= 50) return 'Continue praticando'
        return 'Precisa melhorar'
      }),
      nextRevisionDate: computed(() => {
        const days = Math.round(performancePercentage.value / 10)
        const date = new Date()
        date.setDate(date.getDate() + days)
        return date.toLocaleDateString('pt-BR')
      }),
      
      // Moods
      moods: [
        { id: 'excellent', icon: 'grin' },
        { id: 'good', icon: 'smile' },
        { id: 'neutral', icon: 'meh' },
        { id: 'tired', icon: 'tired' },
        { id: 'stressed', icon: 'grimace' }
      ],
      
      // Time periods
      timePeriods: [
        { id: 'day', label: 'Hoje' },
        { id: 'week', label: 'Semana' },
        { id: 'month', label: 'Mês' },
        { id: 'year', label: 'Ano' }
      ],
      selectedPeriod: ref('week'),
      
      // Analytics
      analyticsMetrics: computed(() => [
        {
          id: 'retention',
          icon: 'brain',
          title: 'Taxa de Retenção',
          value: '87%',
          chartComponent: 'div',
          chartData: {},
          insight: 'Sua retenção está 12% acima da média'
        },
        {
          id: 'efficiency',
          icon: 'rocket',
          title: 'Eficiência de Estudo',
          value: '94%',
          chartComponent: 'div',
          chartData: {},
          insight: 'Você está no top 10% dos estudantes'
        }
      ]),
      
      // Heatmap
      heatmapMonths: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
      getHeatmapIntensity: (week, day) => {
        const intensity = Math.floor(Math.random() * 6)
        return `intensity-${intensity}`
      },
      getHeatmapTooltip: (week, day) => {
        return `${Math.floor(Math.random() * 5)} revisões`
      },
      
      // Queue
      activeFilter: ref('all'),
      queueFilters: [
        { id: 'all', label: 'Todas', count: 8 },
        { id: 'urgent', label: 'Urgentes', count: 3 },
        { id: 'today', label: 'Hoje', count: 5 }
      ],
      filteredQueueItems: computed(() => {
        // Mock data
        return [
          {
            id: 1,
            subject: 'Anatomia Cardiovascular',
            scheduledFor: new Date(),
            priority: 'high',
            isOverdue: true,
            reviewCount: 4,
            lastScore: 85,
            retentionEstimate: 75,
            aiRecommendation: 'Foque nos vasos sanguíneos periféricos'
          },
          {
            id: 2,
            subject: 'Fisiologia Renal',
            scheduledFor: new Date(Date.now() + 3600000),
            priority: 'medium',
            isOverdue: false,
            reviewCount: 2,
            lastScore: 92,
            retentionEstimate: 88
          }
        ]
      }),
      
      // Gamification
      userLevel: ref(12),
      levelTitle: computed(() => 'Estudante Dedicado'),
      levelDescription: computed(() => 'Continue assim para alcançar o próximo nível!'),
      xpProgress: ref(65),
      currentXP: ref(650),
      nextLevelXP: ref(1000),
      lastXPGain: ref(50),
      currentStreak: ref(15),
      bestStreak: ref(32),
      
      recentAchievements: computed(() => [
        {
          id: 'week_warrior',
          title: 'Guerreiro da Semana',
          description: '7 dias consecutivos',
          icon: 'shield-alt',
          rarity: 'rare',
          unlocked: true,
          isNew: true,
          reward: 100,
          progress: 100
        },
        {
          id: 'perfect_ten',
          title: 'Dezena Perfeita',
          description: '10 revisões com 100%',
          icon: 'star',
          rarity: 'epic',
          unlocked: false,
          progress: 70
        }
      ]),
      
      streakDays: computed(() => {
        const days = []
        for (let i = 0; i < 30; i++) {
          days.push({
            date: new Date(Date.now() - i * 86400000),
            studied: i < 15 || i > 20
          })
        }
        return days.reverse()
      }),
      
      dailyChallenges: computed(() => [
        {
          id: 'reviews',
          icon: 'tasks',
          title: 'Complete 5 revisões',
          current: 3,
          target: 5,
          completed: false
        },
        {
          id: 'perfect',
          icon: 'trophy',
          title: 'Uma revisão perfeita',
          current: 0,
          target: 1,
          completed: false
        }
      ])
    }
  }
}
</script>

<style lang="scss" scoped>
// Variables
$primary: #6366f1;
$secondary: #8b5cf6;
$success: #10b981;
$warning: #f59e0b;
$danger: #ef4444;
$dark: #1e293b;
$light: #f1f5f9;

// Mixins
@mixin glass($opacity: 0.1) {
  background: rgba(30, 41, 59, $opacity);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

@mixin glow($color: $primary) {
  box-shadow: 0 0 20px rgba($color, 0.5),
              0 0 40px rgba($color, 0.3),
              0 0 60px rgba($color, 0.1);
}

// Base Container
.revision-ultra-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  position: relative;
  overflow-x: hidden;
}

// Neural Background
.neural-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  
  canvas {
    width: 100%;
    height: 100%;
  }
}

// Quantum Particles
.quantum-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  
  .quantum-particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: $primary;
    border-radius: 50%;
    opacity: 0;
    
    @for $i from 1 through 50 {
      &:nth-child(#{$i}) {
        top: random(100) * 1%;
        left: random(100) * 1%;
        animation: quantum-float #{10 + random(20)}s #{random(5)}s infinite;
      }
    }
  }
}

@keyframes quantum-float {
  0%, 100% {
    opacity: 0;
    transform: translateY(0) scale(0);
  }
  50% {
    opacity: 0.8;
    transform: translateY(-100px) scale(1);
  }
}

// Content Wrapper
.content-wrapper {
  position: relative;
  z-index: 1;
  padding: 2rem;
  max-width: 1600px;
  margin: 0 auto;
}

// Ultra Header
.ultra-header {
  margin-bottom: 3rem;
  
  .header-glass {
    @include glass(0.8);
    border-radius: 1.5rem;
    padding: 2rem;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba($primary, 0.1) 0%, transparent 70%);
      animation: rotate 20s linear infinite;
    }
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Header Main
.header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
  position: relative;
  z-index: 1;
}

// Logo Section
.logo-section {
  display: flex;
  align-items: center;
  gap: 2rem;
  
  .neural-logo {
    position: relative;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .brain-3d {
      font-size: 3rem;
      color: $primary;
      filter: drop-shadow(0 0 20px rgba($primary, 0.5));
      animation: brain-pulse 2s ease-in-out infinite;
    }
    
    .neural-pulse {
      position: absolute;
      width: 100%;
      height: 100%;
      border: 2px solid $primary;
      border-radius: 50%;
      animation: pulse-ring 2s ease-out infinite;
    }
  }
}

@keyframes brain-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

// Title Group
.title-group {
  .glitch-text {
    font-size: 2.5rem;
    font-weight: 800;
    margin: 0;
    position: relative;
    color: white;
    letter-spacing: -1px;
    
    &::before,
    &::after {
      content: attr(data-text);
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    
    &::before {
      animation: glitch-1 0.5s infinite;
      color: $primary;
      z-index: -1;
    }
    
    &::after {
      animation: glitch-2 0.5s infinite;
      color: $secondary;
      z-index: -2;
    }
  }
  
  .subtitle-animated {
    margin: 0.5rem 0 0;
    font-size: 1.1rem;
    
    .text-gradient {
      background: linear-gradient(90deg, $primary, $secondary, $primary);
      background-size: 200% auto;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: gradient-shift 3s ease infinite;
    }
  }
}

@keyframes glitch-1 {
  0%, 100% {
    clip-path: inset(0 0 0 0);
    transform: translate(0);
  }
  20% {
    clip-path: inset(20% 0 60% 0);
    transform: translate(-2px, 2px);
  }
  40% {
    clip-path: inset(60% 0 20% 0);
    transform: translate(2px, -2px);
  }
  60% {
    clip-path: inset(40% 0 40% 0);
    transform: translate(-1px, 1px);
  }
  80% {
    clip-path: inset(80% 0 10% 0);
    transform: translate(1px, -1px);
  }
}

@keyframes glitch-2 {
  0%, 100% {
    clip-path: inset(0 0 0 0);
    transform: translate(0);
  }
  20% {
    clip-path: inset(80% 0 10% 0);
    transform: translate(2px, -2px);
  }
  40% {
    clip-path: inset(10% 0 80% 0);
    transform: translate(-2px, 2px);
  }
  60% {
    clip-path: inset(50% 0 30% 0);
    transform: translate(1px, -1px);
  }
  80% {
    clip-path: inset(30% 0 50% 0);
    transform: translate(-1px, 1px);
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// Live Stats Grid
.live-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  
  .stat-card {
    @include glass(0.5);
    border-radius: 1rem;
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    align-items: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
      @include glow($primary);
    }
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, transparent, $primary, transparent);
      animation: scan 2s ease-in-out infinite;
    }
  }
}

@keyframes scan {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.stat-icon-wrapper {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba($primary, 0.2);
  border-radius: 12px;
  
  .stat-icon {
    font-size: 1.5rem;
    color: $primary;
  }
}

.stat-content {
  flex: 1;
  
  .stat-value {
    display: flex;
    align-items: baseline;
    gap: 0.25rem;
    
    .count-up {
      font-size: 1.8rem;
      font-weight: 700;
      color: white;
    }
    
    .stat-unit {
      font-size: 1rem;
      color: rgba(white, 0.7);
    }
  }
  
  .stat-label {
    font-size: 0.875rem;
    color: rgba(white, 0.6);
    margin-top: 0.25rem;
  }
  
  .stat-trend {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.5rem;
    margin-top: 0.5rem;
    
    &.up {
      color: $success;
      background: rgba($success, 0.1);
    }
    
    &.down {
      color: $danger;
      background: rgba($danger, 0.1);
    }
  }
}

.stat-chart {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100px;
  height: 40px;
  opacity: 0.3;
  
  .mini-chart {
    width: 100%;
    height: 100%;
  }
}

// AI Assistant Widget
.ai-assistant-widget {
  margin-top: 2rem;
  display: flex;
  gap: 1.5rem;
  align-items: center;
  @include glass(0.3);
  padding: 1.5rem;
  border-radius: 1rem;
  
  .ai-avatar {
    position: relative;
    width: 60px;
    height: 60px;
    
    .ai-core {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 20px;
      height: 20px;
      background: $secondary;
      border-radius: 50%;
      box-shadow: 0 0 30px $secondary;
    }
    
    .ai-rings {
      position: absolute;
      width: 100%;
      height: 100%;
      
      .ring {
        position: absolute;
        border: 2px solid $secondary;
        border-radius: 50%;
        opacity: 0.5;
        
        &.ring-1 {
          width: 100%;
          height: 100%;
          animation: ring-pulse 2s ease-out infinite;
        }
        
        &.ring-2 {
          width: 80%;
          height: 80%;
          top: 10%;
          left: 10%;
          animation: ring-pulse 2s ease-out infinite 0.5s;
        }
        
        &.ring-3 {
          width: 60%;
          height: 60%;
          top: 20%;
          left: 20%;
          animation: ring-pulse 2s ease-out infinite 1s;
        }
      }
    }
  }
}

@keyframes ring-pulse {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.2;
  }
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
}

.ai-message {
  flex: 1;
  
  p {
    margin: 0 0 1rem;
    color: rgba(white, 0.9);
  }
  
  .ai-suggestions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    
    .suggestion-pill {
      padding: 0.5rem 1rem;
      background: rgba($secondary, 0.2);
      border: 1px solid rgba($secondary, 0.3);
      border-radius: 2rem;
      color: white;
      font-size: 0.875rem;
      transition: all 0.3s ease;
      cursor: pointer;
      
      &:hover {
        background: rgba($secondary, 0.3);
        transform: translateY(-2px);
      }
    }
  }
}

// Command Center
.command-center {
  margin-bottom: 3rem;
  
  .center-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    
    @media (max-width: 1200px) {
      grid-template-columns: 1fr;
    }
  }
}

// Glass Panel
.glass-panel {
  @include glass(0.8);
  border-radius: 1.5rem;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

// Section Header
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  
  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
  }
}

// Mode Selector
.mode-selector {
  display: flex;
  gap: 0.5rem;
  
  .mode-btn {
    padding: 0.5rem 1rem;
    background: rgba(white, 0.1);
    border: 1px solid rgba(white, 0.2);
    border-radius: 0.5rem;
    color: rgba(white, 0.7);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    
    &.active {
      background: $primary;
      border-color: $primary;
      color: white;
    }
    
    &:hover:not(.active) {
      background: rgba(white, 0.2);
      color: white;
    }
  }
}

// Neural Form
.neural-form {
  .form-field {
    margin-bottom: 1.5rem;
    position: relative;
    
    &.floating {
      .neural-input {
        padding: 1.25rem 1rem 0.75rem;
        
        &:focus ~ .floating-label,
        &:valid ~ .floating-label {
          transform: translateY(-1.5rem) scale(0.85);
          color: $primary;
        }
      }
      
      .floating-label {
        position: absolute;
        top: 1rem;
        left: 1rem;
        color: rgba(white, 0.5);
        transition: all 0.3s ease;
        pointer-events: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
    }
  }
  
  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
}

// Neural Input
.neural-input {
  width: 100%;
  padding: 1rem;
  background: rgba(white, 0.05);
  border: 1px solid rgba(white, 0.1);
  border-radius: 0.75rem;
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: $primary;
    background: rgba(white, 0.1);
    box-shadow: 0 0 0 3px rgba($primary, 0.2);
  }
  
  &::-webkit-calendar-picker-indicator {
    filter: invert(1);
  }
}

// Field Label
.field-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(white, 0.9);
  font-weight: 500;
  margin-bottom: 0.75rem;
}

// Complexity Selector
.complexity-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  
  .complexity-option {
    padding: 1rem;
    background: rgba(white, 0.05);
    border: 2px solid rgba(white, 0.1);
    border-radius: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    
    &.selected {
      background: rgba($primary, 0.2);
      border-color: $primary;
    }
    
    &:hover:not(.selected) {
      background: rgba(white, 0.1);
      transform: translateY(-2px);
    }
    
    .complexity-visual {
      margin-bottom: 0.5rem;
      
      .complexity-bars {
        display: flex;
        justify-content: center;
        gap: 0.25rem;
        height: 30px;
        align-items: flex-end;
        
        .bar {
          width: 8px;
          height: 20%;
          background: rgba(white, 0.2);
          border-radius: 2px;
          transition: all 0.3s ease;
          
          &.active {
            background: $primary;
            height: 100%;
          }
        }
      }
    }
    
    .complexity-label {
      display: block;
      font-weight: 600;
      color: white;
      margin-bottom: 0.25rem;
    }
    
    .complexity-desc {
      display: block;
      font-size: 0.75rem;
      color: rgba(white, 0.6);
    }
  }
}

// Advanced Options
.advanced-options {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(white, 0.1);
  
  .option-group {
    display: flex;
    gap: 2rem;
    margin-bottom: 1.5rem;
  }
}

// Neural Switch
.switch-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  
  .neural-switch {
    display: none;
    
    &:checked ~ .switch-slider {
      background: $primary;
      
      &::before {
        transform: translateX(20px);
      }
    }
  }
  
  .switch-slider {
    position: relative;
    width: 44px;
    height: 24px;
    background: rgba(white, 0.2);
    border-radius: 12px;
    margin-right: 0.75rem;
    transition: background 0.3s ease;
    
    &::before {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      width: 20px;
      height: 20px;
      background: white;
      border-radius: 50%;
      transition: transform 0.3s ease;
    }
  }
  
  .switch-text {
    color: rgba(white, 0.9);
  }
}

// Tags Input
.tags-input {
  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0.75rem;
    background: rgba(white, 0.05);
    border: 1px solid rgba(white, 0.1);
    border-radius: 0.75rem;
    min-height: 48px;
    align-items: center;
    
    .tag-chip {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.25rem 0.75rem;
      background: rgba($primary, 0.2);
      border: 1px solid rgba($primary, 0.3);
      border-radius: 1rem;
      color: white;
      font-size: 0.875rem;
      
      .tag-remove {
        background: none;
        border: none;
        color: rgba(white, 0.7);
        cursor: pointer;
        font-size: 1.2rem;
        line-height: 1;
        padding: 0;
        transition: color 0.3s ease;
        
        &:hover {
          color: white;
        }
      }
    }
    
    .tag-input {
      flex: 1;
      min-width: 120px;
      background: none;
      border: none;
      color: white;
      outline: none;
      
      &::placeholder {
        color: rgba(white, 0.5);
      }
    }
  }
}

// Prediction Display
.prediction-display {
  margin-top: 2rem;
  padding: 1.5rem;
  border-radius: 1rem;
  
  .prediction-title {
    font-size: 1.1rem;
    color: white;
    margin: 0 0 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .prediction-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
    
    .metric {
      text-align: center;
      
      .metric-label {
        display: block;
        font-size: 0.75rem;
        color: rgba(white, 0.6);
        margin-bottom: 0.25rem;
      }
      
      .metric-value {
        display: block;
        font-size: 1.25rem;
        font-weight: 600;
        color: $primary;
      }
    }
  }
  
  .retention-curve {
    height: 150px;
    position: relative;
    
    canvas {
      width: 100%;
      height: 100%;
    }
  }
}

// Submit Button Ultra
.submit-btn-ultra {
  width: 100%;
  padding: 1.25rem 2rem;
  background: linear-gradient(135deg, $primary, $secondary);
  border: none;
  border-radius: 0.75rem;
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    @include glow($primary);
    
    .btn-glow {
      opacity: 1;
    }
    
    .btn-particles span {
      animation: particle-float 1s ease-out infinite;
    }
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .btn-text {
    position: relative;
    z-index: 1;
  }
  
  .btn-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(white, 0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  .btn-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    
    span {
      position: absolute;
      width: 4px;
      height: 4px;
      background: white;
      border-radius: 50%;
      opacity: 0;
      
      &:nth-child(1) {
        top: 20%;
        left: 20%;
      }
      
      &:nth-child(2) {
        top: 80%;
        left: 80%;
      }
      
      &:nth-child(3) {
        top: 50%;
        left: 50%;
      }
    }
  }
}

@keyframes particle-float {
  0% {
    opacity: 0;
    transform: translate(0, 0) scale(0);
  }
  50% {
    opacity: 1;
    transform: translate(random(40) - 20px, -random(40) - 20px) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(random(80) - 40px, -random(80) - 40px) scale(0);
  }
}

// Practical Review Specific Styles
.subject-selector {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
  padding: 0.5rem;
  
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(white, 0.05);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(white, 0.2);
    border-radius: 4px;
    
    &:hover {
      background: rgba(white, 0.3);
    }
  }
  
  .subject-card {
    padding: 1.25rem;
    background: rgba(white, 0.05);
    border: 2px solid rgba(white, 0.1);
    border-radius: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.selected {
      background: rgba($primary, 0.2);
      border-color: $primary;
    }
    
    &.urgent {
      border-color: $warning;
      
      .urgency-indicator {
        color: $warning;
      }
    }
    
    &:hover:not(.selected) {
      background: rgba(white, 0.1);
      transform: translateY(-2px);
    }
    
    .subject-header {
      display: flex;
      justify-content: space-between;
      align-items: start;
      margin-bottom: 0.75rem;
      
      h4 {
        margin: 0;
        color: white;
        font-size: 1.1rem;
      }
      
      .subject-date {
        font-size: 0.75rem;
        color: rgba(white, 0.6);
      }
    }
    
    .subject-stats {
      display: flex;
      gap: 1rem;
      margin-bottom: 0.75rem;
      
      .stat-mini {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.875rem;
        color: rgba(white, 0.7);
      }
    }
    
    .urgency-indicator {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.875rem;
      font-weight: 500;
    }
  }
}

// Performance Input Section
.performance-input-section {
  margin-top: 2rem;
  
  .subsection-title {
    font-size: 1.2rem;
    color: white;
    margin: 0 0 1.5rem;
  }
  
  .questions-input {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
    
    .input-group {
      .field-label {
        margin-bottom: 0.75rem;
      }
    }
  }
}

// Number Input Fancy
.number-input-fancy {
  display: flex;
  align-items: center;
  background: rgba(white, 0.05);
  border: 1px solid rgba(white, 0.1);
  border-radius: 0.75rem;
  overflow: hidden;
  
  .number-btn {
    width: 48px;
    height: 48px;
    background: none;
    border: none;
    color: rgba(white, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover:not(:disabled) {
      background: rgba(white, 0.1);
      color: white;
    }
    
    &:disabled {
      opacity: 0.3;
      cursor: not-allowed;
    }
  }
  
  .number-display {
    flex: 1;
    text-align: center;
    background: none;
    border: none;
    color: white;
    font-size: 1.25rem;
    font-weight: 600;
    
    &::-webkit-inner-spin-button,
    &::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
  }
}

// Slider Input
.slider-input {
  position: relative;
  
  .neural-slider {
    width: 100%;
    height: 8px;
    background: rgba(white, 0.1);
    border-radius: 4px;
    outline: none;
    -webkit-appearance: none;
    
    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      width: 24px;
      height: 24px;
      background: $primary;
      border-radius: 50%;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba($primary, 0.5);
      transition: all 0.3s ease;
      
      &:hover {
        transform: scale(1.2);
      }
    }
    
    &::-moz-range-thumb {
      width: 24px;
      height: 24px;
      background: $primary;
      border-radius: 50%;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba($primary, 0.5);
      border: none;
    }
  }
  
  .slider-value {
    position: absolute;
    top: -30px;
    right: 0;
    background: rgba($primary, 0.9);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: white;
    
    &::after {
      content: '';
      position: absolute;
      top: 100%;
      right: 10px;
      border: 4px solid transparent;
      border-top-color: rgba($primary, 0.9);
    }
  }
}

// Performance Visualization
.performance-viz {
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: 2rem;
  align-items: center;
  margin: 2rem 0;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    justify-items: center;
  }
}

.circular-progress {
  position: relative;
  width: 200px;
  height: 200px;
  
  svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
    
    .progress-bg {
      fill: none;
      stroke: rgba(white, 0.1);
      stroke-width: 8;
    }
    
    .progress-fill {
      fill: none;
      stroke: url(#progress-gradient);
      stroke-width: 8;
      stroke-linecap: round;
      stroke-dasharray: 565;
      stroke-dashoffset: 565;
      transition: stroke-dasharray 0.6s ease;
    }
  }
  
  .progress-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    
    .percentage-large {
      display: block;
      font-size: 2.5rem;
      font-weight: 700;
      color: white;
    }
    
    .percentage-label {
      display: block;
      font-size: 0.875rem;
      color: rgba(white, 0.6);
      margin-top: 0.25rem;
    }
  }
}

.performance-analysis {
  .analysis-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    background: rgba(white, 0.05);
    border-radius: 0.75rem;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    
    &.excellent {
      background: rgba($success, 0.1);
      color: $success;
    }
    
    &.good {
      background: rgba($primary, 0.1);
      color: $primary;
    }
    
    &.fair {
      background: rgba($warning, 0.1);
      color: $warning;
    }
    
    &.poor {
      background: rgba($danger, 0.1);
      color: $danger;
    }
  }
  
  .next-interval {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    background: rgba($primary, 0.1);
    border: 1px solid rgba($primary, 0.2);
    border-radius: 0.75rem;
    color: white;
  }
}

// Context Factors
.context-factors {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(white, 0.1);
  
  .factors-title {
    font-size: 1.1rem;
    color: white;
    margin: 0 0 1rem;
  }
  
  .factors-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
  
  .factor-item {
    .factor-label {
      display: block;
      font-size: 0.875rem;
      color: rgba(white, 0.7);
      margin-bottom: 0.5rem;
    }
  }
}

// Mood Selector
.mood-selector {
  display: flex;
  gap: 0.5rem;
  
  .mood-btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(white, 0.05);
    border: 2px solid rgba(white, 0.1);
    border-radius: 0.5rem;
    color: rgba(white, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.active {
      background: rgba($primary, 0.2);
      border-color: $primary;
      color: $primary;
    }
    
    &:hover:not(.active) {
      background: rgba(white, 0.1);
      color: white;
    }
  }
}

// Energy Meter
.energy-meter {
  display: flex;
  gap: 0.25rem;
  
  .energy-bar {
    flex: 1;
    height: 32px;
    background: rgba(white, 0.05);
    border: 1px solid rgba(white, 0.1);
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.active {
      background: linear-gradient(to top, $primary, lighten($primary, 10%));
      border-color: $primary;
    }
    
    &:hover {
      transform: scaleY(1.1);
    }
  }
}

// Neural Select
.neural-select {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(white, 0.05);
  border: 1px solid rgba(white, 0.1);
  border-radius: 0.5rem;
  color: white;
  cursor: pointer;
  
  option {
    background: $dark;
  }
}

// Revision Queue
.revision-queue {
  height: fit-content;
  max-height: 800px;
  display: flex;
  flex-direction: column;
  
  .queue-header {
    flex-shrink: 0;
  }
  
  .queue-filters {
    display: flex;
    gap: 0.5rem;
    
    .filter-btn {
      padding: 0.5rem 1rem;
      background: rgba(white, 0.05);
      border: 1px solid rgba(white, 0.1);
      border-radius: 0.5rem;
      color: rgba(white, 0.7);
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      
      &.active {
        background: rgba($primary, 0.2);
        border-color: $primary;
        color: white;
      }
      
      .filter-count {
        background: rgba(white, 0.2);
        padding: 0.125rem 0.5rem;
        border-radius: 1rem;
        font-size: 0.75rem;
      }
    }
  }
}

.queue-timeline {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
  position: relative;
  
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(white, 0.05);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(white, 0.2);
    border-radius: 4px;
  }
  
  .timeline-now {
    position: sticky;
    top: 0;
    z-index: 10;
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    
    .now-indicator {
      flex: 1;
      height: 2px;
      background: $danger;
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: -4px;
        width: 10px;
        height: 10px;
        background: $danger;
        border-radius: 50%;
      }
    }
    
    .now-label {
      color: $danger;
      font-weight: 600;
      font-size: 0.875rem;
    }
  }
}

.queue-items {
  position: relative;
}

.queue-item {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    .item-content {
      transform: translateX(5px);
    }
  }
  
  &.high {
    .timeline-dot {
      background: $danger;
    }
  }
  
  &.medium {
    .timeline-dot {
      background: $warning;
    }
  }
  
  &.low {
    .timeline-dot {
      background: $success;
    }
  }
  
  &.overdue {
    .item-content {
      border-color: $danger;
      background: rgba($danger, 0.1);
    }
  }
  
  .item-timeline {
    position: relative;
    width: 20px;
    flex-shrink: 0;
    
    .timeline-dot {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 12px;
      height: 12px;
      background: $primary;
      border-radius: 50%;
      z-index: 1;
    }
    
    .timeline-line {
      position: absolute;
      top: 12px;
      left: 50%;
      transform: translateX(-50%);
      width: 2px;
      height: calc(100% + 1rem);
      background: rgba(white, 0.1);
    }
  }
  
  .item-content {
    flex: 1;
    padding: 1.5rem;
    background: rgba(white, 0.05);
    border: 1px solid rgba(white, 0.1);
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    
    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: start;
      margin-bottom: 0.75rem;
      
      .item-title {
        margin: 0;
        font-size: 1.1rem;
        color: white;
      }
      
      .item-time {
        font-size: 0.875rem;
        color: rgba(white, 0.6);
      }
    }
    
    .item-meta {
      display: flex;
      gap: 1.5rem;
      margin-bottom: 1rem;
      
      .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: rgba(white, 0.7);
      }
    }
    
    .item-actions {
      display: flex;
      gap: 0.75rem;
      
      .action-btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        
        &.primary {
          background: $primary;
          color: white;
          
          &:hover {
            background: lighten($primary, 5%);
            transform: translateY(-2px);
          }
        }
        
        &.secondary {
          background: rgba(white, 0.1);
          color: white;
          
          &:hover {
            background: rgba(white, 0.2);
          }
        }
        
        &.ghost {
          background: transparent;
          color: rgba(white, 0.7);
          
          &:hover {
            color: white;
          }
        }
      }
    }
    
    .ai-recommendation {
      margin-top: 1rem;
      padding: 0.75rem;
      background: rgba($secondary, 0.1);
      border-left: 3px solid $secondary;
      border-radius: 0.25rem;
      font-size: 0.875rem;
      color: rgba(white, 0.9);
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
  }
}

// Queue Item Transitions
.queue-item-enter-active,
.queue-item-leave-active {
  transition: all 0.5s ease;
}

.queue-item-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.queue-item-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

// Analytics Panel
.analytics-panel {
  .analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    
    .analytics-title {
      font-size: 1.5rem;
      color: white;
      margin: 0;
    }
  }
}

.time-selector {
  display: flex;
  gap: 0.5rem;
  
  .period-btn {
    padding: 0.5rem 1rem;
    background: rgba(white, 0.05);
    border: 1px solid rgba(white, 0.1);
    border-radius: 0.5rem;
    color: rgba(white, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.active {
      background: $primary;
      border-color: $primary;
      color: white;
    }
  }
}

// 3D Visualization
.visualization-3d {
  height: 400px;
  margin-bottom: 2rem;
  border-radius: 1rem;
  overflow: hidden;
  position: relative;
  background: rgba(black, 0.3);
  
  canvas {
    width: 100%;
    height: 100%;
  }
}

// Metrics Grid
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  
  .metric-card {
    padding: 1.5rem;
    border-radius: 1rem;
    
    .metric-header {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 1rem;
      
      svg {
        font-size: 1.5rem;
        color: $primary;
      }
      
      h4 {
        margin: 0;
        color: white;
      }
    }
    
    .metric-value {
      font-size: 2.5rem;
      font-weight: 700;
      color: white;
      margin-bottom: 1rem;
    }
    
    .metric-chart {
      height: 100px;
      margin-bottom: 1rem;
    }
    
    .metric-insight {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.875rem;
      color: rgba(white, 0.7);
      
      svg {
        color: $warning;
      }
    }
  }
}

// Heatmap Section
.heatmap-section {
  .heatmap-title {
    font-size: 1.3rem;
    color: white;
    margin: 0 0 1.5rem;
  }
}

.heatmap-calendar {
  @include glass(0.5);
  padding: 1.5rem;
  border-radius: 1rem;
  
  .heatmap-months {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0 0.5rem;
    
    span {
      font-size: 0.75rem;
      color: rgba(white, 0.6);
    }
  }
  
  .heatmap-grid {
    display: flex;
    gap: 3px;
    
    .heatmap-week {
      display: flex;
      flex-direction: column;
      gap: 3px;
      
      .heatmap-day {
        width: 14px;
        height: 14px;
        background: rgba(white, 0.05);
        border-radius: 2px;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &.intensity-1 {
          background: rgba($primary, 0.2);
        }
        
        &.intensity-2 {
          background: rgba($primary, 0.4);
        }
        
        &.intensity-3 {
          background: rgba($primary, 0.6);
        }
        
        &.intensity-4 {
          background: rgba($primary, 0.8);
        }
        
        &.intensity-5 {
          background: $primary;
        }
        
        &:hover {
          transform: scale(1.5);
          border-radius: 4px;
        }
      }
    }
  }
  
  .heatmap-legend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    font-size: 0.75rem;
    color: rgba(white, 0.6);
    
    .legend-squares {
      display: flex;
      gap: 3px;
      
      div {
        width: 12px;
        height: 12px;
        border-radius: 2px;
        
        &.intensity-1 {
          background: rgba($primary, 0.2);
        }
        
        &.intensity-2 {
          background: rgba($primary, 0.4);
        }
        
        &.intensity-3 {
          background: rgba($primary, 0.6);
        }
        
        &.intensity-4 {
          background: rgba($primary, 0.8);
        }
        
        &.intensity-5 {
          background: $primary;
        }
      }
    }
  }
}

// Gamification Section
.gamification-section {
  margin-bottom: 3rem;
  
  .section-glass {
    @include glass(0.8);
    border-radius: 1.5rem;
    padding: 2rem;
  }
}

.gamification-grid {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 2rem;
  
  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
  }
}

// Level Card
.level-card {
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  
  .level-info {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    
    .level-badge {
      position: relative;
      width: 80px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, $primary, $secondary);
      border-radius: 50%;
      
      .badge-glow {
        position: absolute;
        width: 100%;
        height: 100%;
        background: inherit;
        border-radius: 50%;
        filter: blur(20px);
        opacity: 0.5;
        z-index: -1;
      }
      
      .level-number {
        font-size: 2rem;
        font-weight: 700;
        color: white;
      }
    }
    
    .level-details {
      text-align: left;
      
      h3 {
        margin: 0 0 0.25rem;
        color: white;
        font-size: 1.3rem;
      }
      
      p {
        margin: 0;
        color: rgba(white, 0.7);
        font-size: 0.875rem;
      }
    }
  }
  
  .xp-progress {
    .xp-bar {
      height: 8px;
      background: rgba(white, 0.1);
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 0.5rem;
      
      .xp-fill {
        height: 100%;
        background: linear-gradient(90deg, $primary, $secondary);
        transition: width 0.6s ease;
        position: relative;
        
        .xp-glow {
          position: absolute;
          top: 0;
          right: 0;
          width: 20px;
          height: 100%;
          background: white;
          filter: blur(10px);
          animation: xp-shine 2s ease-in-out infinite;
        }
      }
    }
    
    .xp-text {
      display: flex;
      justify-content: space-between;
      font-size: 0.875rem;
      color: rgba(white, 0.7);
      
      .xp-gain {
        color: $success;
        font-weight: 600;
      }
    }
  }
}

@keyframes xp-shine {
  0%, 100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

// Achievements Showcase
.achievements-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  
  .achievement-card {
    padding: 1.5rem;
    background: rgba(white, 0.05);
    border: 2px solid rgba(white, 0.1);
    border-radius: 1rem;
    display: flex;
    gap: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    
    &.unlocked {
      background: rgba(white, 0.1);
      border-color: rgba(white, 0.2);
    }
    
    &.newly-unlocked {
      animation: achievement-unlock 0.6s ease;
      border-color: $primary;
      
      .achievement-shine {
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent 30%, rgba(white, 0.5) 50%, transparent 70%);
        animation: shine-sweep 0.6s ease;
      }
    }
    
    &.legendary {
      background: linear-gradient(135deg, rgba(#ffd700, 0.1), rgba(#ff6b6b, 0.1));
      border-color: #ffd700;
    }
    
    &.epic {
      background: linear-gradient(135deg, rgba($secondary, 0.1), rgba($primary, 0.1));
      border-color: $secondary;
    }
    
    &.rare {
      background: rgba($primary, 0.1);
      border-color: $primary;
    }
    
    .achievement-icon {
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(white, 0.1);
      border-radius: 0.75rem;
      font-size: 1.5rem;
      color: rgba(white, 0.5);
      position: relative;
      
      .unlocked & {
        background: rgba($primary, 0.2);
        color: $primary;
      }
    }
    
    .achievement-info {
      flex: 1;
      
      h4 {
        margin: 0 0 0.25rem;
        color: white;
        font-size: 1rem;
      }
      
      p {
        margin: 0 0 0.5rem;
        color: rgba(white, 0.6);
        font-size: 0.875rem;
      }
      
      .achievement-progress {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        
        .progress-mini {
          flex: 1;
          height: 4px;
          background: rgba(white, 0.1);
          border-radius: 2px;
          overflow: hidden;
          
          .progress-fill {
            height: 100%;
            background: $primary;
            transition: width 0.3s ease;
          }
        }
        
        span {
          font-size: 0.75rem;
          color: rgba(white, 0.7);
        }
      }
    }
    
    .achievement-reward {
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
      display: flex;
      align-items: center;
      gap: 0.25rem;
      background: rgba($warning, 0.2);
      padding: 0.25rem 0.5rem;
      border-radius: 1rem;
      font-size: 0.75rem;
      color: $warning;
      font-weight: 600;
    }
  }
}

@keyframes achievement-unlock {
  0% {
    transform: scale(0.9);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shine-sweep {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

// Streaks and Challenges
.streaks-challenges {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  
  .streak-card {
    @include glass(0.5);
    padding: 1.5rem;
    border-radius: 1rem;
    text-align: center;
    
    .streak-flame {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      position: relative;
      margin-bottom: 1rem;
      
      svg {
        font-size: 3rem;
        color: #ff6b6b;
        filter: drop-shadow(0 0 20px rgba(#ff6b6b, 0.5));
        animation: flame-flicker 2s ease-in-out infinite;
      }
      
      .streak-number {
        position: absolute;
        font-size: 1.5rem;
        font-weight: 700;
        color: white;
      }
    }
    
    h4 {
      margin: 0 0 0.5rem;
      color: white;
      font-size: 1.1rem;
    }
    
    p {
      margin: 0 0 1rem;
      color: rgba(white, 0.6);
      font-size: 0.875rem;
    }
    
    .streak-calendar {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 3px;
      
      .streak-day {
        aspect-ratio: 1;
        background: rgba(white, 0.05);
        border-radius: 4px;
        
        &.active {
          background: #ff6b6b;
        }
      }
    }
  }
}

@keyframes flame-flicker {
  0%, 100% {
    transform: scale(1) rotate(-2deg);
  }
  25% {
    transform: scale(1.1) rotate(2deg);
  }
  50% {
    transform: scale(0.95) rotate(-1deg);
  }
  75% {
    transform: scale(1.05) rotate(1deg);
  }
}

.daily-challenges {
  @include glass(0.5);
  padding: 1.5rem;
  border-radius: 1rem;
  
  h4 {
    margin: 0 0 1rem;
    color: white;
    font-size: 1.1rem;
  }
  
  .challenge-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: rgba(white, 0.05);
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    
    &.completed {
      background: rgba($success, 0.1);
      color: $success;
      
      svg {
        color: $success;
      }
    }
    
    svg {
      font-size: 1.25rem;
      color: rgba(white, 0.5);
    }
    
    span {
      flex: 1;
      font-size: 0.875rem;
      color: rgba(white, 0.9);
    }
    
    .challenge-progress {
      font-size: 0.75rem;
      color: rgba(white, 0.6);
      font-weight: 600;
    }
  }
}

// Settings Float Button
.settings-float {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 60px;
  height: 60px;
  background: $primary;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba($primary, 0.5);
  transition: all 0.3s ease;
  z-index: 100;
  
  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 30px rgba($primary, 0.7);
    
    .settings-icon {
      animation: rotate 2s linear infinite;
    }
  }
  
  .settings-icon {
    font-size: 1.5rem;
    color: white;
  }
}

// Settings Panel
.settings-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background: rgba($dark, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: -5px 0 20px rgba(black, 0.5);
  z-index: 1000;
  overflow-y: auto;
  
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-bottom: 1px solid rgba(white, 0.1);
    
    h3 {
      margin: 0;
      color: white;
      font-size: 1.3rem;
    }
    
    .close-btn {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(white, 0.1);
      border: none;
      border-radius: 0.5rem;
      color: white;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(white, 0.2);
      }
    }
  }
  
  .settings-content {
    padding: 2rem;
    
    .settings-section {
      margin-bottom: 2rem;
      
      h4 {
        margin: 0 0 1rem;
        color: white;
        font-size: 1.1rem;
      }
      
      .setting-item {
        margin-bottom: 1rem;
        
        label {
          display: block;
          color: rgba(white, 0.7);
          font-size: 0.875rem;
          margin-bottom: 0.5rem;
        }
        
        input[type="range"] {
          width: 100%;
          margin-bottom: 0.25rem;
        }
        
        span {
          color: $primary;
          font-weight: 600;
        }
      }
    }
  }
}

// Slide Panel Transition
.slide-panel-enter-active,
.slide-panel-leave-active {
  transition: transform 0.3s ease;
}

.slide-panel-enter-from,
.slide-panel-leave-to {
  transform: translateX(100%);
}

// Morph Transition
.morph-enter-active,
.morph-leave-active {
  transition: all 0.5s ease;
}

.morph-enter-from {
  opacity: 0;
  transform: scale(0.9) rotateX(-10deg);
}

.morph-leave-to {
  opacity: 0;
  transform: scale(1.1) rotateX(10deg);
}

// Glass Inner
.glass-inner {
  @include glass(0.3);
  border-radius: 0.75rem;
}

// Responsive Design
@media (max-width: 768px) {
  .content-wrapper {
    padding: 1rem;
  }
  
  .ultra-header {
    .header-main {
      flex-direction: column;
      text-align: center;
      
      .logo-section {
        flex-direction: column;
      }
    }
    
    .live-stats-grid {
      grid-template-columns: 1fr 1fr;
    }
  }
  
  .command-center {
    .center-grid {
      grid-template-columns: 1fr;
    }
  }
  
  .settings-panel {
    width: 100%;
  }
  
  .gamification-grid {
    grid-template-columns: 1fr;
  }
}

// Print styles
@media print {
  .neural-background,
  .quantum-particles,
  .settings-float,
  .ai-assistant-widget {
    display: none;
  }
  
  .revision-ultra-container {
    background: white;
    color: black;
  }
}
</style>