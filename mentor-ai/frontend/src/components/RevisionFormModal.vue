<template>
  <div class="modal-overlay" @click.self="$emit('close')">
    <div class="modal-content">
      <div class="modal-header">
        <h2>
          <i class="fas fa-calendar-plus"></i>
          Nova Revisão
        </h2>
        <button @click="$emit('close')" class="close-button">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body">
        <form @submit.prevent="handleSubmit">
          <div class="form-row">
            <div class="form-group">
              <label>
                <i class="fas fa-book"></i>
                T<PERSON><PERSON><PERSON> da Revisão
              </label>
              <input 
                v-model="formData.title" 
                type="text" 
                placeholder="Ex: Revisão de Anatomia"
                required
                class="form-input"
              >
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>
                <i class="fas fa-tag"></i>
                Tipo
              </label>
              <select v-model="formData.type" class="form-select" required>
                <option value="Revisão">Revisão</option>
                <option value="Prova">Prova</option>
                <option value="Trabalho">Trabalho</option>
                <option value="Apresentação">Apresentação</option>
                <option value="Exercício">Exercício</option>
              </select>
            </div>

            <div class="form-group">
              <label>
                <i class="fas fa-exclamation-circle"></i>
                Prioridade
              </label>
              <select v-model="formData.priority" class="form-select" required>
                <option value="Alta">Alta</option>
                <option value="Média">Média</option>
                <option value="Baixa">Baixa</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>
                <i class="fas fa-calendar"></i>
                Data
              </label>
              <input 
                v-model="formData.date" 
                type="date" 
                required
                class="form-input"
              >
            </div>

            <div class="form-group">
              <label>
                <i class="fas fa-clock"></i>
                Horário
              </label>
              <div class="time-inputs">
                <input 
                  v-model="formData.startTime" 
                  type="time" 
                  required
                  class="form-input"
                  placeholder="Início"
                >
                <span class="time-separator">até</span>
                <input 
                  v-model="formData.endTime" 
                  type="time" 
                  required
                  class="form-input"
                  placeholder="Fim"
                >
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group full-width">
              <label>
                <i class="fas fa-graduation-cap"></i>
                Disciplina
              </label>
              <select v-model="formData.subject" class="form-select">
                <option value="">Selecione uma disciplina</option>
                <option v-for="subject in subjects" :key="subject.id" :value="subject.id">
                  {{ subject.name }}
                </option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label>
              <i class="fas fa-align-left"></i>
              Descrição (opcional)
            </label>
            <textarea 
              v-model="formData.description" 
              rows="3" 
              placeholder="Adicione detalhes sobre esta revisão..."
              class="form-textarea"
            ></textarea>
          </div>

          <div class="form-actions">
            <button type="button" @click="$emit('close')" class="cancel-button">
              <i class="fas fa-times"></i>
              Cancelar
            </button>
            <button type="submit" class="submit-button">
              <i class="fas fa-save"></i>
              Criar Revisão
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RevisionFormModal',
  props: {
    subjects: {
      type: Array,
      default: () => []
    },
    initialDate: {
      type: Date,
      default: () => new Date()
    }
  },
  data() {
    return {
      formData: {
        title: '',
        type: 'Revisão',
        priority: 'Média',
        date: this.formatDate(this.initialDate),
        startTime: '08:00',
        endTime: '09:00',
        subject: '',
        description: ''
      }
    };
  },
  methods: {
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    
    handleSubmit() {
      const event = {
        id: Date.now().toString(),
        title: this.formData.title,
        type: this.formData.type,
        priority: this.formData.priority,
        start: `${this.formData.date}T${this.formData.startTime}:00`,
        end: `${this.formData.date}T${this.formData.endTime}:00`,
        subject: this.formData.subject,
        description: this.formData.description,
        isRevision: true
      };
      
      this.$emit('save', event);
    }
  }
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background: #0f1724;
  border-radius: 16px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: rgba(99, 102, 241, 0.1);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #e4e6eb;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modal-header i {
  color: #6366f1;
}

.close-button {
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  transform: scale(1.05);
}

.modal-body {
  padding: 2rem;
  overflow-y: auto;
  max-height: calc(90vh - 80px);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #94a3b8;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

label i {
  color: #6366f1;
  font-size: 0.875rem;
}

.form-input,
.form-select,
.form-textarea {
  padding: 0.875rem 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid rgba(148, 163, 184, 0.1);
  border-radius: 10px;
  color: #e4e6eb;
  font-size: 0.875rem;
  transition: all 0.2s;
  width: 100%;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: rgba(99, 102, 241, 0.5);
  background: rgba(30, 41, 59, 0.8);
}

.time-inputs {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.time-separator {
  color: #64748b;
  font-size: 0.875rem;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.cancel-button,
.submit-button {
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cancel-button {
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
}

.cancel-button:hover {
  background: rgba(148, 163, 184, 0.2);
  transform: translateY(-1px);
}

.submit-button {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.submit-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
}

/* Responsive */
@media (max-width: 600px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    width: 95%;
  }
}
</style>