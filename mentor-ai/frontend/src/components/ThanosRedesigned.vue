<template>
  <div class="thanos-app">
    <!-- Modern Navigation Header -->
    <nav class="nav-header">
      <div class="nav-container">
        <div class="brand-section">
          <div class="brand-icon">
            <div class="oracle-logo">
              <svg viewBox="0 0 24 24" fill="none" class="oracle-svg">
                <!-- Círculo principal com animação -->
                <circle cx="12" cy="12" r="8" stroke="currentColor" stroke-width="2.5" fill="none" class="main-ring"/>
                
                <!-- Pequenos pontos orbitais -->
                <g class="orbital-dots">
                  <circle r="1.5" fill="currentColor" opacity="0.6" class="dot-1">
                    <animateMotion dur="4s" repeatCount="indefinite" path="M 12,4 A 8,8 0 1,1 12,4"/>
                  </circle>
                  <circle r="1" fill="currentColor" opacity="0.4" class="dot-2">
                    <animateMotion dur="6s" repeatCount="indefinite" path="M 12,4 A 8,8 0 1,0 12,4"/>
                  </circle>
                </g>
              </svg>
            </div>
          </div>
          <div class="brand-text">
            <h1>
              <i class="fas fa-microscope brand-symbol"></i>
              Oracle<span class="ai-suffix">AI</span>
              <i class="fas fa-dna brand-symbol-end"></i>
            </h1>
            <span class="tagline">
              <i class="fas fa-stethoscope"></i>
              Assistente Médico Inteligente
              <i class="fas fa-heartbeat pulse-icon"></i>
            </span>
          </div>
          
          <!-- Botão de colapso movido para a brand section -->
          <button class="nav-collapse-btn" @click="toggleSidebar">
            <div class="nav-collapse-icon-wrapper">
              <div class="nav-custom-collapse-logo">
                <svg viewBox="0 0 24 24" fill="none" class="nav-collapse-svg">
                  <g class="nav-sidebar-lines" :class="{ 'collapsed': sidebarCollapsed }">
                    <line x1="4" y1="8" x2="10" y2="8" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                    <line x1="4" y1="12" x2="8" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                    <line x1="4" y1="16" x2="10" y2="16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                  </g>
                  <g class="nav-arrow-direction" :class="{ 'collapsed': sidebarCollapsed }">
                    <path d="M16 10L18 12L16 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </g>
                </svg>
              </div>
            </div>
          </button>
        </div>
        
        <div class="nav-actions">
          <button 
            class="nav-status-card" 
            :class="{ 'connected': isInitialized }" 
            @click="toggleStatusInfo"
            :title="getStatusTooltip()"
          >
            <div class="status-icon-container">
              <svg class="status-logo" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2" fill="none" opacity="0.7"/>
                <circle cx="12" cy="12" r="4" fill="currentColor" :class="{ 'active': isInitialized }"/>
              </svg>
            </div>
            <span class="status-text">{{ getStatusText() }}</span>
            <div class="status-glow"></div>
          </button>
        </div>
      </div>
    </nav>

    <!-- Main Layout -->
    <div class="main-layout">
      <!-- Sidebar -->
      <aside class="sidebar" :class="{ 'collapsed': sidebarCollapsed }">
        <div class="sidebar-header">
          <!-- Header da sidebar sem botão de colapso -->
        </div>

        <div class="sidebar-content">
          <!-- Sessions -->
          <div class="sessions-section">
            <h3 class="section-title" v-if="!sidebarCollapsed">Histórico de Conversas</h3>
            <div class="sessions-list">
              <div v-if="!chatSessions.length" class="empty-sessions">
                <div class="empty-sessions-icon">
                  <svg class="empty-logo" viewBox="0 0 24 24" fill="none">
                    <rect x="4" y="3" width="16" height="18" rx="2" stroke="currentColor" stroke-width="1.5" fill="none"/>
                    <path d="M8 7h8M8 11h6M8 15h4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                    <circle cx="18" cy="6" r="3" fill="currentColor" opacity="0.3"/>
                    <path d="M16.5 6L17.5 7L19.5 5" stroke="white" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <div class="empty-pulse"></div>
                </div>
                <div v-if="!sidebarCollapsed" class="empty-sessions-text">
                  <span>Nenhuma conversa</span>
                  <small>Inicie uma nova sessão</small>
                </div>
              </div>
              
              <div 
                v-for="(session, index) in chatSessions" 
                :key="index"
                class="session-item"
                :class="{ 'active': selectedSessionIndex === index }"
                @click="selectSession(index)"
              >
                <div class="session-icon">
                  <svg class="session-logo" viewBox="0 0 24 24" fill="none" v-html="getSessionLogo(session)">
                  </svg>
                  <div class="session-indicator" v-if="session.messages && session.messages.length > 0"></div>
                </div>
                <div class="session-content" v-if="!sidebarCollapsed">
                  <div class="session-title">{{ session.title || `Conversa ${index + 1}` }}</div>
                  <div class="session-meta">{{ session.messages?.length || 0 }} mensagens</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Action Cards at Bottom -->
          <div class="bottom-actions">
            <button 
              class="action-card exportar" 
              @click="exportCurrentSession"
              :disabled="!currentSession"
            >
              <div class="action-icon">
                <svg class="action-logo" viewBox="0 0 24 24" fill="none">
                  <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z" fill="currentColor" opacity="0.2"/>
                  <path d="M14 2V8H20" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M16 13L19 16L16 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M19 16H9" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                  <circle cx="7" cy="16" r="1" fill="currentColor"/>
                  <path d="M6 12H12" stroke="currentColor" stroke-width="1" stroke-linecap="round"/>
                </svg>
              </div>
              <span v-if="!sidebarCollapsed">Exportar Conversa</span>
              <div class="action-glow"></div>
            </button>
            
            <button 
              class="action-card limpar" 
              @click="clearAllSessions"
              :disabled="!chatSessions.length"
            >
              <div class="action-icon">
                <svg class="action-logo" viewBox="0 0 24 24" fill="none">
                  <path d="M3 6H21" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                  <path d="M19 6V20C19 21.1 18.1 22 17 22H7C5.9 22 5 21.1 5 20V6" stroke="currentColor" stroke-width="1.5"/>
                  <path d="M8 6V4C8 2.9 8.9 2 10 2H14C15.1 2 16 2.9 16 4V6" stroke="currentColor" stroke-width="1.5"/>
                  <line x1="10" y1="11" x2="10" y2="17" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                  <line x1="14" y1="11" x2="14" y2="17" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                  <circle cx="18" cy="4" r="2" fill="currentColor" opacity="0.6"/>
                  <path d="M16.5 4L17.5 5L19.5 3" stroke="white" stroke-width="1" stroke-linecap="round"/>
                </svg>
              </div>
              <span v-if="!sidebarCollapsed">Limpar Tudo</span>
              <div class="action-glow"></div>
            </button>
            
            <button 
              class="action-card configuracoes" 
              @click="showSettings = !showSettings"
            >
              <div class="action-icon">
                <svg class="action-logo" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="1.5" fill="none"/>
                  <path d="M19.4 15C19.1 15.8 18.7 16.6 18.2 17.3L19.8 18.9L18.9 19.8L17.3 18.2C16.6 18.7 15.8 19.1 15 19.4V21H13V19.4C12.2 19.1 11.4 18.7 10.7 18.2L9.1 19.8L8.2 18.9L9.8 17.3C9.3 16.6 8.9 15.8 8.6 15H7V13H8.6C8.9 12.2 9.3 11.4 9.8 10.7L8.2 9.1L9.1 8.2L10.7 9.8C11.4 9.3 12.2 8.9 13 8.6V7H15V8.6C15.8 8.9 16.6 9.3 17.3 9.8L18.9 8.2L19.8 9.1L18.2 10.7C18.7 11.4 19.1 12.2 19.4 13H21V15H19.4Z" fill="currentColor" opacity="0.3"/>
                  <circle cx="9" cy="6" r="2" fill="currentColor" opacity="0.6"/>
                  <path d="M7 6H11" stroke="currentColor" stroke-width="1" stroke-linecap="round"/>
                </svg>
              </div>
              <span v-if="!sidebarCollapsed">Configurações</span>
              <div class="action-glow"></div>
            </button>
          </div>
        </div>
      </aside>

      <!-- Main Content Area -->
      <main class="content-area">
        <!-- Welcome Screen -->
        <div v-if="!isInitialized" class="welcome-screen">
          <!-- Animated Background -->
          <div class="welcome-bg">
            <div class="grid-pattern">
              <div class="grid-line" v-for="i in 20" :key="'h-' + i" :style="{ top: i * 5 + '%' }"></div>
              <div class="grid-line vertical" v-for="i in 20" :key="'v-' + i" :style="{ left: i * 5 + '%' }"></div>
            </div>
            <div class="floating-particles">
              <div class="particle" v-for="i in 15" :key="i" :style="getParticleStyle(i)"></div>
            </div>
          </div>
          
          <div class="welcome-content">
            <div class="welcome-hero">
              <div class="welcome-icon-group">
                <div class="welcome-icon main">
                  <i class="fas fa-brain"></i>
                  <div class="icon-pulse"></div>
                </div>
                <div class="welcome-icon secondary icon-1">
                  <i class="fas fa-stethoscope"></i>
                </div>
                <div class="welcome-icon secondary icon-2">
                  <i class="fas fa-file-medical"></i>
                </div>
                <div class="welcome-icon secondary icon-3">
                  <i class="fas fa-chart-line"></i>
                </div>
              </div>
              
              <h2>
                <span class="title-gradient">Oracle AI</span>
                <span class="title-sub">Assistente Médico Inteligente</span>
              </h2>
              
              <p class="welcome-description">
                Configure seu assistente com IA avançada para transformar seus estudos médicos. 
                Analise documentos e tenha conversas inteligentes sobre qualquer tópico médico.
              </p>
            </div>
            
            <div class="welcome-actions">
              <button class="cta-button primary jarvis-oracle-button" @click="openModalWithAnimation">
                <div class="jarvis-button-core">
                  <!-- Arc Reactor Core -->
                  <div class="button-arc-reactor">
                    <div class="arc-center"></div>
                    <div class="arc-ring arc-ring-1"></div>
                    <div class="arc-ring arc-ring-2"></div>
                    <div class="arc-ring arc-ring-3"></div>
                  </div>
                  
                  <!-- Neural Grid Background -->
                  <div class="neural-grid-background">
                    <div class="neural-node" v-for="i in 12" :key="'node-' + i" :style="{ 
                      '--delay': i * 0.3 + 's',
                      '--angle': i * 30 + 'deg'
                    }"></div>
                  </div>
                  
                  <!-- Energy Streams -->
                  <div class="energy-streams">
                    <div class="energy-stream stream-1"></div>
                    <div class="energy-stream stream-2"></div>
                    <div class="energy-stream stream-3"></div>
                  </div>
                  
                  <!-- Data Particles -->
                  <div class="data-particles">
                    <div class="data-particle" v-for="i in 8" :key="'particle-' + i" :style="{
                      '--delay': i * 0.4 + 's',
                      '--duration': (2 + i * 0.3) + 's'
                    }"></div>
                  </div>
                  
                  <!-- HUD Corners -->
                  <div class="hud-corners">
                    <div class="hud-corner top-left"></div>
                    <div class="hud-corner top-right"></div>
                    <div class="hud-corner bottom-left"></div>
                    <div class="hud-corner bottom-right"></div>
                  </div>
                  
                  <!-- Holographic Scanning Line -->
                  <div class="scanning-line"></div>
                  
                  <!-- Main Content -->
                  <div class="button-content">
                    <i class="fas fa-rocket button-icon"></i>
                    <span class="button-text">Inicializar Oracle</span>
                  </div>
                </div>
                <div class="button-glow"></div>
              </button>
            </div>
          </div>
        </div>

        <!-- Chat Interface -->
        <div v-else class="chat-interface">
          <!-- Chat Header -->
          <div class="chat-header">
            <div class="session-info">
              <input 
                v-model="currentSession.title"
                class="session-title-input"
                placeholder="Título da sessão..."
                @blur="updateSessionTitle"
              />
              <div class="session-meta">
                <span class="provider-tag">{{ selectedProvider }}</span>
                <span class="model-tag">{{ selectedModel }}</span>
              </div>
            </div>
            
            <div class="chat-actions">
              <button class="icon-btn" @click="clearHistory" title="Limpar histórico">
                <i class="fas fa-trash"></i>
              </button>
              <button class="icon-btn" @click="showSettings = !showSettings" title="Configurações">
                <i class="fas fa-cog"></i>
              </button>
            </div>
          </div>

          <!-- Document Summary -->
          <div v-if="documentSummary && showSummary" class="document-summary">
            <div class="summary-header">
              <i class="fas fa-file-medical"></i>
              <span>Documento Carregado</span>
            </div>
            <div class="summary-content">{{ documentSummary }}</div>
          </div>

          <!-- Messages Container -->
          <div class="messages-container" ref="chatContainer">
            <div v-if="!currentSession?.messages?.length" class="empty-state">
              <div class="empty-animation">
                <div class="floating-brain">
                  <i class="fas fa-brain"></i>
                  <div class="brain-waves">
                    <div class="wave wave-1"></div>
                    <div class="wave wave-2"></div>
                    <div class="wave wave-3"></div>
                  </div>
                </div>
              </div>
              <h3>Sua sessão está pronta!</h3>
              <p>Comece uma conversa inteligente com o Oracle AI</p>
              <div class="starter-suggestions">
                <button 
                  v-for="suggestion in starterSuggestions" 
                  :key="suggestion.id"
                  @click="insertPrompt(suggestion.prompt)"
                  class="suggestion-chip"
                >
                  <i class="fas" :class="suggestion.icon"></i>
                  {{ suggestion.text }}
                </button>
              </div>
            </div>
            
            <div v-else class="messages-list">
              <div 
                v-for="(message, index) in currentSession.messages" 
                :key="index"
                class="message-wrapper"
                :class="message.type"
              >
                <div class="message-avatar">
                  <i class="fas" :class="message.type === 'user' ? 'fa-user' : 'fa-brain'"></i>
                </div>
                <div class="message-content">
                  <div class="message-text" v-html="message.text"></div>
                  <div class="message-timestamp">
                    {{ formatTimestamp(message.timestamp) }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Input Area -->
          <div class="input-area">
            <div class="input-container">
              <div class="input-wrapper">
                <textarea
                  v-model="userInput"
                  placeholder="Digite sua pergunta..."
                  class="message-input"
                  rows="1"
                  :disabled="isLoading"
                  @keydown.enter.prevent="sendMessage"
                  @input="autoResize"
                ></textarea>
                
                <div class="input-actions">
                  <button 
                    class="send-button"
                    @click="sendMessage"
                    :disabled="!userInput.trim() || isLoading"
                  >
                    <i v-if="isLoading" class="fas fa-spinner fa-spin"></i>
                    <i v-else class="fas fa-paper-plane"></i>
                  </button>
                </div>
              </div>
              
              <!-- Quick Prompts -->
              <div class="quick-prompts" v-if="!isLoading && !userInput.trim()">
                <button 
                  v-for="prompt in quickPrompts.slice(0, 3)" 
                  :key="prompt.id"
                  class="prompt-chip"
                  @click="insertPrompt(prompt.prompt)"
                >
                  {{ prompt.title }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- Oracle Initialization Animation -->
    <div v-if="modalAnimationActive" class="oracle-initialization-overlay" :class="modalAnimationPhase">
      <div class="oracle-vision-container">
        <!-- Círculo central (olho do Oracle) -->
        <div class="oracle-eye">
          <div class="eye-iris">
            <div class="eye-pupil">
              <i class="fas fa-eye"></i>
            </div>
          </div>
          <div class="eye-scan-lines"></div>
        </div>
        
        <!-- Anéis de dados orbitando -->
        <div class="data-rings">
          <div class="data-ring ring-1">
            <div class="data-node" v-for="i in 6" :key="'ring1-' + i"></div>
          </div>
          <div class="data-ring ring-2">
            <div class="data-node" v-for="i in 8" :key="'ring2-' + i"></div>
          </div>
          <div class="data-ring ring-3">
            <div class="data-node" v-for="i in 10" :key="'ring3-' + i"></div>
          </div>
        </div>
        
        <!-- Feixes de luz diagnóstica -->
        <div class="diagnostic-beams">
          <div class="beam beam-1"></div>
          <div class="beam beam-2"></div>
          <div class="beam beam-3"></div>
          <div class="beam beam-4"></div>
        </div>
        
        <!-- Partículas de conhecimento -->
        <div class="knowledge-particles">
          <div class="particle" v-for="i in 15" :key="'particle-' + i" :style="getKnowledgeParticleStyle(i)">
            <i class="fas fa-plus-circle"></i>
          </div>
        </div>
      </div>
      
      <div class="oracle-status">
        <h3 v-if="modalAnimationPhase === 'preparing'" class="oracle-status-text">
          <i class="fas fa-search"></i>
          Scanning Medical Database...
        </h3>
        <h3 v-else-if="modalAnimationPhase === 'materializing'" class="oracle-status-text">
          <i class="fas fa-brain"></i>
          Activating Neural Networks...
        </h3>
        <h3 v-else-if="modalAnimationPhase === 'expanding'" class="oracle-status-text">
          <i class="fas fa-eye"></i>
          Oracle Vision Online...
        </h3>
        <h3 v-else class="oracle-status-text">
          <i class="fas fa-check-circle"></i>
          Oracle Ready for Diagnosis!
        </h3>
        
        <div class="medical-data-stream">
          <div class="data-symbol" v-for="i in 12" :key="i" :style="{ '--i': i }">
            {{ getMedicalSymbol(i) }}
          </div>
        </div>
      </div>
    </div>

    <!-- Initialization Modal -->
    <div v-if="showInitModal" class="modal-overlay" @click="closeInitModal" :class="{ 'with-animation': modalAnimationActive }">
      <div class="modal-container" @click.stop :class="{ 'animating': modalAnimationActive }">
        
        <!-- Oracle Initialization Animation -->
        <!-- Simple Loading Animation -->
        <div v-if="thanosAnimationActive" class="simple-loading-overlay">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
          <div class="loading-text">
            <span v-if="thanosAnimationPhase === 'gathering'">Inicializando...</span>
            <span v-else-if="thanosAnimationPhase === 'assembling'">Configurando...</span>
            <span v-else-if="thanosAnimationPhase === 'power'">Conectando...</span>
            <span v-else-if="thanosAnimationPhase === 'complete'">Concluído!</span>
            <span v-else>Processando...</span>
          </div>
        </div>
        
        <div class="modal-header">
          <h3>Configurar Oracle AI</h3>
          <button class="modal-close" @click="closeInitModal">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="modal-content">
          <div class="config-tabs">
            <button 
              v-for="tab in configTabs" 
              :key="tab.id"
              class="tab-button"
              :class="{ 'active': activeConfigTab === tab.id }"
              @click="activeConfigTab = tab.id"
            >
              <i class="fas" :class="tab.icon"></i>
              {{ tab.name }}
            </button>
          </div>

          <div class="tab-content">
            <!-- Document Tab -->
            <div v-show="activeConfigTab === 'document'" class="config-section">
              <div class="form-group">
                <label>Tipo de Documento</label>
                <select v-model="selectedDocumentType" class="form-select">
                  <option value="PDF">PDF</option>
                  <option value="Texto">Texto</option>
                  <option value="Site">Site</option>
                  <option value="Youtube">YouTube</option>
                </select>
              </div>

              <div v-if="selectedDocumentType === 'Site' || selectedDocumentType === 'Youtube'" class="form-group">
                <label>URL</label>
                <input 
                  v-model="documentUrl"
                  type="url"
                  placeholder="Cole a URL aqui..."
                  class="form-input"
                />
              </div>

              <div v-else class="form-group">
                <label>Arquivo</label>
                <div class="file-upload-area" @drop.prevent="handleFileDrop" @dragover.prevent>
                  <input 
                    type="file"
                    @change="handleFileUpload"
                    :accept="getFileAccept()"
                    class="file-input"
                    id="file-input"
                  />
                  <label for="file-input" class="file-label">
                    <i class="fas fa-cloud-upload"></i>
                    <span>Clique ou arraste o arquivo aqui</span>
                  </label>
                </div>
                <div v-if="documentFile" class="file-info">
                  <i class="fas fa-file"></i>
                  <span>{{ documentFile.name }}</span>
                </div>
              </div>
            </div>

            <!-- AI Model Tab -->
            <div v-show="activeConfigTab === 'model'" class="config-section">
              <div class="form-group">
                <label>Provedor de IA</label>
                <select v-model="selectedProvider" class="form-select">
                  <option v-for="provider in availableProviders" :key="provider" :value="provider">
                    {{ provider }}
                  </option>
                </select>
              </div>

              <div class="form-group">
                <label>Modelo</label>
                <select v-model="selectedModel" class="form-select">
                  <option v-for="model in getModelsForProvider(selectedProvider)" :key="model" :value="model">
                    {{ model }}
                  </option>
                </select>
              </div>

              <div class="form-group">
                <label>API Key</label>
                <div class="api-key-input">
                  <input 
                    v-model="apiKey"
                    :type="showApiKey ? 'text' : 'password'"
                    placeholder="Sua API key aqui..."
                    class="form-input"
                  />
                  <button 
                    type="button"
                    class="toggle-visibility"
                    @click="showApiKey = !showApiKey"
                  >
                    <i class="fas" :class="showApiKey ? 'fa-eye-slash' : 'fa-eye'"></i>
                  </button>
                </div>
                <small class="form-hint">
                  {{ apiKeyMasked || 'Sua chave será armazenada localmente de forma segura' }}
                </small>
              </div>
            </div>

            <!-- Advanced Tab -->
            <div v-show="activeConfigTab === 'advanced'" class="config-section">
              <div class="toggle-group">
                <label class="toggle-label">
                  <input type="checkbox" v-model="showSummary" class="toggle-input">
                  <span class="toggle-slider"></span>
                  <span>Mostrar resumo do documento</span>
                </label>
              </div>

              <div class="toggle-group">
                <label class="toggle-label">
                  <input type="checkbox" v-model="useRAG" class="toggle-input">
                  <span class="toggle-slider"></span>
                  <span>Usar RAG (Retrieval Augmented Generation)</span>
                </label>
              </div>

              <div v-if="useRAG" class="rag-settings">
                <div class="form-group">
                  <label>Tamanho do Chunk</label>
                  <input 
                    v-model.number="ragSettings.chunkSize"
                    type="number"
                    min="100"
                    max="2000"
                    class="form-input"
                  />
                </div>

                <div class="form-group">
                  <label>Threshold de Similaridade</label>
                  <input 
                    v-model.number="ragSettings.similarityThreshold"
                    type="range"
                    min="0.5"
                    max="1"
                    step="0.05"
                    class="form-range"
                  />
                  <span class="range-value">{{ ragSettings.similarityThreshold }}</span>
                </div>
              </div>

              <div class="form-group">
                <label>Idioma</label>
                <select v-model="language" class="form-select">
                  <option value="pt">Português</option>
                  <option value="en">English</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button class="btn btn-secondary" @click="closeInitModal">
            Cancelar
          </button>
          <button 
            class="btn btn-primary"
            @click="initializeThanos"
            :disabled="isLoading"
          >
            <i v-if="isLoading" class="fas fa-spinner fa-spin"></i>
            {{ isLoading ? 'Inicializando...' : 'Inicializar' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Settings Panel -->
    <div v-if="showSettings" class="settings-overlay" @click="showSettings = false">
      <div class="settings-panel" @click.stop>
        <div class="settings-header">
          <h3>Configurações</h3>
          <button class="close-btn" @click="showSettings = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="settings-content">
          <div class="setting-item">
            <label>Temperatura do Modelo</label>
            <input 
              v-model.number="modelSettings.temperature"
              type="range"
              min="0"
              max="1"
              step="0.1"
              class="form-range"
            />
            <span>{{ modelSettings.temperature }}</span>
          </div>

          <div class="setting-item">
            <label>Máximo de Tokens</label>
            <input 
              v-model.number="modelSettings.maxTokens"
              type="number"
              min="100"
              max="4000"
              class="form-input"
            />
          </div>

          <div class="toggle-group">
            <label class="toggle-label">
              <input type="checkbox" v-model="modelSettings.streamResponses" class="toggle-input">
              <span class="toggle-slider"></span>
              <span>Respostas em tempo real</span>
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- Status Info Modal -->
    <div v-if="showStatusInfo" class="modal-overlay" @click="showStatusInfo = false">
      <div class="modal-container status-modal" @click.stop>
        <div class="modal-header">
          <h3>
            <i class="fas" :class="getStatusIcon()"></i>
            Status do Sistema
          </h3>
          <button class="modal-close" @click="showStatusInfo = false">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="modal-content">
          <div class="status-details">
            <div class="status-item">
              <div class="status-item-label">
                <i class="fas fa-brain"></i>
                Sistema Oracle
              </div>
              <div class="status-item-value" :class="{ 'online': isInitialized }">
                {{ isInitialized ? 'Inicializado' : 'Não Inicializado' }}
              </div>
            </div>

            <div class="status-item">
              <div class="status-item-label">
                <i class="fas fa-robot"></i>
                Modelo IA
              </div>
              <div class="status-item-value">
                {{ isInitialized ? `${selectedProvider} - ${selectedModel}` : 'Não Configurado' }}
              </div>
            </div>

            <div class="status-item">
              <div class="status-item-label">
                <i class="fas fa-comments"></i>
                Sessões Ativas
              </div>
              <div class="status-item-value">
                {{ chatSessions.length }} {{ chatSessions.length === 1 ? 'sessão' : 'sessões' }}
              </div>
            </div>

            <div class="status-item">
              <div class="status-item-label">
                <i class="fas fa-clock"></i>
                Última Atividade
              </div>
              <div class="status-item-value">
                {{ getLastActivity() }}
              </div>
            </div>

            <div v-if="isInitialized" class="status-item">
              <div class="status-item-label">
                <i class="fas fa-file-alt"></i>
                Documento Carregado
              </div>
              <div class="status-item-value">
                {{ documentSummary ? 'Sim' : 'Nenhum' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, ref, onMounted, nextTick, watch } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { thanosService } from '@/services/thanos'
import { playSound } from '@/utils/sound'

// Configuration constants
const CONFIG_MODELOS = {
  'OpenAI': {
    'modelos': ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo']
  },
  'Groq': {
    'modelos': ['llama-3.1-70b-versatile', 'gemma2-9b-it', 'mixtral-8x7b-32768']
  },
  'Anthropic': {
    'modelos': ['claude-3-5-sonnet-20241022', 'claude-3-5-sonnet-20240620', 'claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307']
  }
}

const QUICK_PROMPTS = [
  { id: 1, title: 'Explique conceito', prompt: 'Explique este conceito médico de forma didática:' },
  { id: 2, title: 'Caso clínico', prompt: 'Analise este caso clínico e sugira diagnósticos diferenciais:' },
  { id: 3, title: 'Resumir conteúdo', prompt: 'Faça um resumo dos pontos principais deste conteúdo:' }
]

export default {
  name: 'ThanosRedesigned',
  setup() {
    const store = useStore()
    const router = useRouter()

    // UI State
    const sidebarCollapsed = ref(false)
    const showInitModal = ref(false)
    const showSettings = ref(false)
    const showApiKey = ref(false)
    const showHelpModal = ref(false)
    const showStatusInfo = ref(false)
    const activeConfigTab = ref('document')
    const isLoading = ref(false)

    // Animação Modal
    const modalAnimationActive = ref(false)
    const modalAnimationPhase = ref('')
    
    // Animação Thanos (durante inicialização)
    const thanosAnimationActive = ref(false)
    const thanosAnimationPhase = ref('')
    const infinityStones = ref([
      { name: 'Mind', color: '#FFD700', active: false },
      { name: 'Soul', color: '#FF6B35', active: false },
      { name: 'Time', color: '#00FF41', active: false },
      { name: 'Space', color: '#0099FF', active: false },
      { name: 'Reality', color: '#FF0040', active: false },
      { name: 'Power', color: '#9932CC', active: false }
    ])

    // Chat State
    const userInput = ref('')
    const selectedSessionIndex = ref(0)

    // Configuration State
    const selectedDocumentType = ref('PDF')
    const documentUrl = ref('')
    const documentFile = ref(null)
    const selectedProvider = ref('OpenAI')
    const selectedModel = ref('gpt-4o')
    const apiKey = ref('')
    const language = ref('pt')
    const showSummary = ref(true)
    const useRAG = ref(false)
    const documentSummary = ref('')

    // Advanced Settings
    const ragSettings = ref({
      enabled: false,
      chunkSize: 1000,
      chunkOverlap: 200,
      similarityThreshold: 0.75,
      maxChunks: 5
    })

    const modelSettings = ref({
      temperature: 0.7,
      maxTokens: 2000,
      streamResponses: false
    })

    // Refs
    const chatContainer = ref(null)

    // Computed Properties
    const isInitialized = computed(() => store.getters['thanos/isInitialized'] || false)
    const chatSessions = computed(() => store.getters['thanos/allSessions'] || [])
    const currentSession = computed(() => chatSessions.value[selectedSessionIndex.value] || null)
    const availableProviders = computed(() => Object.keys(CONFIG_MODELOS))
    const quickPrompts = computed(() => QUICK_PROMPTS)

    const configTabs = computed(() => [
      { id: 'document', name: 'Documento', icon: 'fa-file' },
      { id: 'model', name: 'Modelo IA', icon: 'fa-brain' },
      { id: 'advanced', name: 'Avançado', icon: 'fa-cog' }
    ])

    const totalMessages = computed(() => {
      return chatSessions.value.reduce((total, session) => {
        return total + (session.messages?.length || 0)
      }, 0)
    })

    const starterSuggestions = computed(() => [
      { id: 1, text: 'Explicar conceito', prompt: 'Explique este conceito médico de forma didática:', icon: 'fa-lightbulb' },
      { id: 2, text: 'Analisar caso', prompt: 'Analise este caso clínico:', icon: 'fa-stethoscope' },
      { id: 3, text: 'Resumir conteúdo', prompt: 'Faça um resumo dos pontos principais:', icon: 'fa-clipboard-list' },
      { id: 4, text: 'Criar questões', prompt: 'Crie questões de múltipla escolha sobre:', icon: 'fa-question-circle' }
    ])

    const apiKeyMasked = computed(() => {
      if (!apiKey.value) return ''
      try {
        return apiKey.value.length > 8 
          ? apiKey.value.substring(0, 4) + '*'.repeat(apiKey.value.length - 8) + apiKey.value.substring(apiKey.value.length - 4)
          : '*'.repeat(apiKey.value.length)
      } catch (error) {
        return ''
      }
    })

    const canInitialize = computed(() => {
      const hasDocument = documentUrl.value || documentFile.value
      const hasApiKey = apiKey.value.trim()
      const hasModel = selectedModel.value
      return hasDocument && hasApiKey && hasModel
    })

    // Methods
    const toggleSidebar = () => {
      sidebarCollapsed.value = !sidebarCollapsed.value
    }

    const openModalWithAnimation = async () => {
      // Iniciar animação de abertura
      modalAnimationActive.value = true
      modalAnimationPhase.value = 'preparing'
      
      // Pequeno delay para preparação
      await new Promise(resolve => setTimeout(resolve, 300))
      
      // Fase de materialização
      modalAnimationPhase.value = 'materializing'
      showInitModal.value = true
      
      // Fase de expansão
      setTimeout(() => {
        modalAnimationPhase.value = 'expanding'
      }, 400)
      
      // Fase final - ready
      setTimeout(() => {
        modalAnimationPhase.value = 'ready'
        
        // Limpar animação após um tempo
        setTimeout(() => {
          modalAnimationActive.value = false
          modalAnimationPhase.value = ''
        }, 800)
      }, 1000)
    }

    const closeInitModal = () => {
      showInitModal.value = false
      modalAnimationActive.value = false
      modalAnimationPhase.value = ''
    }

    const toggleStatusInfo = () => {
      showStatusInfo.value = !showStatusInfo.value
    }

    const getStatusIcon = () => {
      if (isInitialized.value) {
        return 'fa-wifi'
      }
      return 'fa-wifi-slash'
    }

    const getStatusText = () => {
      if (isInitialized.value) {
        return 'Conectado'
      }
      return 'Desconectado'
    }

    const getStatusTooltip = () => {
      if (isInitialized.value) {
        return 'Sistema inicializado e funcionando. Clique para ver detalhes.'
      }
      return 'Sistema não inicializado. Clique para ver detalhes e configurar.'
    }

    const getLastActivity = () => {
      if (!chatSessions.value.length) {
        return 'Nenhuma atividade'
      }
      
      let lastTimestamp = 0
      chatSessions.value.forEach(session => {
        if (session.messages && session.messages.length > 0) {
          const lastMessage = session.messages[session.messages.length - 1]
          if (lastMessage.timestamp > lastTimestamp) {
            lastTimestamp = lastMessage.timestamp
          }
        }
      })
      
      if (lastTimestamp === 0) {
        return 'Nenhuma mensagem'
      }
      
      const now = Date.now()
      const diff = now - lastTimestamp
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(minutes / 60)
      const days = Math.floor(hours / 24)
      
      if (days > 0) {
        return `${days} ${days === 1 ? 'dia' : 'dias'} atrás`
      } else if (hours > 0) {
        return `${hours} ${hours === 1 ? 'hora' : 'horas'} atrás`
      } else if (minutes > 0) {
        return `${minutes} ${minutes === 1 ? 'minuto' : 'minutos'} atrás`
      } else {
        return 'Agora mesmo'
      }
    }

    const getSessionIcon = (session) => {
      const messageCount = session.messages?.length || 0
      
      if (messageCount === 0) {
        return 'fa-plus-circle'  // Nova conversa
      } else if (messageCount < 5) {
        return 'fa-stethoscope'  // Consulta inicial
      } else if (messageCount < 15) {
        return 'fa-heartbeat'    // Conversa ativa
      } else {
        return 'fa-user-md'      // Sessão completa
      }
    }

    const getSessionLogo = (session) => {
      const messageCount = session.messages?.length || 0
      
      if (messageCount === 0) {
        // Nova conversa - Ícone de iniciar
        return `
          <circle cx="12" cy="12" r="8" stroke="currentColor" stroke-width="1.5" fill="none" opacity="0.3"/>
          <path d="M10 8L16 12L10 16Z" fill="currentColor"/>
          <circle cx="18" cy="6" r="2" fill="currentColor" opacity="0.6"/>
        `
      } else if (messageCount < 5) {
        // Consulta inicial - Estetoscópio
        return `
          <path d="M11 2V6C11 7.1 11.9 8 13 8H15C16.1 8 17 8.9 17 10V14" stroke="currentColor" stroke-width="1.5" fill="none"/>
          <path d="M7 2V6C7 7.1 7.9 8 9 8H11" stroke="currentColor" stroke-width="1.5" fill="none"/>
          <circle cx="17" cy="17" r="3" stroke="currentColor" stroke-width="1.5" fill="none"/>
          <circle cx="17" cy="17" r="1" fill="currentColor"/>
          <circle cx="7" cy="4" r="2" stroke="currentColor" stroke-width="1.5" fill="currentColor" opacity="0.3"/>
          <circle cx="11" cy="4" r="2" stroke="currentColor" stroke-width="1.5" fill="currentColor" opacity="0.3"/>
        `
      } else if (messageCount < 15) {
        // Conversa ativa - Batimento cardíaco
        return `
          <path d="M2 12H6L8 6L12 18L14 12H18L20 16H22" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round"/>
          <circle cx="12" cy="10" r="2" fill="currentColor" opacity="0.6"/>
          <path d="M8 4C6 4 4 6 4 8C4 12 12 20 12 20S20 12 20 8C20 6 18 4 16 4C14 4 12 6 12 8C12 6 10 4 8 4Z" fill="currentColor" opacity="0.2"/>
        `
      } else {
        // Sessão completa - Médico especialista
        return `
          <circle cx="12" cy="8" r="4" stroke="currentColor" stroke-width="1.5" fill="currentColor" opacity="0.3"/>
          <path d="M6 21V19C6 16.8 7.8 15 10 15H14C16.2 15 18 16.8 18 19V21" stroke="currentColor" stroke-width="1.5" fill="none"/>
          <path d="M12 12V16" stroke="currentColor" stroke-width="1.5"/>
          <path d="M10 14H14" stroke="currentColor" stroke-width="1.5"/>
          <circle cx="18" cy="6" r="2" fill="currentColor"/>
          <path d="M16.5 6L17.5 7L19.5 5" stroke="white" stroke-width="1" stroke-linecap="round"/>
        `
      }
    }

    const getStatusSubtext = () => {
      if (isInitialized.value) {
        return 'NEURAL ACTIVE'
      }
      return 'INIT REQUIRED'
    }

    const getBinaryDigit = () => {
      return Math.random() > 0.5 ? '1' : '0'
    }

    const getQuantumStyle = (index) => {
      const positions = [
        { top: '10%', left: '15%', delay: '0s', duration: '3s' },
        { top: '25%', left: '85%', delay: '0.5s', duration: '4s' },
        { top: '50%', left: '10%', delay: '1s', duration: '3.5s' },
        { top: '75%', left: '80%', delay: '1.5s', duration: '4.5s' },
        { top: '90%', left: '20%', delay: '2s', duration: '3s' },
        { top: '20%', left: '60%', delay: '2.5s', duration: '4s' },
        { top: '65%', left: '70%', delay: '3s', duration: '3.5s' },
        { top: '40%', left: '40%', delay: '3.5s', duration: '4s' }
      ]
      
      const pos = positions[index % positions.length]
      return {
        top: pos.top,
        left: pos.left,
        animationDelay: pos.delay,
        animationDuration: pos.duration
      }
    }

    const formatTimestamp = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString()
    }

    const autoResize = (event) => {
      const textarea = event.target
      textarea.style.height = 'auto'
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
    }

    const clearInput = () => {
      userInput.value = ''
    }

    const insertPrompt = (prompt) => {
      userInput.value = prompt + ' '
      nextTick(() => {
        const textarea = document.querySelector('.message-input')
        if (textarea) {
          textarea.focus()
          autoResize({ target: textarea })
        }
      })
    }

    const scrollToBottom = () => {
      nextTick(() => {
        const el = chatContainer.value
        if (el) {
          el.scrollTop = el.scrollHeight
        }
      })
    }

    const getModelsForProvider = (provider) => {
      return CONFIG_MODELOS[provider]?.modelos || []
    }

    const getFileAccept = () => {
      const accepts = {
        'PDF': '.pdf',
        'Texto': '.txt,.md,.doc,.docx',
        'CSV': '.csv',
        'Imagem': '.jpg,.jpeg,.png,.gif'
      }
      return accepts[selectedDocumentType.value] || '*'
    }

    const handleFileUpload = (event) => {
      documentFile.value = event.target.files[0]
    }

    const handleFileDrop = (event) => {
      const files = event.dataTransfer.files
      if (files.length > 0) {
        documentFile.value = files[0]
      }
    }

    const selectSession = (index) => {
      selectedSessionIndex.value = index
      scrollToBottom()
    }

    const createNewSession = async () => {
      await store.dispatch('thanos/createNewSession')
      selectedSessionIndex.value = chatSessions.value.length - 1
    }

    const updateSessionTitle = async () => {
      if (currentSession.value) {
        await store.dispatch('thanos/updateSessionTitle', {
          sessionIndex: selectedSessionIndex.value,
          title: currentSession.value.title
        })
      }
    }

    const clearHistory = async () => {
      if (currentSession.value && confirm('Tem certeza que deseja limpar o histórico desta sessão?')) {
        await store.dispatch('thanos/clearSession', selectedSessionIndex.value)
      }
    }

    const initializeThanos = async () => {
      try {
        // Iniciar animação épica SEMPRE (para teste)
        startThanosInitAnimation()
        isLoading.value = true

        // Se não tem API key, apenas simula o processo
        if (!apiKey.value) {
          console.log('Modo demo: Simulando inicialização...')
          // Simular tempo de processamento
          await new Promise(resolve => setTimeout(resolve, 9000))
          
          // Completar animação com sucesso
          completeThanosInitAnimation()
          
          setTimeout(() => {
            showInitModal.value = false
          }, 1500)
          playSound('success')
          return
        }

        const config = {
          provider: selectedProvider.value,
          model: selectedModel.value,
          apiKey: apiKey.value,
          documentType: selectedDocumentType.value.toLowerCase(),
          useRAG: useRAG.value,
          showSummary: showSummary.value,
          language: language.value,
          ragSettings: ragSettings.value,
          modelSettings: modelSettings.value
        }

        let document = null

        if (selectedDocumentType.value === 'Site' && documentUrl.value) {
          document = await thanosService.loadFromUrl(documentUrl.value, 'site')
        } else if (selectedDocumentType.value === 'Youtube' && documentUrl.value) {
          document = await thanosService.loadFromUrl(documentUrl.value, 'youtube')
        } else if (documentFile.value) {
          document = await thanosService.uploadDocument(documentFile.value, selectedDocumentType.value.toLowerCase())
        }

        if (document) {
          const context = { ...config, document, timestamp: new Date().toISOString() }
          
          if (showSummary.value && typeof document === 'string') {
            documentSummary.value = document.substring(0, 250) + '...'
          } else if (showSummary.value && document && document.summary) {
            documentSummary.value = document.summary
          }

          await store.dispatch('thanos/initializeThanos', context)
          localStorage.setItem(`thanos_api_key_${selectedProvider.value}`, apiKey.value)
          
          if (!chatSessions.value.length) {
            await createNewSession()
          }

          // Completar animação com sucesso
          completeThanosInitAnimation()
          
          setTimeout(() => {
            showInitModal.value = false
          }, 1500)
          playSound('success')
        }
      } catch (error) {
        console.error('Erro ao inicializar Oracle:', error)
        failThanosInitAnimation()
        playSound('error')
      } finally {
        setTimeout(() => {
          isLoading.value = false
        }, 2000)
      }
    }

    // Funções de Animação Thanos
    const startThanosInitAnimation = () => {
      thanosAnimationActive.value = true
      thanosAnimationPhase.value = 'gathering'
      
      // Resetar as pedras
      infinityStones.value.forEach(stone => {
        stone.active = false
      })
      
      // Ativar as pedras sequencialmente
      infinityStones.value.forEach((stone, index) => {
        setTimeout(() => {
          stone.active = true
          playSound('notification')
        }, index * 800)
      })
      
      // Fase de assemblagem do gauntlet
      setTimeout(() => {
        thanosAnimationPhase.value = 'assembling'
      }, 5000)
      
      // Fase de poder
      setTimeout(() => {
        thanosAnimationPhase.value = 'power'
      }, 7000)
    }

    const completeThanosInitAnimation = () => {
      thanosAnimationPhase.value = 'complete'
      
      setTimeout(() => {
        thanosAnimationPhase.value = 'snap'
      }, 500)
      
      setTimeout(() => {
        thanosAnimationActive.value = false
        thanosAnimationPhase.value = ''
      }, 2500)
    }

    const failThanosInitAnimation = () => {
      thanosAnimationPhase.value = 'failed'
      
      setTimeout(() => {
        thanosAnimationActive.value = false
        thanosAnimationPhase.value = ''
        infinityStones.value.forEach(stone => {
          stone.active = false
        })
      }, 2000)
    }

    const sendMessage = async () => {
      if (!currentSession.value || !userInput.value.trim()) return

      try {
        isLoading.value = true
        
        await store.dispatch('thanos/addUserMessage', {
          sessionIndex: selectedSessionIndex.value,
          text: userInput.value
        })

        const response = await thanosService.sendMessage(userInput.value, {
          provider: selectedProvider.value,
          model: selectedModel.value,
          apiKey: apiKey.value,
          settings: modelSettings.value
        })

        await store.dispatch('thanos/addAIMessage', {
          sessionIndex: selectedSessionIndex.value,
          text: response
        })

        userInput.value = ''
        scrollToBottom()
        playSound('success')
        
      } catch (error) {
        console.error('Erro ao enviar mensagem:', error)
        playSound('error')
      } finally {
        isLoading.value = false
      }
    }

    const exportCurrentSession = async () => {
      if (!currentSession.value) return
      
      try {
        const sessionData = {
          title: currentSession.value.title,
          messages: currentSession.value.messages,
          timestamp: new Date().toISOString(),
          metadata: {
            provider: selectedProvider.value,
            model: selectedModel.value,
            messageCount: currentSession.value.messages?.length || 0
          }
        }

        const blob = new Blob([JSON.stringify(sessionData, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `thanos-session-${Date.now()}.json`
        a.click()
        URL.revokeObjectURL(url)
        
        playSound('success')
      } catch (error) {
        console.error('Erro ao exportar sessão:', error)
        playSound('error')
      }
    }

    const createFlashcardsFromSession = async () => {
      if (!currentSession.value) return
      
      try {
        router.push({
          name: 'Flashcards',
          query: { import: 'thanos', sessionId: selectedSessionIndex.value }
        })
      } catch (error) {
        console.error('Erro ao criar flashcards:', error)
      }
    }

    const importSession = () => {
      try {
        const input = document.createElement('input')
        input.type = 'file'
        input.accept = '.json'
        input.onchange = (event) => {
          const file = event.target.files[0]
          if (file) {
            const reader = new FileReader()
            reader.onload = (e) => {
              try {
                const sessionData = JSON.parse(e.target.result)
                store.dispatch('thanos/importSession', sessionData)
                playSound('success')
              } catch (error) {
                console.error('Erro ao importar sessão:', error)
                playSound('error')
              }
            }
            reader.readAsText(file)
          }
        }
        input.click()
      } catch (error) {
        console.error('Erro ao importar sessão:', error)
        playSound('error')
      }
    }

    const clearAllSessions = async () => {
      if (confirm('Tem certeza que deseja limpar todas as sessões? Esta ação não pode ser desfeita.')) {
        try {
          await store.dispatch('thanos/clearAllSessions')
          selectedSessionIndex.value = 0
          playSound('success')
        } catch (error) {
          console.error('Erro ao limpar sessões:', error)
          playSound('error')
        }
      }
    }

    const getMedicalSymbol = (index) => {
      return '' // Remove symbols for JARVIS-style animation
    }
    
    const getKnowledgeParticleStyle = (index) => {
      const angle = (index * 360) / 15
      const radius = 120 + Math.random() * 80
      const x = Math.cos(angle * Math.PI / 180) * radius
      const y = Math.sin(angle * Math.PI / 180) * radius
      return {
        left: `calc(50% + ${x}px)`,
        top: `calc(50% + ${y}px)`,
        animationDelay: `${index * 0.3}s`,
        '--i': index
      }
    }


    const getParticleStyle = (index) => {
      const positions = [
        { top: '10%', left: '20%', delay: '0s' },
        { top: '20%', left: '80%', delay: '0.5s' },
        { top: '40%', left: '15%', delay: '1s' },
        { top: '60%', left: '85%', delay: '1.5s' },
        { top: '80%', left: '30%', delay: '2s' },
        { top: '15%', left: '60%', delay: '2.5s' },
        { top: '70%', left: '70%', delay: '3s' },
        { top: '35%', left: '40%', delay: '3.5s' },
        { top: '90%', left: '10%', delay: '4s' },
        { top: '25%', left: '90%', delay: '4.5s' },
        { top: '55%', left: '25%', delay: '5s' },
        { top: '75%', left: '55%', delay: '5.5s' },
        { top: '45%', left: '75%', delay: '6s' },
        { top: '85%', left: '45%', delay: '6.5s' },
        { top: '65%', left: '95%', delay: '7s' }
      ]
      
      const pos = positions[index % positions.length]
      return {
        top: pos.top,
        left: pos.left,
        animationDelay: pos.delay
      }
    }

    // Watchers
    watch(() => currentSession.value?.messages?.length, (newVal, oldVal) => {
      if (newVal > oldVal) {
        scrollToBottom()
      }
    })

    watch(selectedProvider, (newProvider) => {
      const savedKey = localStorage.getItem(`thanos_api_key_${newProvider}`)
      apiKey.value = savedKey || ''
    })

    // Lifecycle
    onMounted(() => {
      if (chatSessions.value.length === 0 && !isInitialized.value) {
        showInitModal.value = true
      }
      
      const savedKey = localStorage.getItem(`thanos_api_key_${selectedProvider.value}`)
      if (savedKey) {
        apiKey.value = savedKey
      }
    })

    return {
      // UI State
      sidebarCollapsed,
      showInitModal,
      showSettings,
      showApiKey,
      showHelpModal,
      showStatusInfo,
      activeConfigTab,
      isLoading,
      
      // Animação Modal
      modalAnimationActive,
      modalAnimationPhase,
      
      // Animação Thanos
      thanosAnimationActive,
      thanosAnimationPhase,
      infinityStones,
      
      // Chat State
      userInput,
      selectedSessionIndex,
      
      // Configuration
      selectedDocumentType,
      documentUrl,
      documentFile,
      selectedProvider,
      selectedModel,
      apiKey,
      language,
      showSummary,
      useRAG,
      documentSummary,
      ragSettings,
      modelSettings,
      
      // Computed
      isInitialized,
      chatSessions,
      currentSession,
      availableProviders,
      quickPrompts,
      configTabs,
      totalMessages,
      starterSuggestions,
      apiKeyMasked,
      canInitialize,
      
      // Refs
      chatContainer,
      
      // Methods
      toggleSidebar,
      openModalWithAnimation,
      closeInitModal,
      toggleStatusInfo,
      getStatusIcon,
      getStatusText,
      getStatusTooltip,
      getLastActivity,
      getSessionIcon,
      getSessionLogo,
      getStatusSubtext,
      getBinaryDigit,
      getQuantumStyle,
      formatTimestamp,
      autoResize,
      clearInput,
      insertPrompt,
      scrollToBottom,
      getModelsForProvider,
      getFileAccept,
      handleFileUpload,
      handleFileDrop,
      selectSession,
      createNewSession,
      updateSessionTitle,
      clearHistory,
      initializeThanos,
      sendMessage,
      exportCurrentSession,
      createFlashcardsFromSession,
      importSession,
      clearAllSessions,
      getParticleStyle,
      startThanosInitAnimation,
      completeThanosInitAnimation,
      failThanosInitAnimation,
      getMedicalSymbol,
      getKnowledgeParticleStyle
    }
  }
}
</script>

<style scoped>
/* Reset and Base */
* {
  box-sizing: border-box;
}

.thanos-app {
  height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #0e1a2e 100%);
  color: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  overflow: hidden;
}

/* Navigation Header */
.nav-header {
  height: 60px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 1.5rem;
  max-width: 100%;
  min-width: 0;
  gap: 1rem;
  overflow: hidden;
}

.brand-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  min-width: 0;
  max-width: calc(100% - 200px);
  overflow: hidden;
}

/* Nav Collapse Button */
.nav-collapse-btn {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 10px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  margin-left: 0.5rem;
  flex-shrink: 0;
}

.nav-collapse-btn:hover {
  background: rgba(0, 212, 255, 0.12);
  border-color: rgba(0, 212, 255, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 212, 255, 0.15);
}

.nav-collapse-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.nav-custom-collapse-logo {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-collapse-svg {
  width: 16px;
  height: 16px;
  color: #ffffff;
  transition: all 0.3s ease;
}

.nav-collapse-btn:hover .nav-collapse-svg {
  color: #00d4ff;
}

.nav-sidebar-lines {
  transition: all 0.3s ease;
}

.nav-sidebar-lines.collapsed {
  transform: translateX(-2px);
  opacity: 0.6;
}

.nav-collapse-btn:hover .nav-sidebar-lines {
  opacity: 1;
}

.nav-arrow-direction {
  transition: transform 0.3s ease;
}

.nav-arrow-direction.collapsed {
  transform: rotate(180deg);
}

.brand-icon {
  position: relative;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(78, 205, 196, 0.05));
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.brand-icon:hover {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(78, 205, 196, 0.1));
  border-color: rgba(0, 212, 255, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.2);
}

/* Oracle Logo Styles */
.oracle-logo {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.oracle-svg {
  width: 20px;
  height: 20px;
  color: #00d4ff;
  transition: all 0.3s ease;
}

.brand-icon:hover .oracle-svg {
  color: #ffffff;
  transform: scale(1.05);
}

/* Animações do anel principal */
.main-ring {
  opacity: 0.8;
  transition: all 0.3s ease;
  stroke-dasharray: 50 10;
  animation: ringPulse 3s ease-in-out infinite;
}

.brand-icon:hover .main-ring {
  opacity: 1;
  stroke-dasharray: 60 5;
  animation-duration: 2s;
}

/* Pontos orbitais */
.orbital-dots {
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.brand-icon:hover .orbital-dots {
  opacity: 1;
}

.dot-1 {
  filter: drop-shadow(0 0 3px currentColor);
}

.dot-2 {
  filter: drop-shadow(0 0 2px currentColor);
}


.brand-text h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.brand-symbol {
  font-size: 1rem;
  color: #00d4ff;
  opacity: 0.8;
}

.brand-symbol-end {
  font-size: 0.9rem;
  color: #00d4ff;
  opacity: 0.6;
  animation: rotate 4s linear infinite;
}

.ai-suffix {
  color: #00d4ff;
  margin-left: 0.25rem;
}

.tagline {
  font-size: 0.75rem;
  color: #a0a0a0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: -0.25rem;
}

.tagline i {
  color: #00d4ff;
  opacity: 0.7;
}

.pulse-icon {
  animation: heartbeat 1.5s ease-in-out infinite;
  color: #ff6b6b !important;
}

.nav-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 1rem;
  flex-shrink: 0;
  min-width: 0;
  position: relative;
}

/* Clean Status Button */
/* Nav Status Card - Similar to Action Cards */
.nav-status-card {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.875rem;
  font-weight: 600;
  overflow: hidden;
  min-height: 44px;
  flex-shrink: 0;
  white-space: nowrap;
}

.nav-status-card:hover {
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.25);
}

.nav-status-card:hover .status-glow {
  opacity: 1;
}

.nav-status-card.connected {
  border-color: rgba(81, 207, 102, 0.3);
}

.nav-status-card.connected:hover {
  border-color: rgba(81, 207, 102, 0.5);
}

.nav-status-card.connected .status-glow {
  background: linear-gradient(135deg, rgba(81, 207, 102, 0.1), rgba(46, 160, 67, 0.1));
}

/* Status Icon Container */
.status-icon-container {
  position: relative;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  transition: all 0.3s ease;
}

.nav-status-card:hover .status-icon-container {
  transform: scale(1.05);
}

/* Status Logo */
.status-logo {
  width: 20px;
  height: 20px;
  color: #ff6b6b;
  transition: all 0.3s ease;
}

.nav-status-card.connected .status-logo {
  color: #51cf66;
}

.nav-status-card:hover .status-logo {
  transform: scale(1.1);
}

/* Status Text */
.status-text {
  font-size: 0.875rem;
  font-weight: 600;
  color: #ffffff;
}

/* Status Glow */
.status-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 82, 82, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12px;
}



.settings-btn {
  position: relative;
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 14px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.settings-btn:hover {
  background: rgba(0, 212, 255, 0.15);
  transform: translateY(-2px);
  border-color: rgba(0, 212, 255, 0.4);
  box-shadow: 0 8px 30px rgba(0, 212, 255, 0.2);
}

.settings-btn:hover .settings-glow {
  opacity: 1;
}

.settings-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.settings-btn i {
  font-size: 1.1rem;
  animation: settingsRotate 4s ease-in-out infinite;
  transition: all 0.3s ease;
  z-index: 2;
}

.settings-btn:hover i {
  color: #00d4ff;
  transform: scale(1.1);
}

.settings-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(0, 153, 204, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 14px;
}

/* Main Layout */
.main-layout {
  display: flex;
  height: calc(100vh - 60px);
}

/* Sidebar */
.sidebar {
  width: 320px;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  transition: width 0.3s ease;
  overflow: hidden;
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-header {
  height: 0px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.collapse-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.collapse-btn:hover {
  background: rgba(0, 212, 255, 0.15);
  border-color: rgba(0, 212, 255, 0.3);
  transform: translateY(-1px);
}

.collapse-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.collapse-symbol {
  font-size: 0.9rem;
  color: #00d4ff;
  opacity: 0.6;
  position: absolute;
  animation: medicalPulse 3s ease-in-out infinite;
}

.collapse-arrow {
  font-size: 0.9rem;
  color: #ffffff;
  z-index: 2;
  transition: transform 0.3s ease;
}

.collapse-btn:hover .collapse-arrow {
  transform: scale(1.1);
}

.collapse-btn:hover .collapse-symbol {
  opacity: 0.9;
  color: #00d4ff;
}

/* Custom Collapse Logo */
.custom-collapse-logo {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collapse-svg {
  width: 18px;
  height: 18px;
  color: #ffffff;
  transition: all 0.3s ease;
}

.collapse-btn:hover .collapse-svg {
  color: #00d4ff;
}

/* Sidebar Lines Animation */
.sidebar-lines {
  transition: all 0.3s ease;
}

.sidebar-lines.collapsed {
  transform: translateX(-2px);
  opacity: 0.6;
}

.collapse-btn:hover .sidebar-lines {
  opacity: 1;
}

/* Arrow Direction Animation */
.arrow-direction {
  transition: transform 0.3s ease;
}

.arrow-direction.collapsed {
  transform: rotate(180deg);
}

.sidebar-content {
  padding: 1.5rem;
  overflow-y: auto;
  height: calc(100% - 1px);
  display: flex;
  flex-direction: column;
}

.section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #a0a0a0;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}


.action-card {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1.25rem 1rem;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.875rem;
  font-weight: 600;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-height: 90px;
}

.action-card:hover {
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-4px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25);
  border-color: rgba(255, 255, 255, 0.25);
}

.action-card:hover .action-glow {
  opacity: 1;
}

.action-card:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.action-icon {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: rgba(0, 212, 255, 0.2);
  transition: all 0.3s ease;
}

.action-card i {
  font-size: 1.3rem;
  color: #00d4ff;
  z-index: 2;
  transition: all 0.3s ease;
}

.action-logo {
  width: 24px;
  height: 24px;
  color: #00d4ff;
  transition: all 0.3s ease;
  z-index: 2;
}

.icon-overlay {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
  z-index: 3;
}

.icon-overlay i {
  font-size: 0.7rem !important;
  color: #1a1a2e !important;
}

.action-card:hover .icon-overlay {
  opacity: 1;
  transform: scale(1);
}

.action-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(0, 153, 204, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12px;
}

/* Themed action cards */
.action-card.exportar:hover .action-icon {
  background: rgba(33, 150, 243, 0.4);
  transform: scale(1.05);
}

.action-card.exportar:hover i {
  color: #2196f3;
  transform: scale(1.1);
}

.action-card.exportar:hover .action-logo {
  color: #2196f3;
  transform: scale(1.1);
}

.action-card.configuracoes:hover .action-icon {
  background: rgba(156, 39, 176, 0.4);
  transform: scale(1.05);
}

.action-card.configuracoes:hover i {
  color: #9c27b0;
  transform: scale(1.1);
  animation: settingsRotate 0.5s ease-in-out;
}

.action-card.configuracoes:hover .action-logo {
  color: #9c27b0;
  transform: scale(1.1);
  animation: logoRotate 0.5s ease-in-out;
}

.action-card.importar:hover .action-icon {
  background: rgba(103, 58, 183, 0.4);
  transform: scale(1.05);
}

.action-card.importar:hover i {
  color: #673ab7;
  transform: scale(1.1);
}

.action-card.limpar:hover .action-icon {
  background: rgba(244, 67, 54, 0.4);
  transform: scale(1.05);
}

.action-card.limpar:hover i {
  color: #f44336;
  transform: scale(1.1);
}

.action-card.limpar:hover .action-logo {
  color: #f44336;
  transform: scale(1.1);
  animation: shakeAnimation 0.3s ease-in-out;
}

/* Sessions */
.sessions-section {
  flex: 1;
  margin-bottom: 1.5rem;
  overflow-y: auto;
}

.sessions-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.empty-sessions {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 1rem;
  background: rgba(255, 255, 255, 0.03);
  border: 2px dashed rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  text-align: center;
}

.empty-sessions-icon {
  position: relative;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  flex-shrink: 0;
}

.empty-sessions-icon i {
  font-size: 1.2rem;
  z-index: 2;
}

.empty-logo {
  width: 22px;
  height: 22px;
  color: inherit;
  transition: all 0.3s ease;
  z-index: 2;
}

.empty-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: emptyPulse 3s ease-in-out infinite;
}

.empty-sessions-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  text-align: left;
}

.empty-sessions-text span {
  color: #a0a0a0;
  font-weight: 500;
  font-size: 0.875rem;
}

.empty-sessions-text small {
  color: #666;
  font-size: 0.75rem;
}

.session-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.session-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.session-item.active {
  background: rgba(0, 212, 255, 0.2);
  border-color: #00d4ff;
}

.session-icon {
  position: relative;
  width: 40px;
  height: 40px;
  background: rgba(0, 212, 255, 0.2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00d4ff;
  flex-shrink: 0;
  overflow: hidden;
}

.session-icon i {
  font-size: 1.1rem;
  z-index: 2;
  transition: all 0.3s ease;
}

.session-logo {
  width: 20px;
  height: 20px;
  color: inherit;
  transition: all 0.3s ease;
  z-index: 2;
}

.session-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  background: #51cf66;
  border: 2px solid #1a1a2e;
  border-radius: 50%;
  animation: sessionPulse 2s ease-in-out infinite;
}

.session-item.active .session-icon {
  background: rgba(0, 212, 255, 0.4);
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
}

.session-item.active .session-icon i {
  color: #ffffff;
  transform: scale(1.1);
}

.session-item.active .session-logo {
  color: #ffffff;
  transform: scale(1.1);
}

.session-content {
  flex: 1;
  min-width: 0;
}

.session-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.session-meta {
  font-size: 0.75rem;
  color: #a0a0a0;
}


/* Bottom Actions */
.bottom-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: auto;
}

.sidebar.collapsed .bottom-actions {
  padding: 0.5rem 0;
}

.bottom-actions .action-card {
  width: 100%;
  justify-content: flex-start;
  padding: 0.625rem 0.875rem;
  min-height: auto;
  gap: 0.5rem;
}

.sidebar.collapsed .bottom-actions .action-card {
  justify-content: center;
  padding: 0.75rem;
}

.bottom-actions .action-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
}

.bottom-actions .action-card i {
  font-size: 1rem;
}

.bottom-actions .action-card span {
  font-size: 0.8rem;
}

/* Content Area */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Welcome Screen */
.welcome-screen {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  overflow: hidden;
}

.welcome-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
}

.grid-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.grid-line {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.3), transparent);
  height: 1px;
  width: 100%;
  animation: gridPulse 4s ease-in-out infinite;
}

.grid-line.vertical {
  background: linear-gradient(0deg, transparent, rgba(0, 212, 255, 0.3), transparent);
  width: 1px;
  height: 100%;
  top: 0;
  animation: gridPulse 4s ease-in-out infinite reverse;
}

.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #00d4ff;
  border-radius: 50%;
  animation: particleFloat 8s ease-in-out infinite;
}

.welcome-content {
  position: relative;
  text-align: center;
  max-width: 600px;
  padding: 3rem 2rem;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 10;
}

.welcome-hero {
  margin-bottom: 3rem;
}

.welcome-icon-group {
  position: relative;
  margin: 0 auto 2rem;
  width: 120px;
  height: 120px;
}

.welcome-icon {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #000;
  font-weight: bold;
}

.welcome-icon.main {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  font-size: 2rem;
  z-index: 3;
}

.welcome-icon.secondary {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  font-size: 1rem;
  color: #00d4ff;
  animation: orbitalRotate 15s linear infinite;
}

.welcome-icon.icon-1 {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}

.welcome-icon.icon-2 {
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  animation-delay: -5s;
}

.welcome-icon.icon-3 {
  bottom: 0;
  left: 0;
  animation-delay: -10s;
}

.icon-pulse {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 2px solid #00d4ff;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.welcome-content h2 {
  margin-bottom: 1.5rem;
}

.title-gradient {
  display: block;
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(135deg, #00d4ff, #ffffff, #0099cc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.title-sub {
  display: block;
  font-size: 1.2rem;
  font-weight: 400;
  color: #a0a0a0;
}

.welcome-description {
  color: #e0e0e0;
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 3rem;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}


.welcome-actions {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 0.875rem;
  padding: 1.5rem 3rem;
  border: none;
  border-radius: 18px;
  font-weight: 600;
  font-size: 1.125rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.cta-button.primary {
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  color: #000;
  box-shadow: 0 4px 20px rgba(0, 212, 255, 0.2);
}

.cta-button.primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 40px rgba(0, 212, 255, 0.4);
}

.cta-button.primary:hover .button-glow {
  opacity: 1;
}

.cta-button.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.cta-button.secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.button-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 16px;
}

/* Enhanced JARVIS Oracle Button */
.jarvis-oracle-button {
  position: relative;
  overflow: visible;
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  border: 2px solid #00ffff;
  box-shadow: 
    0 0 20px rgba(0, 255, 255, 0.4),
    inset 0 0 20px rgba(0, 255, 255, 0.1);
  animation: oracleButtonPulse 3s ease-in-out infinite;
}

.jarvis-oracle-button:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 
    0 8px 40px rgba(0, 255, 255, 0.6),
    0 0 30px rgba(0, 255, 255, 0.8),
    inset 0 0 30px rgba(0, 255, 255, 0.2);
  border-color: #00ffff;
  animation-duration: 1.5s;
}

.jarvis-button-core {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* Arc Reactor for Button */
.button-arc-reactor {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  z-index: 3;
}

.arc-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, #ffffff, #00ffff);
  border-radius: 50%;
  box-shadow: 0 0 10px #00ffff;
  animation: arcPulse 2s ease-in-out infinite;
}

.arc-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid transparent;
  border-radius: 50%;
  border-top-color: #00ffff;
  animation: arcRotate 3s linear infinite;
}

.arc-ring-1 {
  width: 20px;
  height: 20px;
  animation-duration: 2s;
}

.arc-ring-2 {
  width: 30px;
  height: 30px;
  animation-duration: 3s;
  animation-direction: reverse;
}

.arc-ring-3 {
  width: 40px;
  height: 40px;
  animation-duration: 4s;
  border-top-color: rgba(0, 255, 255, 0.6);
}

/* Neural Grid Background */
.neural-grid-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.3;
  z-index: 1;
}

.neural-node {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #00ffff;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform-origin: 0 0;
  transform: translate(-50%, -50%) rotate(var(--angle)) translateX(60px);
  animation: neuralPulse 2s ease-in-out infinite;
  animation-delay: var(--delay);
  box-shadow: 0 0 6px #00ffff;
}

/* Energy Streams */
.energy-streams {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}

.energy-stream {
  position: absolute;
  background: linear-gradient(90deg, transparent, #00ffff, transparent);
  height: 2px;
  width: 100%;
  top: 50%;
  transform: translateY(-50%);
  animation: energyFlow 2s ease-in-out infinite;
  opacity: 0.7;
}

.stream-1 {
  animation-delay: 0s;
}

.stream-2 {
  top: 30%;
  animation-delay: 0.7s;
  opacity: 0.5;
}

.stream-3 {
  top: 70%;
  animation-delay: 1.4s;
  opacity: 0.5;
}

/* Data Particles */
.data-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}

.data-particle {
  position: absolute;
  width: 3px;
  height: 3px;
  background: #00ffff;
  border-radius: 50%;
  top: 50%;
  left: 0;
  animation: particleTravel 3s ease-in-out infinite;
  animation-delay: var(--delay);
  animation-duration: var(--duration);
  box-shadow: 0 0 4px #00ffff;
}

/* HUD Corners */
.hud-corners {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 4;
}

.hud-corner {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid #00ffff;
  opacity: 0.8;
}

.hud-corner.top-left {
  top: 8px;
  left: 8px;
  border-right: none;
  border-bottom: none;
  animation: hudFlicker 3s ease-in-out infinite;
}

.hud-corner.top-right {
  top: 8px;
  right: 8px;
  border-left: none;
  border-bottom: none;
  animation: hudFlicker 3s ease-in-out infinite;
  animation-delay: 0.75s;
}

.hud-corner.bottom-left {
  bottom: 8px;
  left: 8px;
  border-right: none;
  border-top: none;
  animation: hudFlicker 3s ease-in-out infinite;
  animation-delay: 1.5s;
}

.hud-corner.bottom-right {
  bottom: 8px;
  right: 8px;
  border-left: none;
  border-top: none;
  animation: hudFlicker 3s ease-in-out infinite;
  animation-delay: 2.25s;
}

/* Holographic Scanning Line */
.scanning-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(to bottom, transparent, #00ffff, transparent);
  animation: scanningMove 3s ease-in-out infinite;
  z-index: 3;
  box-shadow: 0 0 10px #00ffff;
}

/* Button Content */
.button-content {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.875rem;
  z-index: 5;
  padding: 0 20px;
}

.button-icon {
  font-size: 1.25rem;
  color: #000;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
  animation: iconGlow 2s ease-in-out infinite;
}

.button-text {
  font-weight: 600;
  font-size: 1.125rem;
  color: #000;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.6);
}

/* Keyframe Animations for JARVIS Button */
@keyframes oracleButtonPulse {
  0%, 100% {
    box-shadow: 
      0 0 20px rgba(0, 255, 255, 0.4),
      inset 0 0 20px rgba(0, 255, 255, 0.1);
  }
  50% {
    box-shadow: 
      0 0 30px rgba(0, 255, 255, 0.6),
      inset 0 0 25px rgba(0, 255, 255, 0.2);
  }
}

@keyframes arcPulse {
  0%, 100% {
    box-shadow: 0 0 10px #00ffff;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    box-shadow: 0 0 20px #00ffff;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes arcRotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes neuralPulse {
  0%, 100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) rotate(var(--angle)) translateX(60px) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) rotate(var(--angle)) translateX(60px) scale(1.5);
  }
}

@keyframes energyFlow {
  0% {
    transform: translateY(-50%) translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    transform: translateY(-50%) translateX(100%);
    opacity: 0;
  }
}

@keyframes particleTravel {
  0% {
    left: 0;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

@keyframes hudFlicker {
  0%, 100% {
    opacity: 0.8;
  }
  20%, 80% {
    opacity: 1;
  }
  30%, 70% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.9;
  }
}

@keyframes scanningMove {
  0% {
    left: 0;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    left: calc(100% - 2px);
    opacity: 0;
  }
}

@keyframes iconGlow {
  0%, 100% {
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
  }
  50% {
    text-shadow: 0 0 20px rgba(0, 255, 255, 1);
  }
}

/* Chat Interface */
.chat-interface {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
}

.session-info {
  flex: 1;
}

.session-title-input {
  background: transparent;
  border: none;
  color: #ffffff;
  font-size: 1.25rem;
  font-weight: 600;
  outline: none;
  width: 100%;
  margin-bottom: 0.5rem;
}

.session-title-input::placeholder {
  color: #666;
}

.session-meta {
  display: flex;
  gap: 0.75rem;
}

.provider-tag,
.model-tag {
  padding: 0.25rem 0.75rem;
  background: rgba(0, 212, 255, 0.2);
  color: #00d4ff;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
}

.chat-actions {
  display: flex;
  gap: 0.5rem;
}

.icon-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 10px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.icon-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Document Summary */
.document-summary {
  margin: 1.5rem;
  padding: 1.5rem;
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 12px;
}

.summary-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  color: #00d4ff;
  font-weight: 600;
}

.summary-content {
  color: #e0e0e0;
  line-height: 1.6;
}

/* Messages */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 3rem;
}

.empty-animation {
  margin-bottom: 2rem;
}

.floating-brain {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  border-radius: 50%;
  animation: brainFloat 3s ease-in-out infinite;
}

.floating-brain i {
  font-size: 2rem;
  color: #000;
  z-index: 2;
}

.brain-waves {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.wave {
  position: absolute;
  border: 2px solid #00d4ff;
  border-radius: 50%;
  animation: waveExpand 2s ease-out infinite;
}

.wave-1 {
  width: 100px;
  height: 100px;
  margin: -50px;
  animation-delay: 0s;
}

.wave-2 {
  width: 120px;
  height: 120px;
  margin: -60px;
  animation-delay: 0.5s;
}

.wave-3 {
  width: 140px;
  height: 140px;
  margin: -70px;
  animation-delay: 1s;
}

.empty-state h3 {
  color: #ffffff;
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.empty-state p {
  color: #a0a0a0;
  margin-bottom: 2rem;
  font-size: 1rem;
}

.starter-suggestions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  max-width: 600px;
  width: 100%;
}

.suggestion-chip {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.25rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  text-align: left;
}

.suggestion-chip:hover {
  background: rgba(0, 212, 255, 0.1);
  border-color: #00d4ff;
  transform: translateY(-2px);
}

.suggestion-chip i {
  color: #00d4ff;
  font-size: 1.1rem;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.message-wrapper {
  display: flex;
  gap: 1rem;
  max-width: 80%;
}

.message-wrapper.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.message-wrapper.user .message-avatar {
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  color: #000;
}

.message-wrapper.ai .message-avatar {
  background: rgba(255, 255, 255, 0.1);
  color: #00d4ff;
}

.message-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  padding: 1rem 1.25rem;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.message-wrapper.user .message-content {
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  color: #000;
  border: none;
}

.message-text {
  line-height: 1.6;
  margin-bottom: 0.5rem;
}

.message-timestamp {
  font-size: 0.75rem;
  opacity: 0.7;
}

/* Input Area */
.input-area {
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
}

.input-container {
  max-width: 100%;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #00d4ff;
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.message-input {
  flex: 1;
  background: transparent;
  border: none;
  color: #ffffff;
  font-size: 1rem;
  line-height: 1.5;
  resize: none;
  outline: none;
  min-height: 24px;
  max-height: 120px;
}

.message-input::placeholder {
  color: #666;
}

.input-actions {
  display: flex;
  gap: 0.5rem;
}

.send-button {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  border: none;
  border-radius: 12px;
  color: #000;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quick-prompts {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.prompt-chip {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  color: #ffffff;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.prompt-chip:hover {
  background: rgba(0, 212, 255, 0.2);
  border-color: #00d4ff;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 2rem;
}

.modal-container {
  background: #1a1a2e;
  border-radius: 20px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2rem 2rem 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  color: #ffffff;
}

.modal-close {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 12px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
}

.modal-content {
  padding: 2rem;
  overflow-y: auto;
  max-height: calc(90vh - 140px);
}

.config-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.5rem;
  border-radius: 12px;
}

.tab-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: #a0a0a0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.tab-button.active {
  background: #00d4ff;
  color: #000;
}

.tab-content {
  min-height: 300px;
}

.config-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: #ffffff;
  font-size: 0.875rem;
}

.form-input,
.form-select {
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #00d4ff;
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.form-range {
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  outline: none;
}

.form-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: #00d4ff;
  border-radius: 50%;
  cursor: pointer;
}

.form-range::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: #00d4ff;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.range-value {
  display: inline-block;
  margin-left: 1rem;
  padding: 0.25rem 0.75rem;
  background: rgba(0, 212, 255, 0.2);
  color: #00d4ff;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.file-upload-area {
  position: relative;
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.file-upload-area:hover {
  border-color: #00d4ff;
  background: rgba(0, 212, 255, 0.05);
}

.file-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #a0a0a0;
  cursor: pointer;
}

.file-label i {
  font-size: 2rem;
  color: #00d4ff;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 8px;
  color: #00d4ff;
  margin-top: 0.5rem;
}

.api-key-input {
  position: relative;
}

.toggle-visibility {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #a0a0a0;
  cursor: pointer;
  padding: 0.5rem;
}

.form-hint {
  color: #a0a0a0;
  font-size: 0.75rem;
}

.toggle-group {
  display: flex;
  align-items: center;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  font-size: 0.875rem;
}

.toggle-input {
  display: none;
}

.toggle-slider {
  width: 50px;
  height: 26px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 13px;
  position: relative;
  transition: all 0.3s ease;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  width: 22px;
  height: 22px;
  background: #ffffff;
  border-radius: 50%;
  top: 2px;
  left: 2px;
  transition: all 0.3s ease;
}

.toggle-input:checked + .toggle-slider {
  background: #00d4ff;
}

.toggle-input:checked + .toggle-slider::before {
  transform: translateX(24px);
}

.rag-settings {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-footer {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding: 0 2rem 2rem;
}

.btn {
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

.btn-primary {
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  color: #000;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Status Modal Styles */
.status-modal {
  max-width: 500px;
}

.status-details {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-item-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 500;
  color: #e0e0e0;
}

.status-item-label i {
  width: 20px;
  text-align: center;
  color: #00d4ff;
  font-size: 1rem;
}

.status-item-value {
  font-weight: 600;
  color: #a0a0a0;
  text-align: right;
  max-width: 200px;
  word-wrap: break-word;
}

.status-item-value.online {
  color: #51cf66;
}

.status-actions {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: center;
}

/* Settings Panel */
.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1500;
  display: flex;
  justify-content: flex-end;
}

.settings-panel {
  width: 400px;
  background: #1a1a2e;
  box-shadow: -10px 0 30px rgba(0, 0, 0, 0.3);
  height: 100vh;
  overflow-y: auto;
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-header h3 {
  margin: 0;
  color: #ffffff;
}

.close-btn {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 8px;
  color: #ffffff;
  cursor: pointer;
}

.settings-content {
  padding: 1.5rem;
}

.setting-item {
  margin-bottom: 2rem;
}

.setting-item label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #ffffff;
}

/* Animations */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Custom Animations */
@keyframes gridPulse {
  0%, 100% { opacity: 0.1; }
  50% { opacity: 0.4; }
}

@keyframes particleFloat {
  0%, 100% { 
    transform: translateY(0px) scale(1);
    opacity: 0.3;
  }
  50% { 
    transform: translateY(-20px) scale(1.2);
    opacity: 0.8;
  }
}

@keyframes brainFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes waveExpand {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

@keyframes pulse {
  0%, 100% { 
    transform: scale(1);
    opacity: 0.5;
  }
  50% { 
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes orbitalRotate {
  0% { 
    transform: rotate(0deg) translateX(40px) rotate(0deg);
  }
  100% { 
    transform: rotate(360deg) translateX(40px) rotate(-360deg);
  }
}

/* Header Animations */
@keyframes particlePulse {
  0%, 100% { 
    opacity: 0.3;
    transform: scale(1);
  }
  50% { 
    opacity: 1;
    transform: scale(1.5);
  }
}

@keyframes heartbeat {
  0%, 100% { 
    transform: scale(1);
  }
  50% { 
    transform: scale(1.2);
  }
}

@keyframes rotate {
  0% { 
    transform: rotate(0deg);
  }
  100% { 
    transform: rotate(360deg);
  }
}

@keyframes statusPulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.6;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

@keyframes sessionPulse {
  0%, 100% { 
    opacity: 0.8;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(81, 207, 102, 0.4);
  }
  50% { 
    opacity: 1;
    transform: scale(1.1);
    box-shadow: 0 0 0 4px rgba(81, 207, 102, 0.1);
  }
}

@keyframes settingsRotate {
  0%, 100% { 
    transform: rotate(0deg);
  }
  25% { 
    transform: rotate(90deg);
  }
  50% { 
    transform: rotate(180deg);
  }
  75% { 
    transform: rotate(270deg);
  }
}

@keyframes medicalPulse {
  0%, 100% { 
    opacity: 0.6;
    transform: scale(1);
  }
  50% { 
    opacity: 0.9;
    transform: scale(1.1);
  }
}

@keyframes actionHintPulse {
  0%, 100% { 
    opacity: 0.7;
    transform: scale(0.9);
  }
  50% { 
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes emptyPulse {
  0%, 100% { 
    opacity: 0.2;
    transform: translate(-50%, -50%) scale(0.8);
  }
  50% { 
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes logoRotate {
  0% { 
    transform: scale(1.1) rotate(0deg);
  }
  50% { 
    transform: scale(1.1) rotate(180deg);
  }
  100% { 
    transform: scale(1.1) rotate(360deg);
  }
}

@keyframes shakeAnimation {
  0%, 100% { 
    transform: scale(1.1) translateX(0);
  }
  25% { 
    transform: scale(1.1) translateX(-2px);
  }
  75% { 
    transform: scale(1.1) translateX(2px);
  }
}

/* Neural Panel Animations */
@keyframes neuralPulse {
  0%, 100% { 
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 2px rgba(255, 255, 255, 0.1),
      0 0 0 1px rgba(255, 107, 107, 0.4),
      0 0 20px rgba(255, 107, 107, 0.2);
  }
  50% { 
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 2px rgba(255, 255, 255, 0.1),
      0 0 0 2px rgba(255, 107, 107, 0.6),
      0 0 30px rgba(255, 107, 107, 0.4);
  }
}

@keyframes neuralNodePulse {
  0%, 100% { 
    opacity: 0.6;
    r: 1.5;
  }
  50% { 
    opacity: 1;
    r: 2.5;
  }
}

@keyframes neuralFlow {
  0% { 
    stroke-dasharray: 5 5;
    stroke-dashoffset: 0;
    opacity: 0.4;
  }
  50% { 
    opacity: 0.8;
  }
  100% { 
    stroke-dashoffset: 10;
    opacity: 0.4;
  }
}

@keyframes cpuActivity {
  0%, 100% { 
    background: rgba(0, 212, 255, 0.4);
    transform: scale(1);
  }
  25% { 
    background: rgba(0, 212, 255, 0.8);
    transform: scale(1.1);
  }
  50% { 
    background: rgba(81, 207, 102, 0.6);
    transform: scale(0.9);
  }
  75% { 
    background: rgba(255, 107, 107, 0.5);
    transform: scale(1.05);
  }
}

@keyframes cpuPulse {
  0%, 100% { 
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
  50% { 
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes holoRotate {
  0% { 
    transform: rotate(0deg) scale(1);
    opacity: 0.3;
  }
  25% { 
    opacity: 0.6;
  }
  50% { 
    transform: rotate(180deg) scale(1.1);
    opacity: 0.8;
  }
  75% { 
    opacity: 0.6;
  }
  100% { 
    transform: rotate(360deg) scale(1);
    opacity: 0.3;
  }
}

@keyframes symbolPulse {
  0%, 100% { 
    opacity: 1;
    transform: scale(1);
  }
  50% { 
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes symbolAlert {
  0%, 100% { 
    opacity: 1;
    transform: scale(1);
  }
  50% { 
    opacity: 0.5;
    transform: scale(1.2);
  }
}

@keyframes textGlitch {
  0%, 100% { 
    text-shadow: 0 0 8px currentColor;
    transform: translateX(0);
  }
  20% { 
    text-shadow: -2px 0 8px currentColor, 2px 0 8px currentColor;
    transform: translateX(-1px);
  }
  40% { 
    text-shadow: 2px 0 8px currentColor, -2px 0 8px currentColor;
    transform: translateX(1px);
  }
  60% { 
    text-shadow: 0 0 8px currentColor;
    transform: translateX(0);
  }
}

@keyframes textGlow {
  0%, 100% { 
    text-shadow: 0 0 8px currentColor;
  }
  50% { 
    text-shadow: 0 0 16px currentColor, 0 0 24px currentColor;
  }
}

@keyframes barPulse {
  0%, 100% { 
    opacity: 1;
    transform: scaleY(1);
  }
  50% { 
    opacity: 0.7;
    transform: scaleY(1.3);
  }
}

@keyframes quantumFloat {
  0%, 100% { 
    opacity: 0.6;
    transform: translateY(0px) rotate(0deg);
  }
  25% { 
    opacity: 1;
    transform: translateY(-10px) rotate(90deg);
  }
  50% { 
    opacity: 0.8;
    transform: translateY(-5px) rotate(180deg);
  }
  75% { 
    opacity: 1;
    transform: translateY(-15px) rotate(270deg);
  }
}

@keyframes scanSweep {
  0% { 
    left: -100%;
    opacity: 0;
  }
  10% { 
    opacity: 1;
  }
  90% { 
    opacity: 1;
  }
  100% { 
    left: 100%;
    opacity: 0;
  }
}

@keyframes dataBlink {
  0%, 100% { 
    opacity: 0.6;
  }
  50% { 
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 1rem;
  }
  
  .brand-text .tagline {
    display: none;
  }
  
  .sidebar {
    width: 280px;
  }
  
  .sidebar.collapsed {
    width: 60px;
  }
  
  .modal-container {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }
  
  .settings-panel {
    width: 100%;
  }
  
  .message-wrapper {
    max-width: 95%;
  }
}

@media (max-width: 480px) {
  .main-layout {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    height: auto;
    max-height: 200px;
  }
  
  .sidebar.collapsed {
    height: 60px;
  }
  
  .content-area {
    height: calc(100vh - 260px);
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Responsive fixes for nav */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 1rem;
    gap: 0.5rem;
  }
  
  .brand-section {
    max-width: calc(100% - 150px);
  }
  
  .nav-collapse-btn {
    width: 32px;
    height: 32px;
    margin-left: 0.25rem;
  }
  
  .nav-collapse-svg {
    width: 14px;
    height: 14px;
  }
  
  .brand-icon {
    width: 36px;
    height: 36px;
  }
  
  .oracle-svg {
    width: 18px;
    height: 18px;
  }
  
  .brand-text h1 {
    font-size: 1.2rem;
  }
  
  .tagline {
    font-size: 0.8rem;
  }
  
  .nav-actions {
    gap: 0.5rem;
  }
  
  .nav-status-card {
    padding: 0.625rem 0.875rem;
    font-size: 0.8rem;
    gap: 0.625rem;
    min-height: 40px;
  }
  
  .status-icon-container {
    width: 28px;
    height: 28px;
  }
  
  .status-logo {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 0 0.5rem;
  }
  
  .brand-section {
    max-width: calc(100% - 100px);
  }
  
  .nav-collapse-btn {
    width: 28px;
    height: 28px;
    margin-left: 0.25rem;
  }
  
  .nav-collapse-svg {
    width: 12px;
    height: 12px;
  }
  
  .brand-icon {
    width: 32px;
    height: 32px;
  }
  
  .oracle-svg {
    width: 16px;
    height: 16px;
  }
  
  .brand-text h1 {
    font-size: 1rem;
  }
  
  .tagline {
    display: none;
  }
  
  .nav-status-card {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    gap: 0.5rem;
    min-height: 36px;
  }
  
  .status-text {
    display: none;
  }
  
  .status-icon-container {
    width: 24px;
    height: 24px;
  }
  
  .status-logo {
    width: 16px;
    height: 16px;
  }
}

/* Oracle Initialization Animation */
.oracle-initialization-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(10, 25, 47, 0.98), rgba(0, 15, 35, 0.95));
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(20px);
  animation: oracle-appear 0.8s ease-out;
}

.oracle-initialization-overlay.preparing {
  background: linear-gradient(135deg, rgba(10, 25, 47, 0.98), rgba(25, 35, 60, 0.95));
}

.oracle-initialization-overlay.materializing {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.08), rgba(10, 25, 47, 0.98));
  animation: oracle-scan 1s ease-in-out;
}

.oracle-initialization-overlay.expanding {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(25, 35, 60, 0.95));
  animation: oracle-vision 0.8s ease-out;
}

.oracle-initialization-overlay.ready {
  opacity: 0;
  animation: oracle-complete 0.6s ease-in forwards;
}

/* Oracle Vision Container */
.oracle-vision-container {
  position: relative;
  width: 350px;
  height: 350px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 3rem;
}

/* Oracle Eye (Centro) */
.oracle-eye {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 212, 255, 0.3) 0%, rgba(0, 150, 255, 0.1) 70%, transparent 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  animation: oracle-eye-pulse 2s ease-in-out infinite;
}

.eye-iris {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 
    0 0 30px rgba(0, 212, 255, 0.6),
    inset 0 0 20px rgba(255, 255, 255, 0.1);
  animation: iris-rotate 8s linear infinite;
}

.eye-pupil {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00d4ff;
  font-size: 1.2rem;
  animation: pupil-scan 3s ease-in-out infinite;
}

.eye-scan-lines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: conic-gradient(transparent 0deg, rgba(0, 212, 255, 0.5) 45deg, transparent 90deg);
  animation: scan-sweep 2s linear infinite;
}

/* Data Rings */
.data-rings {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.data-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.ring-1 {
  width: 160px;
  height: 160px;
  animation: ring-orbit 4s linear infinite;
}

.ring-2 {
  width: 220px;
  height: 220px;
  animation: ring-orbit 6s linear infinite reverse;
}

.ring-3 {
  width: 280px;
  height: 280px;
  animation: ring-orbit 8s linear infinite;
}

.data-node {
  position: absolute;
  width: 6px;
  height: 6px;
  background: #00d4ff;
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(0, 212, 255, 0.8);
  animation: node-pulse 2s ease-in-out infinite;
}

/* Posicionamento dos nós */
.ring-1 .data-node:nth-child(1) { top: 0; left: 50%; transform: translateX(-50%); }
.ring-1 .data-node:nth-child(2) { top: 25%; right: 0; }
.ring-1 .data-node:nth-child(3) { bottom: 25%; right: 0; }
.ring-1 .data-node:nth-child(4) { bottom: 0; left: 50%; transform: translateX(-50%); }
.ring-1 .data-node:nth-child(5) { bottom: 25%; left: 0; }
.ring-1 .data-node:nth-child(6) { top: 25%; left: 0; }

/* Diagnostic Beams */
.diagnostic-beams {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.beam {
  position: absolute;
  width: 2px;
  height: 100px;
  background: linear-gradient(to bottom, transparent, rgba(0, 212, 255, 0.8), transparent);
  transform-origin: center bottom;
}

.beam-1 {
  top: 20%;
  left: 50%;
  transform: translateX(-50%) rotate(0deg);
  animation: beam-scan 3s ease-in-out infinite;
}

.beam-2 {
  top: 50%;
  right: 20%;
  transform: translateY(-50%) rotate(90deg);
  animation: beam-scan 3s ease-in-out infinite 0.5s;
}

.beam-3 {
  bottom: 20%;
  left: 50%;
  transform: translateX(-50%) rotate(180deg);
  animation: beam-scan 3s ease-in-out infinite 1s;
}

.beam-4 {
  top: 50%;
  left: 20%;
  transform: translateY(-50%) rotate(270deg);
  animation: beam-scan 3s ease-in-out infinite 1.5s;
}

/* Knowledge Particles */
.knowledge-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.particle {
  position: absolute;
  color: #00d4ff;
  font-size: 0.8rem;
  opacity: 0.7;
  animation: particle-float 4s ease-in-out infinite;
}

/* Oracle Status */
.oracle-status {
  text-align: center;
  position: relative;
  z-index: 10;
}

.oracle-status-text {
  color: #ffffff !important;
  font-size: 1.5rem !important;
  font-weight: 300 !important;
  margin-bottom: 1.5rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.75rem !important;
  animation: text-glow 2s ease-in-out infinite alternate !important;
}

.oracle-status-text i {
  color: #00d4ff !important;
  font-size: 1.2rem !important;
  animation: icon-pulse 1.5s ease-in-out infinite !important;
}

/* Medical Data Stream */
.medical-data-stream {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.data-symbol {
  color: #00d4ff;
  font-size: 1.2rem;
  font-weight: bold;
  opacity: 0.6;
  animation: symbol-drift 3s ease-in-out infinite;
  animation-delay: calc(var(--i) * 0.2s);
}

.animation-status {
  text-align: center;
  position: relative;
  z-index: 10;
}

.status-text {
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50px;
  border: 1px solid rgba(78, 205, 196, 0.3);
  backdrop-filter: blur(10px);
}

.status-text .text-glow {
  font-size: 1.2rem;
  font-weight: 600;
  background: linear-gradient(45deg, #4ecdc4, #667eea, #FFD700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: text-shimmer 2s ease-in-out infinite;
}

.status-text .text-glow.success {
  background: linear-gradient(45deg, #00FF41, #32CD32);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Modal Enhanced Transitions */
.modal-overlay.with-animation {
  animation: modal-overlay-enter 0.8s ease-out 1s both;
}

.modal-container.animating {
  animation: modal-container-enter 0.6s ease-out 1.2s both;
  transform: scale(0.8);
  opacity: 0;
}

/* Keyframe Animations */
@keyframes overlay-appear {
  0% {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  100% {
    opacity: 1;
    backdrop-filter: blur(15px);
  }
}

@keyframes materializing-glow {
  0%, 100% {
    box-shadow: inset 0 0 50px rgba(78, 205, 196, 0.1);
  }
  50% {
    box-shadow: inset 0 0 100px rgba(78, 205, 196, 0.3);
  }
}

@keyframes expanding-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes fade-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    visibility: hidden;
  }
}

@keyframes hologram-spin {
  0% {
    transform: rotate(0deg);
    border-style: solid;
  }
  25% {
    border-style: dashed;
  }
  50% {
    transform: rotate(180deg);
    border-style: dotted;
  }
  75% {
    border-style: dashed;
  }
  100% {
    transform: rotate(360deg);
    border-style: solid;
  }
}

@keyframes logo-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 
      0 0 30px rgba(78, 205, 196, 0.5),
      inset 0 0 20px rgba(255, 255, 255, 0.1);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 
      0 0 50px rgba(78, 205, 196, 0.8),
      inset 0 0 30px rgba(255, 255, 255, 0.2);
  }
}

/* Oracle Animations */
@keyframes oracle-appear {
  0% {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  100% {
    opacity: 1;
    backdrop-filter: blur(20px);
  }
}

@keyframes oracle-scan {
  0%, 100% {
    background: linear-gradient(135deg, rgba(10, 25, 47, 0.98), rgba(25, 35, 60, 0.95));
  }
  50% {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(10, 25, 47, 0.98));
  }
}

@keyframes oracle-vision {
  0% {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.08), rgba(10, 25, 47, 0.98));
  }
  100% {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.25), rgba(25, 35, 60, 0.95));
  }
}

@keyframes oracle-complete {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    visibility: hidden;
  }
}

@keyframes oracle-eye-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 50px rgba(0, 212, 255, 0.8);
  }
}

@keyframes iris-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pupil-scan {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

@keyframes scan-sweep {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes ring-orbit {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes node-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.5);
    opacity: 1;
  }
}

@keyframes beam-scan {
  0%, 100% {
    opacity: 0.3;
    height: 80px;
  }
  50% {
    opacity: 1;
    height: 120px;
  }
}

@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

@keyframes text-glow {
  0% {
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
  }
  100% {
    text-shadow: 0 0 20px rgba(0, 212, 255, 0.8);
  }
}

@keyframes icon-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes symbol-drift {
  0%, 100% {
    transform: translateY(0px);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

@keyframes modal-overlay-enter {
  0% {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  100% {
    opacity: 1;
    backdrop-filter: blur(10px);
  }
}

@keyframes modal-container-enter {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(50px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes text-shimmer {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

@keyframes disconnectedPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 
      0 0 15px rgba(255, 107, 107, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    transform: scale(1.2);
    box-shadow: 
      0 0 25px rgba(255, 107, 107, 0.8),
      inset 0 1px 0 rgba(255, 255, 255, 0.5);
  }
}

@keyframes connectedGlow {
  0%, 100% {
    transform: scale(1);
    box-shadow: 
      0 0 15px rgba(81, 207, 102, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    transform: scale(1.15);
    box-shadow: 
      0 0 22px rgba(81, 207, 102, 0.8),
      inset 0 1px 0 rgba(255, 255, 255, 0.5);
  }
}

@keyframes ringPulse {
  0%, 100% {
    opacity: 0.8;
    stroke-width: 2.5;
  }
  50% {
    opacity: 1;
    stroke-width: 3;
  }
}


/* Oracle Initialization Animation Styles */
.oracle-animation-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, 
    rgba(5, 10, 30, 0.98) 0%, 
    rgba(0, 5, 15, 0.95) 70%, 
    rgba(0, 0, 5, 1) 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: 20px;
  overflow: hidden;
  backdrop-filter: blur(15px);
}

.quantum-core {
  position: relative;
  width: 400px;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 3rem;
}

.core-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.neural-core {
  position: relative;
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.core-orb {
  position: relative;
  width: 120px;
  height: 120px;
  background: radial-gradient(circle at 30% 30%, 
    rgba(0, 212, 255, 0.8) 0%, 
    rgba(0, 150, 255, 0.6) 40%, 
    rgba(0, 100, 255, 0.4) 70%, 
    rgba(0, 50, 150, 0.8) 100%);
  border-radius: 50%;
  box-shadow: 
    0 0 40px rgba(0, 212, 255, 0.6),
    0 0 80px rgba(0, 212, 255, 0.4),
    inset 0 0 30px rgba(255, 255, 255, 0.2);
  animation: core-pulse 2s ease-in-out infinite;
}

.orb-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80%;
  height: 80%;
  background: radial-gradient(circle, 
    rgba(255, 255, 255, 0.4) 0%, 
    rgba(0, 212, 255, 0.6) 50%, 
    transparent 100%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: orb-shimmer 3s ease-in-out infinite alternate;
}

.orb-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: 2px solid rgba(0, 212, 255, 0.6);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse-ring 2s ease-out infinite;
}

.neural-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  border: 2px solid rgba(0, 212, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.8s ease;
  opacity: 0;
}

.neural-ring.active {
  opacity: 1;
  border-color: rgba(0, 212, 255, 0.8);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
}

.ring-1 {
  width: 180px;
  height: 180px;
  animation: rotate-clockwise 8s linear infinite;
}

.ring-2 {
  width: 240px;
  height: 240px;
  animation: rotate-counter 12s linear infinite;
}

.ring-3 {
  width: 300px;
  height: 300px;
  animation: rotate-clockwise 16s linear infinite;
}

.ring-segment {
  position: absolute;
  width: 8px;
  height: 8px;
  background: rgba(0, 212, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(0, 212, 255, 0.6);
  animation: segment-pulse 2s ease-in-out infinite;
}

.ring-1 .ring-segment:nth-child(1) { top: 0; left: 50%; transform: translateX(-50%); animation-delay: 0s; }
.ring-1 .ring-segment:nth-child(2) { top: 25%; right: 0; animation-delay: 0.25s; }
.ring-1 .ring-segment:nth-child(3) { bottom: 0; right: 25%; animation-delay: 0.5s; }
.ring-1 .ring-segment:nth-child(4) { bottom: 0; left: 50%; transform: translateX(-50%); animation-delay: 0.75s; }
.ring-1 .ring-segment:nth-child(5) { bottom: 25%; left: 0; animation-delay: 1s; }
.ring-1 .ring-segment:nth-child(6) { top: 0; left: 25%; animation-delay: 1.25s; }
.ring-1 .ring-segment:nth-child(7) { top: 25%; left: 0; animation-delay: 1.5s; }
.ring-1 .ring-segment:nth-child(8) { top: 0; right: 25%; animation-delay: 1.75s; }

/* Data Streams */
.data-streams {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.stream {
  position: absolute;
  width: 2px;
  height: 40px;
  background: linear-gradient(to bottom, 
    transparent 0%, 
    rgba(0, 212, 255, 0.8) 20%, 
    rgba(0, 255, 150, 0.6) 50%, 
    rgba(0, 212, 255, 0.8) 80%, 
    transparent 100%);
  animation: stream-flow 2s ease-in-out infinite;
  animation-delay: var(--stream-delay);
}

.stream:nth-child(1) { top: 10%; left: 20%; transform: rotate(45deg); }
.stream:nth-child(2) { top: 20%; right: 15%; transform: rotate(-30deg); }
.stream:nth-child(3) { bottom: 15%; left: 25%; transform: rotate(60deg); }
.stream:nth-child(4) { bottom: 20%; right: 20%; transform: rotate(-45deg); }
.stream:nth-child(5) { top: 50%; left: 10%; transform: rotate(90deg); }
.stream:nth-child(6) { top: 50%; right: 10%; transform: rotate(90deg); }

/* Quantum Field */
.quantum-field {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.quantum-particle {
  position: absolute;
  width: 3px;
  height: 3px;
  background: rgba(0, 212, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 0 6px rgba(0, 212, 255, 0.6);
  animation: quantum-float var(--particle-duration) ease-in-out infinite;
  animation-delay: var(--particle-delay);
  top: var(--particle-y);
  left: var(--particle-x);
}

/* Completion Wave */
.completion-wave {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.wave-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  border: 2px solid rgba(0, 212, 255, 0.8);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: completion-wave 3s ease-out infinite;
  animation-delay: var(--wave-delay);
}

/* Holographic Interface */
.holo-interface {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.holo-panel {
  position: absolute;
}

.panel-left {
  top: 20%;
  left: 5%;
  width: 80px;
  height: 200px;
}

.panel-right {
  top: 20%;
  right: 5%;
  width: 80px;
  height: 200px;
}

.panel-bottom {
  bottom: 10%;
  left: 50%;
  transform: translateX(-50%);
  width: 300px;
  height: 20px;
}

.holo-line {
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(0, 212, 255, 0.8) 20%, 
    rgba(0, 255, 150, 0.6) 50%, 
    rgba(0, 212, 255, 0.8) 80%, 
    transparent 100%);
  margin-bottom: 8px;
  animation: holo-scan 2s ease-in-out infinite;
  animation-delay: var(--line-delay);
  opacity: 0;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(0, 212, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, 
    rgba(0, 212, 255, 0.8) 0%, 
    rgba(0, 255, 150, 1) 50%, 
    rgba(0, 212, 255, 0.8) 100%);
  border-radius: 2px;
  animation: progress-fill 4s ease-in-out infinite;
}

/* JARVIS-style Elements */
.jarvis-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 0.3;
}

.grid-line {
  position: absolute;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(0, 212, 255, 0.6) 20%, 
    rgba(0, 255, 150, 0.4) 50%, 
    rgba(0, 212, 255, 0.6) 80%, 
    transparent 100%);
  animation: grid-pulse 3s ease-in-out infinite;
  animation-delay: calc(var(--line-index) * 0.1s);
}

.grid-line.horizontal {
  width: 100%;
  height: 1px;
  top: calc(12.5% * var(--line-index));
  left: 0;
}

.grid-line.vertical {
  width: 1px;
  height: 100%;
  top: 0;
  left: calc(12.5% * var(--line-index));
  background: linear-gradient(180deg, 
    transparent 0%, 
    rgba(0, 212, 255, 0.6) 20%, 
    rgba(0, 255, 150, 0.4) 50%, 
    rgba(0, 212, 255, 0.6) 80%, 
    transparent 100%);
}

.jarvis-nodes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.node {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(0, 212, 255, 0.9);
  border-radius: 50%;
  box-shadow: 0 0 12px rgba(0, 212, 255, 0.8);
  animation: node-pulse 2s ease-in-out infinite;
  animation-delay: var(--node-delay);
}

.node:nth-child(1) { top: 20%; left: 15%; }
.node:nth-child(2) { top: 30%; right: 20%; }
.node:nth-child(3) { bottom: 25%; left: 25%; }
.node:nth-child(4) { bottom: 35%; right: 15%; }
.node:nth-child(5) { top: 50%; left: 10%; }
.node:nth-child(6) { top: 50%; right: 10%; }
.node:nth-child(7) { top: 15%; left: 50%; }
.node:nth-child(8) { bottom: 15%; left: 50%; }
.node:nth-child(9) { top: 25%; left: 35%; }
.node:nth-child(10) { top: 25%; right: 35%; }
.node:nth-child(11) { bottom: 25%; left: 35%; }
.node:nth-child(12) { bottom: 25%; right: 35%; }

.arc-reactor {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.reactor-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  border: 2px solid rgba(0, 212, 255, 0.8);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: reactor-pulse 2s ease-in-out infinite;
  animation-delay: var(--reactor-delay);
}

.reactor-ring:nth-child(1) {
  width: 80px;
  height: 80px;
  border-color: rgba(255, 255, 255, 0.9);
}

.reactor-ring:nth-child(2) {
  width: 140px;
  height: 140px;
  border-color: rgba(0, 212, 255, 0.7);
}

.reactor-ring:nth-child(3) {
  width: 200px;
  height: 200px;
  border-color: rgba(0, 255, 150, 0.5);
}

/* Animation Text */
.animation-text {
  text-align: center;
  position: relative;
  z-index: 10;
}

.animation-text h2 {
  font-size: 2rem;
  margin: 0;
  color: white;
  font-weight: 700;
}

.text-quantum {
  background: linear-gradient(45deg, #00d4ff, #0099ff, #0066cc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
  animation: quantum-text 2s ease-in-out infinite alternate;
}

.text-quantum.success {
  background: linear-gradient(45deg, #00ff96, #00d4ff, #0099ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(0, 255, 150, 0.8);
}

.text-quantum.error {
  background: linear-gradient(45deg, #ff4757, #ff6b81, #ff9999);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(255, 71, 87, 0.8);
}

.text-quantum.complete {
  font-size: 2.5rem !important;
  background: linear-gradient(45deg, #00ff96, #ffffff, #00d4ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 50px rgba(0, 255, 150, 1);
  animation: completion-text 1.5s ease-in-out;
}

/* Oracle Animation Keyframes */
@keyframes core-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 
      0 0 40px rgba(0, 212, 255, 0.6),
      0 0 80px rgba(0, 212, 255, 0.4),
      inset 0 0 30px rgba(255, 255, 255, 0.2);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 
      0 0 60px rgba(0, 212, 255, 0.8),
      0 0 120px rgba(0, 212, 255, 0.6),
      inset 0 0 40px rgba(255, 255, 255, 0.3);
  }
}

@keyframes orb-shimmer {
  0% { opacity: 0.6; }
  100% { opacity: 1; }
}

@keyframes pulse-ring {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

@keyframes rotate-clockwise {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes rotate-counter {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(-360deg); }
}

@keyframes segment-pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
    box-shadow: 0 0 8px rgba(0, 212, 255, 0.6);
  }
  50% {
    opacity: 1;
    transform: scale(1.3);
    box-shadow: 0 0 15px rgba(0, 212, 255, 1);
  }
}

@keyframes stream-flow {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.5);
  }
  50% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(20px) scale(0.5);
  }
}

@keyframes quantum-float {
  0%, 100% {
    opacity: 0.4;
    transform: translateY(0) rotate(0deg) scale(1);
  }
  25% {
    opacity: 0.8;
    transform: translateY(-10px) rotate(90deg) scale(1.2);
  }
  50% {
    opacity: 1;
    transform: translateY(-5px) rotate(180deg) scale(1);
  }
  75% {
    opacity: 0.8;
    transform: translateY(-15px) rotate(270deg) scale(1.2);
  }
}

@keyframes completion-wave {
  0% {
    width: 50px;
    height: 50px;
    opacity: 1;
  }
  100% {
    width: 400px;
    height: 400px;
    opacity: 0;
  }
}

@keyframes holo-scan {
  0% {
    opacity: 0;
    transform: scaleX(0);
  }
  50% {
    opacity: 1;
    transform: scaleX(1);
  }
  100% {
    opacity: 0;
    transform: scaleX(0);
  }
}

@keyframes progress-fill {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

@keyframes quantum-text {
  0% {
    background-position: 0% 50%;
    text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
  }
  100% {
    background-position: 100% 50%;
    text-shadow: 0 0 40px rgba(0, 212, 255, 0.8);
  }
}

@keyframes completion-text {
  0% {
    transform: scale(1);
    filter: brightness(1);
    text-shadow: 0 0 50px rgba(0, 255, 150, 1);
  }
  50% {
    transform: scale(1.1);
    filter: brightness(1.5);
    text-shadow: 0 0 80px rgba(0, 255, 150, 1.5);
  }
  100% {
    transform: scale(1);
    filter: brightness(1);
    text-shadow: 0 0 50px rgba(0, 255, 150, 1);
  }
}

/* JARVIS Animation Keyframes */
@keyframes grid-pulse {
  0%, 100% {
    opacity: 0.2;
    transform: scaleX(0.8);
  }
  50% {
    opacity: 0.8;
    transform: scaleX(1.2);
  }
}

@keyframes node-pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
    box-shadow: 0 0 12px rgba(0, 212, 255, 0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.5);
    box-shadow: 0 0 20px rgba(0, 212, 255, 1);
  }
}

@keyframes reactor-pulse {
  0% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
  }
  100% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Failed animation styles */
.oracle-animation-overlay.failed .core-orb {
  animation: core-failure 1s ease-in-out;
  background: radial-gradient(circle at 30% 30%, 
    rgba(255, 71, 87, 0.8) 0%, 
    rgba(255, 107, 107, 0.6) 40%, 
    rgba(200, 50, 50, 0.4) 70%, 
    rgba(150, 30, 30, 0.8) 100%);
}

.oracle-animation-overlay.failed .neural-ring.active {
  animation: ring-failure 1s ease-in-out;
  border-color: rgba(255, 71, 87, 0.8);
  box-shadow: 0 0 20px rgba(255, 71, 87, 0.4);
}

@keyframes core-failure {
  0%, 100% { 
    transform: scale(1);
    filter: brightness(1);
  }
  25% { 
    transform: scale(1.1) rotate(5deg);
    filter: brightness(1.5);
  }
  75% { 
    transform: scale(0.9) rotate(-5deg);
    filter: brightness(0.8);
  }
}

@keyframes ring-failure {
  0% { opacity: 1; }
  50% { opacity: 0.3; }
  100% { opacity: 0.1; }
}
</style>