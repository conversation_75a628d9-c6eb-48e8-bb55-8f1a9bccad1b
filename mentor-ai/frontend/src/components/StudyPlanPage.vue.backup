<template>
  <div class="study-plan-page">
    <!-- Minimal Background -->
    <div class="background-effects">
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="grid-overlay"></div>
    </div>

    <!-- Minimal Header -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-icon">
            <i class="fas fa-graduation-cap"></i>
          </div>
          <div class="page-info">
            <h1 class="page-title">
              Plano de Estudo
              <span class="title-badge">Inteligente</span>
            </h1>
            <p class="page-subtitle">Otimizado por IA para máximo aprendizado</p>
          </div>
        </div>
        <div class="header-right">
          <div class="header-stats">
            <div class="stat-item" v-for="stat in headerStats" :key="stat.id">
              <span class="stat-value">{{ stat.value }}</span>
              <span class="stat-label">{{ stat.label }}</span>
            </div>
          </div>
          <button @click="showPlanWizard = true" class="create-plan-btn">
            <i class="fas fa-plus"></i>
            <span>Novo Plano</span>
          </button>
        </div>
      </div>
    </div>

    <!-- AI Assistant Bar Minimal -->
    <div class="ai-assistant-bar">
      <div class="assistant-content">
        <i class="fas fa-lightbulb assistant-icon"></i>
        <p class="assistant-message">{{ aiMessage }}</p>
        <div class="assistant-actions">
          <button @click="applyAISuggestion" class="btn-apply">
            <i class="fas fa-check"></i>
            Aplicar
          </button>
          <button @click="refreshAISuggestion" class="btn-refresh">
            <i class="fas fa-sync-alt"></i>
            Nova sugestão
          </button>
        </div>
        <div class="assistant-metrics">
          <div class="metric">
            <span>Eficiência</span>
            <div class="bar"><div class="fill" style="width: 87%"></div></div>
          </div>
          <div class="metric">
            <span>Cobertura</span>
            <div class="bar"><div class="fill" style="width: 92%"></div></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
      <!-- Quick Stats Row Minimal -->
      <div class="stats-row">
        <div class="stat-card" v-for="stat in quickStats" :key="stat.id">
          <div class="stat-icon">
            <i :class="stat.icon"></i>
          </div>
          <div class="stat-content">
            <h3>{{ stat.value }}</h3>
            <p>{{ stat.label }}</p>
          </div>
          <div class="stat-trend" :class="stat.trend">
            <i :class="stat.trend === 'up' ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
            <span>{{ stat.change }}%</span>
          </div>
        </div>
      </div>

      <!-- Main Grid Layout -->
      <div class="content-grid">
        <!-- Left Side: Plans & Insights -->
        <div class="left-section">
          <!-- Active Plans Section -->
          <div class="plans-card modern-card">
            <div class="card-header">
              <div class="header-left">
                <div class="section-icon">
                  <i class="fas fa-book-open"></i>
                </div>
                <h2>Planos Ativos</h2>
                <span class="badge">{{ activePlans.length }}</span>
              </div>
              <div class="view-controls">
                <button 
                  v-for="mode in ['grid', 'list']" 
                  :key="mode"
                  @click="viewMode = mode" 
                  :class="['view-btn', { active: viewMode === mode }]"
                >
                  <i :class="mode === 'grid' ? 'fas fa-th' : 'fas fa-list'"></i>
                </button>
              </div>
            </div>

            <div :class="['plans-container', viewMode]">
              <transition-group name="plan-list">
                <div 
                  v-for="plan in activePlans" 
                  :key="plan.id"
                  class="plan-item"
                  @click="selectPlan(plan)"
                  :class="{ selected: selectedPlan?.id === plan.id }"
                >
                  <div class="plan-header">
                    <div class="plan-icon" :style="{ backgroundColor: plan.color + '15', color: plan.color }">
                      <i :class="plan.icon"></i>
                    </div>
                    <span class="plan-priority" :class="plan.priority">{{ plan.priority }}</span>
                  </div>
                  <h3>{{ plan.title }}</h3>
                  <p>{{ plan.description }}</p>
                  <div class="plan-stats">
                    <span><i class="fas fa-tasks"></i> {{ plan.completedTasks }}/{{ plan.totalTasks }}</span>
                    <span><i class="fas fa-clock"></i> {{ plan.hoursSpent }}h</span>
                    <span><i class="fas fa-fire"></i> {{ plan.streak }}d</span>
                  </div>
                  <div class="plan-progress">
                    <div class="progress-bar">
                      <div class="progress-fill" :style="{ width: plan.progress + '%' }"></div>
                    </div>
                    <span>{{ plan.progress }}%</span>
                  </div>
                </div>
              </transition-group>
            </div>

            <!-- Empty State -->
            <div v-if="activePlans.length === 0" class="empty-state">
              <div class="empty-icon">
                <i class="fas fa-book"></i>
                <div class="icon-bg"></div>
              </div>
              <h3>Nenhum plano ativo</h3>
              <p>Crie seu primeiro plano de estudo personalizado</p>
              <button @click="showPlanWizard = true" class="empty-cta">
                <i class="fas fa-plus"></i>
                Criar Plano
              </button>
            </div>
          </div>

          <!-- AI Insights Section Minimal -->
          <div class="insights-card modern-card">
            <div class="card-header">
              <h2><i class="fas fa-brain"></i> Insights Inteligentes</h2>
              <button class="refresh-btn" :class="{ spinning: refreshingInsights }">
                <i class="fas fa-sync-alt"></i>
              </button>
            </div>

            <div class="insights-list">
              <div 
                v-for="(insight, index) in aiInsights" 
                :key="index"
                class="insight-item"
              >
                <i :class="insight.icon" :style="{ color: insight.color }"></i>
                <div class="insight-content">
                  <h4>{{ insight.title }}</h4>
                  <p>{{ insight.description }}</p>
                </div>
                <button v-if="insight.actionable" @click="applyInsight(insight)" class="apply-btn">
                  {{ insight.actionText }}
                </button>
              </div>
            </div>
          </div>

          <!-- Recommendations Section -->
          <div class="recommendations-card modern-card">
            <div class="card-header">
              <div class="header-left">
                <div class="section-icon">
                  <i class="fas fa-lightbulb"></i>
                </div>
                <h2>Recomendações</h2>
              </div>
            </div>

            <div class="recommendations-grid">
              <div 
                v-for="rec in recommendations" 
                :key="rec.id"
                class="recommendation-item"
                @click="applyRecommendation(rec)"
              >
                <div class="rec-icon" :style="{ background: rec.color }">
                  <i :class="rec.icon"></i>
                </div>
                <div class="rec-content">
                  <h4>{{ rec.title }}</h4>
                  <p>{{ rec.benefit }}</p>
                </div>
                <div class="rec-arrow">
                  <i class="fas fa-arrow-right"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Side: Calendar -->
        <div class="calendar-section">
          <div class="calendar-wrapper modern-card">
            <CalendarView :embedded="true" />
          </div>
        </div>
      </div>
    </div>

    <!-- Plan Wizard Modal -->
    <transition name="modal">
      <div v-if="showPlanWizard" class="modal-overlay" @click.self="showPlanWizard = false">
        <div class="modal-content plan-wizard">
          <button @click="showPlanWizard = false" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
          <h2>Criar Novo Plano de Estudo</h2>
          <!-- Wizard content here -->
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import CalendarView from './CalendarView.vue';

export default {
  name: 'StudyPlanPage',
  components: {
    CalendarView
  },
  data() {
    return {
      viewMode: 'grid',
      showPlanWizard: false,
      selectedPlan: null,
      refreshingInsights: false,
      aiMessage: '💡 Baseado no seu desempenho, recomendo aumentar o tempo de revisão de Matemática em 15 minutos por dia.',
      headerStats: [
        {
          id: 1,
          value: 142,
          label: 'Horas Estudadas',
          icon: 'fas fa-clock',
          gradient: 'linear-gradient(135deg, #667eea, #764ba2)'
        },
        {
          id: 2,
          value: 28,
          label: 'Dias de Sequência',
          icon: 'fas fa-fire',
          gradient: 'linear-gradient(135deg, #f093fb, #f5576c)'
        },
        {
          id: 3,
          value: '94%',
          label: 'Taxa de Conclusão',
          icon: 'fas fa-check-circle',
          gradient: 'linear-gradient(135deg, #4facfe, #00f2fe)'
        }
      ],
      quickStats: [
        {
          id: 1,
          value: '3.8',
          label: 'Média de Desempenho',
          icon: 'fas fa-chart-line',
          gradient: 'linear-gradient(135deg, #667eea20, #764ba220)',
          progress: 76,
          trend: 'up',
          change: 12
        },
        {
          id: 2,
          value: '24',
          label: 'Revisões Pendentes',
          icon: 'fas fa-redo',
          gradient: 'linear-gradient(135deg, #f093fb20, #f5576c20)',
          progress: 45,
          trend: 'down',
          change: 8
        },
        {
          id: 3,
          value: '156',
          label: 'Tópicos Dominados',
          icon: 'fas fa-trophy',
          gradient: 'linear-gradient(135deg, #4facfe20, #00f2fe20)',
          progress: 89,
          trend: 'up',
          change: 23
        },
        {
          id: 4,
          value: '7.2h',
          label: 'Tempo Médio/Semana',
          icon: 'fas fa-hourglass-half',
          gradient: 'linear-gradient(135deg, #fa709a20, #fee14020)',
          progress: 68,
          trend: 'up',
          change: 15
        }
      ],
      activePlans: [
        {
          id: 1,
          title: 'Cálculo Avançado',
          description: 'Preparação completa para P2 com foco em integrais',
          icon: 'fas fa-square-root-alt',
          color: '#667eea',
          gradient: 'linear-gradient(135deg, #667eea20, #764ba220)',
          duration: '6 semanas',
          priority: 'alta',
          progress: 67,
          completedTasks: 24,
          totalTasks: 36,
          hoursSpent: 48,
          streak: 12
        },
        {
          id: 2,
          title: 'Física Quântica',
          description: 'Fundamentos e aplicações da mecânica quântica',
          icon: 'fas fa-atom',
          color: '#f093fb',
          gradient: 'linear-gradient(135deg, #f093fb20, #f5576c20)',
          duration: '8 semanas',
          priority: 'média',
          progress: 42,
          completedTasks: 15,
          totalTasks: 36,
          hoursSpent: 31,
          streak: 8
        },
        {
          id: 3,
          title: 'Algoritmos & Estruturas',
          description: 'Dominando complexidade e otimização',
          icon: 'fas fa-code-branch',
          color: '#4facfe',
          gradient: 'linear-gradient(135deg, #4facfe20, #00f2fe20)',
          duration: '4 semanas',
          priority: 'alta',
          progress: 89,
          completedTasks: 32,
          totalTasks: 36,
          hoursSpent: 64,
          streak: 18
        }
      ],
      aiInsights: [
        {
          type: 'performance',
          icon: 'fas fa-chart-line',
          color: 'linear-gradient(135deg, #667eea, #764ba2)',
          title: 'Desempenho em Alta',
          description: 'Sua taxa de retenção aumentou 23% nas últimas 2 semanas.',
          actionable: true,
          actionText: 'Ver Detalhes'
        },
        {
          type: 'warning',
          icon: 'fas fa-exclamation-triangle',
          color: 'linear-gradient(135deg, #f093fb, #f5576c)',
          title: 'Atenção Necessária',
          description: 'Física Quântica precisa de mais tempo de revisão.',
          actionable: true,
          actionText: 'Ajustar Plano'
        },
        {
          type: 'suggestion',
          icon: 'fas fa-lightbulb',
          color: 'linear-gradient(135deg, #4facfe, #00f2fe)',
          title: 'Dica de Estudo',
          description: 'Adicione sessões de prática aos domingos para melhor fixação.',
          actionable: true,
          actionText: 'Implementar'
        }
      ],
      recommendations: [
        {
          id: 1,
          icon: 'fas fa-brain',
          color: 'linear-gradient(135deg, #30cfd0, #330867)',
          title: 'Técnica Feynman',
          benefit: 'Aumente a compreensão em 40%'
        },
        {
          id: 2,
          icon: 'fas fa-bed',
          color: 'linear-gradient(135deg, #a8edea, #fed6e3)',
          title: 'Revisão Pré-Sono',
          benefit: 'Melhore a consolidação da memória'
        },
        {
          id: 3,
          icon: 'fas fa-dumbbell',
          color: 'linear-gradient(135deg, #ff6b6b, #4ecdc4)',
          title: 'Exercícios Intervalados',
          benefit: 'Aumente o foco em 25%'
        }
      ]
    };
  },
  methods: {
    selectPlan(plan) {
      this.selectedPlan = plan;
      if (this.$toast) {
        this.$toast.info(`Plano ${plan.title} selecionado`);
      }
    },
    applyAISuggestion() {
      if (this.$toast) {
        this.$toast.success('Sugestão aplicada com sucesso!');
      }
      console.log('AI suggestion applied');
    },
    refreshAISuggestion() {
      const suggestions = [
        '💡 Baseado no seu desempenho, recomendo aumentar o tempo de revisão de Matemática em 15 minutos por dia.',
        '🧠 Análise detectou que você aprende melhor com flashcards. Considere criar 10 novos cards por dia.',
        '🌅 Seus melhores resultados são pela manhã. Que tal começar o dia com 30 minutos de revisão?',
        '⚛️ Você está próximo de dominar Física Quântica. Adicione mais 2 exercícios diários para acelerar.',
        '⏱️ Padrão identificado: pausas de 5 minutos a cada 25 minutos aumentam sua retenção em 23%.'
      ];
      
      this.refreshingInsights = true;
      setTimeout(() => {
        const randomIndex = Math.floor(Math.random() * suggestions.length);
        this.aiMessage = suggestions[randomIndex];
        this.refreshingInsights = false;
        if (this.$toast) {
          this.$toast.info('Nova sugestão gerada');
        }
      }, 1000);
    },
    applyInsight(insight) {
      if (this.$toast) {
        this.$toast.success(`Aplicando: ${insight.title}`);
      }
      console.log('Applying insight:', insight);
    },
    applyRecommendation(rec) {
      if (this.$toast) {
        this.$toast.info(`Implementando: ${rec.title}`);
      }
      console.log('Implementing recommendation:', rec);
    }
  }
};
</script>

<style scoped>
/* Variables */
:root {
  --primary: #6366f1;
  --secondary: #8b5cf6;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --dark-bg: #0a0f1b;
  --card-bg: #1a2332;
  --text-primary: #e4e6eb;
  --text-secondary: #94a3b8;
  --border-color: rgba(148, 163, 184, 0.1);
}

/* Base */
.study-plan-page {
  min-height: 100vh;
  background: var(--dark-bg);
  color: var(--text-primary);
  position: relative;
}

/* Minimal Background */
.background-effects {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.2;
}

.orb-1 {
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, var(--primary), transparent);
  top: -200px;
  left: -200px;
}

.orb-2 {
  width: 800px;
  height: 800px;
  background: radial-gradient(circle, var(--secondary), transparent);
  bottom: -300px;
  right: -300px;
}

.grid-overlay {
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(99, 102, 241, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(99, 102, 241, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Minimal Header */
.page-header {
  position: relative;
  z-index: 10;
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  padding: 1.5rem 0;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-icon {
  width: 48px;
  height: 48px;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: var(--primary);
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.title-badge {
  padding: 0.25rem 0.625rem;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  border-radius: 6px;
  font-size: 0.625rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.page-subtitle {
  margin: 0.25rem 0 0;
  font-size: 0.813rem;
  color: var(--text-secondary);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.header-stats {
  display: flex;
  gap: 2rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary);
}

.stat-label {
  font-size: 0.688rem;
  color: var(--text-secondary);
  white-space: nowrap;
}

.create-plan-btn {
  padding: 0.625rem 1.25rem;
  background: var(--primary);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  font-size: 0.813rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.create-plan-btn:hover {
  background: color-mix(in srgb, var(--primary) 85%, black);
  transform: translateY(-1px);
}

/* AI Assistant Bar Minimal */
.ai-assistant-bar {
  background: rgba(26, 35, 50, 0.5);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 0;
  position: relative;
  z-index: 5;
}

.assistant-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.assistant-icon {
  font-size: 1rem;
  color: var(--primary);
}

.assistant-message {
  flex: 1;
  font-size: 0.875rem;
  color: var(--text-primary);
}

.assistant-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-apply,
.btn-refresh {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.btn-apply {
  background: var(--primary);
  color: white;
}

.btn-apply:hover {
  background: color-mix(in srgb, var(--primary) 85%, black);
}

.btn-refresh {
  background: rgba(148, 163, 184, 0.1);
  color: var(--text-primary);
}

.btn-refresh:hover {
  background: rgba(148, 163, 184, 0.2);
}

.assistant-metrics {
  display: flex;
  gap: 1rem;
}

.assistant-metrics .metric {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.assistant-metrics .metric span {
  font-size: 0.688rem;
  color: var(--text-secondary);
}

.assistant-metrics .bar {
  width: 60px;
  height: 3px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.assistant-metrics .fill {
  height: 100%;
  background: var(--primary);
  border-radius: 3px;
}

/* Main Content */
.main-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  position: relative;
  z-index: 5;
}

/* Stats Row Minimal */
.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.25rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  border-color: rgba(99, 102, 241, 0.3);
}

.stat-card .stat-icon {
  width: 40px;
  height: 40px;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
}

.stat-card .stat-content {
  flex: 1;
}

.stat-card .stat-content h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.stat-card .stat-content p {
  margin: 0;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
}

.stat-trend.up {
  color: var(--success);
}

.stat-trend.down {
  color: var(--danger);
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 2rem;
}

/* Modern Card */
.modern-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s;
}

.modern-card:hover {
  border-color: rgba(99, 102, 241, 0.2);
}

.card-header {
  padding: 1.25rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-header h2 i {
  font-size: 0.875rem;
  opacity: 0.7;
}

/* Plans Section */
.plans-card {
  margin-bottom: 1.5rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-icon {
  width: 32px;
  height: 32px;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
  font-size: 0.875rem;
}

.badge {
  padding: 0.125rem 0.5rem;
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary);
  border-radius: 12px;
  font-size: 0.688rem;
  font-weight: 600;
}

.view-controls {
  display: flex;
  gap: 0.25rem;
  background: rgba(148, 163, 184, 0.05);
  padding: 0.25rem;
  border-radius: 6px;
}

.view-btn {
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  border-radius: 4px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-btn:hover {
  background: rgba(148, 163, 184, 0.1);
}

.view-btn.active {
  background: var(--primary);
  color: white;
}

.plans-container {
  padding: 1.25rem;
}

.plan-item {
  background: rgba(30, 41, 59, 0.3);
  border: 1px solid transparent;
  border-radius: 12px;
  padding: 1.25rem;
  margin-bottom: 1rem;
  cursor: pointer;
  transition: all 0.2s;
}

.plan-item:hover {
  background: rgba(30, 41, 59, 0.5);
  border-color: rgba(99, 102, 241, 0.2);
  transform: translateX(4px);
}

.plan-item.selected {
  background: rgba(99, 102, 241, 0.05);
  border-color: var(--primary);
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.plan-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.plan-priority {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.688rem;
  font-weight: 500;
  text-transform: capitalize;
}

.plan-priority.alta {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

.plan-priority.média {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.plan-priority.baixa {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.plan-item h3 {
  margin: 0 0 0.5rem;
  font-size: 0.938rem;
  font-weight: 600;
}

.plan-item p {
  margin: 0 0 1rem;
  font-size: 0.813rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

.plan-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.plan-stats span {
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.plan-stats i {
  opacity: 0.7;
}

.plan-progress {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--primary);
  border-radius: 2px;
  transition: width 0.6s ease;
}

.plan-progress span {
  font-size: 0.688rem;
  font-weight: 600;
  color: var(--primary);
}

/* Empty State */
.empty-state {
  padding: 3rem;
  text-align: center;
}

.empty-icon {
  width: 80px;
  height: 80px;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
  color: var(--primary);
  position: relative;
}

.empty-state h3 {
  margin: 0 0 0.5rem;
  font-size: 1.125rem;
}

.empty-state p {
  margin: 0 0 1.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.empty-cta {
  padding: 0.75rem 1.5rem;
  background: var(--primary);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.empty-cta:hover {
  background: color-mix(in srgb, var(--primary) 85%, black);
  transform: translateY(-1px);
}

/* Insights Section */
.insights-card {
  margin-bottom: 1.5rem;
}

.refresh-btn {
  width: 32px;
  height: 32px;
  background: rgba(148, 163, 184, 0.1);
  border: none;
  border-radius: 6px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary);
}

.refresh-btn.spinning i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.insights-list {
  padding: 1.25rem;
}

.insight-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  margin-bottom: 0.75rem;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 10px;
  transition: all 0.2s;
}

.insight-item:hover {
  background: rgba(30, 41, 59, 0.5);
}

.insight-item i {
  font-size: 1rem;
  margin-top: 0.125rem;
}

.insight-content {
  flex: 1;
}

.insight-content h4 {
  margin: 0 0 0.25rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.insight-content p {
  margin: 0;
  font-size: 0.813rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

.apply-btn {
  padding: 0.375rem 0.75rem;
  background: var(--primary);
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 0.688rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.apply-btn:hover {
  background: color-mix(in srgb, var(--primary) 85%, black);
}

/* Recommendations */
.recommendations-grid {
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s;
}

.recommendation-item:hover {
  background: rgba(30, 41, 59, 0.5);
  transform: translateX(4px);
}

.rec-icon {
  width: 40px;
  height: 40px;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
}

.rec-content {
  flex: 1;
}

.rec-content h4 {
  margin: 0 0 0.125rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.rec-content p {
  margin: 0;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.rec-arrow {
  color: var(--text-secondary);
  opacity: 0.5;
  transition: all 0.2s;
}

.recommendation-item:hover .rec-arrow {
  opacity: 1;
  transform: translateX(2px);
}

/* Calendar Section */
.calendar-section {
  position: sticky;
  top: 2rem;
  height: fit-content;
}

.calendar-wrapper {
  padding: 1rem;
}

/* Modal */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 2rem;
  max-width: 600px;
  width: 90%;
  position: relative;
}

.close-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 32px;
  height: 32px;
  background: rgba(148, 163, 184, 0.1);
  border: none;
  border-radius: 6px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

/* Transitions */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-from .modal-content,
.modal-leave-to .modal-content {
  transform: scale(0.9);
}

.plan-list-enter-active,
.plan-list-leave-active {
  transition: all 0.3s;
}

.plan-list-enter-from,
.plan-list-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* Responsive */
@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .calendar-section {
    position: static;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-stats {
    display: none;
  }
  
  .assistant-content {
    flex-wrap: wrap;
  }
  
  .assistant-metrics {
    width: 100%;
    justify-content: space-between;
  }
  
  .stats-row {
    grid-template-columns: 1fr;
  }
}
</style>

@keyframes orbFloat {
  0%, 100% { 
    transform: translate(0, 0) scale(1) rotate(0deg); 
  }
  25% { 
    transform: translate(50px, -80px) scale(1.1) rotate(90deg); 
  }
  50% { 
    transform: translate(-40px, 60px) scale(0.9) rotate(180deg); 
  }
  75% { 
    transform: translate(-60px, -40px) scale(1.05) rotate(270deg); 
  }
}

.floating-shapes {
  position: absolute;
  inset: 0;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255,255,255,0.05), rgba(255,255,255,0.02));
  backdrop-filter: blur(5px);
  animation: shapeFloat 20s ease-in-out infinite;
}

.shape-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
}

.shape-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: -7s;
}

.shape-3 {
  width: 100px;
  height: 100px;
  bottom: 15%;
  left: 40%;
  animation-delay: -14s;
  clip-path: polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%);
}

@keyframes shapeFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-30px) rotate(180deg); }
}

.grid-overlay {
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(255,255,255,0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255,255,255,0.03) 1px, transparent 1px);
  background-size: 80px 80px;
  animation: gridMove 30s linear infinite;
}

@keyframes gridMove {
  from { transform: translate(0, 0); }
  to { transform: translate(80px, 80px); }
}

.animated-lines {
  position: absolute;
  inset: 0;
  overflow: hidden;
}

.line {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.6), transparent);
  height: 1px;
  width: 300%;
  animation: lineMove 12s linear infinite;
}

.line-1 { top: 20%; animation-delay: 0s; }
.line-2 { top: 40%; animation-delay: 3s; }
.line-3 { top: 60%; animation-delay: 6s; }
.line-4 { top: 80%; animation-delay: 9s; }

@keyframes lineMove {
  from { transform: translateX(-100%); }
  to { transform: translateX(100%); }
}

.particle-field {
  position: absolute;
  inset: 0;
  background-image: 
    radial-gradient(2px 2px at 20% 30%, white, transparent),
    radial-gradient(2px 2px at 60% 70%, white, transparent),
    radial-gradient(1px 1px at 90% 10%, white, transparent);
  background-size: 700px 700px;
  animation: particlesFloat 150s linear infinite;
  opacity: 0.1;
}

@keyframes particlesFloat {
  from { transform: translateY(0) rotate(0deg); }
  to { transform: translateY(-700px) rotate(360deg); }
}

/* Page Header */
.page-header {
  position: relative;
  padding: 2rem 2rem 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 10;
}

.header-background {
  position: absolute;
  inset: 0;
  background: 
    radial-gradient(circle at 20% 50%, rgba(102, 126, 234, 0.1), transparent 50%),
    radial-gradient(circle at 80% 50%, rgba(240, 147, 251, 0.1), transparent 50%);
  animation: headerShift 20s ease-in-out infinite;
}

@keyframes headerShift {
  0%, 100% { transform: scale(1) translateX(0); }
  50% { transform: scale(1.05) translateX(10px); }
}

.header-content {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
  max-width: 1600px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.page-icon {
  position: relative;
}

.icon-wrapper {
  position: relative;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 24px;
  font-size: 2rem;
  color: white;
}

.icon-ring {
  position: absolute;
  inset: -4px;
  border-radius: 28px;
  border: 2px solid;
  opacity: 0.3;
}

.ring-1 {
  border-color: #667eea;
  animation: ringPulse 3s ease-in-out infinite;
}

.ring-2 {
  border-color: #764ba2;
  animation: ringPulse 3s ease-in-out infinite 0.5s;
  transform: scale(1.1);
}

.ring-3 {
  border-color: #f093fb;
  animation: ringPulse 3s ease-in-out infinite 1s;
  transform: scale(1.2);
}

@keyframes ringPulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.05); opacity: 0.6; }
}

.icon-pulse {
  position: absolute;
  inset: -30px;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.4), transparent);
  filter: blur(25px);
  animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { opacity: 0.4; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

.page-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0;
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.title-gradient {
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 5s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { filter: hue-rotate(0deg); }
  50% { filter: hue-rotate(20deg); }
}

.title-accent {
  color: white;
}

.page-subtitle {
  margin: 0.5rem 0 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-subtitle i {
  color: #fee140;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.2) rotate(180deg); }
}

.header-right {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.header-stats {
  display: flex;
  gap: 1.5rem;
}

.stat-item {
  position: relative;
  overflow: hidden;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.stat-background {
  position: absolute;
  inset: 0;
  opacity: 0.1;
  transition: opacity 0.3s ease;
}

.stat-item:hover .stat-background {
  opacity: 0.2;
}

.stat-content {
  position: relative;
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
}

.stat-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  line-height: 1;
}

.stat-value[data-value]::after {
  content: attr(data-value);
  position: absolute;
  color: transparent;
  background: linear-gradient(90deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  animation: valueShimmer 3s ease-in-out infinite;
}

@keyframes valueShimmer {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 0.25rem;
}

.stat-glow {
  position: absolute;
  inset: -50%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.4), transparent);
  filter: blur(40px);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-item:hover .stat-glow {
  opacity: 0.5;
}

/* Create Plan Button */
.create-plan-btn {
  position: relative;
  padding: 1rem 2rem;
  background: transparent;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-background {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  opacity: 1;
  transition: all 0.3s ease;
}

.create-plan-btn:hover .btn-background {
  transform: scale(1.05);
}

.btn-content {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  z-index: 2;
}

.btn-glow {
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  filter: blur(15px);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.create-plan-btn:hover .btn-glow {
  opacity: 0.7;
}

.btn-particles {
  position: absolute;
  inset: 0;
  overflow: hidden;
}

.btn-particles span {
  position: absolute;
  width: 4px;
  height: 4px;
  background: white;
  border-radius: 50%;
  opacity: 0;
}

.create-plan-btn:hover .btn-particles span {
  animation: particleBurst 0.8s ease-out;
}

.btn-particles span:nth-child(1) { top: 50%; left: 30%; }
.btn-particles span:nth-child(2) { top: 50%; left: 50%; animation-delay: 0.1s; }
.btn-particles span:nth-child(3) { top: 50%; left: 70%; animation-delay: 0.2s; }

@keyframes particleBurst {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }
  100% {
    transform: translate(calc(-50% + var(--x, 30px)), calc(-50% + var(--y, -30px))) scale(1);
    opacity: 0;
  }
}

/* AI Assistant Bar */
.ai-assistant-bar {
  margin: 2rem;
  margin-bottom: 0;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}

.assistant-content {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.assistant-avatar {
  position: relative;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #30cfd0, #330867);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.avatar-glow {
  position: absolute;
  inset: -10px;
  background: radial-gradient(circle, rgba(48, 207, 208, 0.4), transparent);
  filter: blur(15px);
  animation: avatarGlow 3s ease-in-out infinite;
}

@keyframes avatarGlow {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 0.8; }
}

.status-dot {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  background: #10b981;
  border-radius: 50%;
  border: 2px solid #0a0b1e;
  animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

.assistant-message {
  flex: 1;
}

.assistant-message p {
  margin: 0 0 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.05rem;
  line-height: 1.6;
}

.message-actions {
  display: flex;
  gap: 1rem;
}

.action-btn {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.action-btn.apply {
  background: linear-gradient(135deg, #10b981, #059669);
  border: none;
}

.action-btn.apply:hover {
  box-shadow: 0 5px 20px rgba(16, 185, 129, 0.4);
}

.assistant-metrics {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.metric {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.metric-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-bar {
  width: 120px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2px;
  transition: width 0.6s ease;
}

/* Main Content */
.main-content {
  padding: 2rem;
  max-width: 1600px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

/* Stats Row */
.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.card-background {
  position: absolute;
  inset: 0;
  transition: all 0.3s ease;
}

.stat-card:hover .card-background {
  transform: scale(1.1);
}

.card-content {
  position: relative;
  padding: 1.5rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.card-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  position: relative;
}

.icon-effect {
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  opacity: 0;
  filter: blur(8px);
  transition: opacity 0.3s ease;
}

.stat-card:hover .icon-effect {
  opacity: 0.3;
}

.card-trend {
  padding: 0.25rem 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.card-trend.up {
  color: #10b981;
  background: rgba(16, 185, 129, 0.2);
}

.card-trend.down {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.2);
}

.card-body {
  position: relative;
}

.card-value {
  font-size: 2rem;
  font-weight: 800;
  color: white;
  margin: 0;
}

.card-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin: 0.25rem 0 1rem;
}

.card-progress {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 3px;
  transition: width 0.6s ease;
}

.card-glow {
  position: absolute;
  inset: -50%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.3), transparent);
  filter: blur(50px);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.stat-card:hover .card-glow {
  opacity: 0.5;
}

/* Modern Card Base */
.modern-card {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 1px rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 1px rgba(255, 255, 255, 0.1);
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: 1fr minmax(700px, 1fr);
  gap: 2rem;
  margin-top: 2rem;
}

.left-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Plans Card */
.plans-card {
  padding: 2rem;
}

.plans-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.section-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 1.2rem;
}

.section-icon.pulse {
  animation: iconPulse 2s ease-in-out infinite;
}

.card-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: white;
  font-weight: 700;
}

.badge {
  padding: 0.25rem 0.75rem;
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.view-controls {
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.25rem;
  border-radius: 12px;
}

.view-btn {
  width: 36px;
  height: 36px;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-btn:hover {
  color: white;
}

.view-btn.active {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
}

/* Plans Container */
.plans-container {
  display: grid;
  gap: 1.5rem;
}

.plans-container.grid {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.plans-container.list {
  grid-template-columns: 1fr;
}

.plan-item {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.plan-item:hover {
  transform: translateY(-4px);
  border-color: rgba(102, 126, 234, 0.5);
}

.plan-item.selected {
  border-color: #667eea;
}

.plan-background {
  position: absolute;
  inset: 0;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.plan-item:hover .plan-background {
  opacity: 0.7;
}

.plan-content {
  position: relative;
  padding: 1.5rem;
  background: rgba(13, 14, 31, 0.6);
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.plan-icon {
  width: 48px;
  height: 48px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.plan-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.plan-duration {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}

.plan-priority {
  padding: 0.25rem 0.5rem;
  border-radius: 8px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.plan-priority.alta {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.plan-priority.média {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.plan-priority.baixa {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.plan-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  margin: 0 0 0.5rem;
}

.plan-description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0 0 1rem;
}

.plan-stats {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.plan-stats .stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
}

.plan-stats .stat i {
  color: rgba(255, 255, 255, 0.4);
}

.plan-progress {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.plan-progress .progress-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.plan-progress .progress-fill {
  position: relative;
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: width 0.6s ease;
}

.progress-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: progressShimmer 2s ease-in-out infinite;
}

@keyframes progressShimmer {
  from { transform: translateX(-100%); }
  to { transform: translateX(100%); }
}

.progress-text {
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
}

.plan-hover-effect {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%), rgba(102, 126, 234, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.plan-item:hover .plan-hover-effect {
  opacity: 1;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
}

.empty-icon {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  margin-bottom: 2rem;
}

.empty-icon i {
  font-size: 3rem;
  color: rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
}

.icon-bg {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.1), transparent);
  border-radius: 50%;
  animation: emptyPulse 3s ease-in-out infinite;
}

@keyframes emptyPulse {
  0%, 100% { transform: scale(1); opacity: 0.5; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

.empty-state h3 {
  font-size: 1.5rem;
  color: white;
  margin: 0 0 0.5rem;
}

.empty-state p {
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 2rem;
}

.empty-cta {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.empty-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

/* Insights Card */
.insights-card {
  padding: 2rem;
}

.refresh-btn {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.refresh-btn.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.insight-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.insight-item:hover {
  transform: translateX(4px);
  border-color: rgba(255, 255, 255, 0.2);
}

.insight-icon {
  width: 48px;
  height: 48px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.insight-content {
  flex: 1;
}

.insight-content h4 {
  margin: 0 0 0.5rem;
  font-size: 1.1rem;
  color: white;
  font-weight: 600;
}

.insight-content p {
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  line-height: 1.5;
}

.insight-action {
  margin-top: 0.75rem;
}

.apply-btn {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: white;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.apply-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.insight-indicator {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #667eea, #764ba2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.insight-item:hover .insight-indicator {
  opacity: 1;
}

.insight-performance .insight-indicator {
  background: linear-gradient(180deg, #10b981, #059669);
}

.insight-warning .insight-indicator {
  background: linear-gradient(180deg, #f59e0b, #d97706);
}

.insight-suggestion .insight-indicator {
  background: linear-gradient(180deg, #3b82f6, #2563eb);
}

/* Recommendations Card */
.recommendations-card {
  padding: 2rem;
}

.recommendations-grid {
  display: grid;
  gap: 1rem;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.recommendation-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(4px);
  border-color: rgba(255, 255, 255, 0.2);
}

.rec-icon {
  width: 48px;
  height: 48px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.rec-content {
  flex: 1;
}

.rec-content h4 {
  margin: 0 0 0.25rem;
  font-size: 1rem;
  color: white;
  font-weight: 600;
}

.rec-content p {
  margin: 0;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
}

.rec-arrow {
  color: rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
}

.recommendation-item:hover .rec-arrow {
  color: white;
  transform: translateX(4px);
}

/* Calendar Section */
.calendar-section {
  position: sticky;
  top: 2rem;
  height: fit-content;
}

.calendar-wrapper {
  padding: 0;
  overflow: hidden;
}

/* Animations */
.plan-list-enter-active,
.plan-list-leave-active {
  transition: all 0.3s ease;
}

.plan-list-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.plan-list-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.plan-list-move {
  transition: transform 0.3s ease;
}

/* Modal Animations */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: rgba(13, 14, 31, 0.95);
  border-radius: 24px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.close-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 10px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(90deg);
}

/* Responsive */
@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .calendar-section {
    display: none;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 1rem;
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .page-title {
    font-size: 2rem;
  }
  
  .header-stats {
    flex-wrap: wrap;
  }
  
  .stats-row {
    grid-template-columns: 1fr;
  }
  
  .ai-assistant-bar {
    margin: 1rem;
  }
  
  .assistant-content {
    flex-wrap: wrap;
  }
  
  .plans-container.grid {
    grid-template-columns: 1fr;
  }
}
</style>