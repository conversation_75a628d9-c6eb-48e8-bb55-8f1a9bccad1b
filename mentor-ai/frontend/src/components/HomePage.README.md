# HomePage Component - Documentação

## Visão Geral

A HomePage foi completamente reestruturada para fornecer uma experiência unificada e moderna, seguindo os princípios de componentização e reutilização.

## Estrutura de Arquivos

```
components/
├── HomePage.vue           # Componente principal da homepage
├── common/               # Componentes reutilizáveis
│   ├── SectionHeader.vue
│   ├── MetricCard.vue
│   └── QuickActionCard.vue
└── home/                 # Componentes específicos da homepage
    ├── ProgressStats.vue
    ├── WeeklyChart.vue
    ├── PerformanceDetails.vue
    ├── AchievementsGrid.vue
    ├── ActivityTimeline.vue
    ├── AIAssistantWidget.vue
    ├── ResourcesGrid.vue
    └── GettingStartedSteps.vue
```

## Componentes

### HomePage.vue
- **Descrição**: Componente principal que orquestra toda a página inicial
- **Seções**:
  - Hero Section com boas-vindas personalizadas
  - Quick Actions para acesso rápido às ferramentas
  - Progress Overview com estatísticas e gráficos
  - Achievements para gamificação
  - Recent Activities timeline
  - AI Assistant widget
  - Study Resources recomendados
  - Getting Started guide para novos usuários

### Componentes Comuns (common/)

#### SectionHeader.vue
- Header reutilizável para todas as seções
- Props: `icon`, `title`, `subtitle`
- Slot opcional para ações adicionais

#### MetricCard.vue
- Card para exibir métricas numéricas
- Props: `icon`, `value`, `suffix`, `label`, `color`, `clickable`
- Suporte a diferentes esquemas de cores

#### QuickActionCard.vue
- Card de ação rápida com navegação
- Props: `link`, `icon`, `title`, `description`, `color`
- Animações hover e suporte a temas de cores

### Componentes Específicos (home/)

#### ProgressStats.vue
- Grid de estatísticas de progresso
- Exibe métricas com tendências positivas/negativas

#### WeeklyChart.vue
- Gráfico de progresso semanal (placeholder)
- Preparado para integração com biblioteca de gráficos

#### PerformanceDetails.vue
- Detalhes de performance em formato compacto
- Métricas de retenção, foco e produtividade

#### AchievementsGrid.vue
- Sistema de conquistas com barras de progresso
- Gamificação do processo de estudo

#### ActivityTimeline.vue
- Timeline vertical de atividades recentes
- Links para navegação direta

#### AIAssistantWidget.vue
- Widget interativo do assistente de IA
- Input de mensagens e sugestões rápidas
- Links para ferramentas de IA

#### ResourcesGrid.vue
- Grid de recursos recomendados
- Tags, duração e ações para cada recurso

#### GettingStartedSteps.vue
- Guia passo-a-passo para novos usuários
- Progresso visual e opção de pular

## Uso

```vue
import HomePage from '@/components/HomePage.vue';

// No router
{
  path: '/',
  name: 'HomePage',
  component: HomePage,
  meta: { requiresAuth: true }
}
```

## Variáveis CSS Esperadas

A HomePage espera as seguintes variáveis CSS definidas globalmente:

```css
:root {
  --color-bg-secondary: #1a1a1a;
  --color-border: #333;
  --color-text: #fff;
  --color-text-secondary: #999;
  --color-primary: #42b983;
  --color-primary-dark: #35a372;
}
```

## Responsividade

Todos os componentes são responsivos com breakpoints em:
- Desktop: > 1024px
- Tablet: 768px - 1024px
- Mobile: < 768px

## Próximos Passos

1. Integrar biblioteca de gráficos para WeeklyChart
2. Conectar com APIs reais para dados dinâmicos
3. Implementar persistência de estado para Getting Started
4. Adicionar animações de transição entre seções
5. Implementar lazy loading para componentes pesados 