<template>
  <div class="notes-hub">
    <!-- Animated Background -->
    <div class="animated-bg">
      <div class="gradient-orb gradient-1"></div>
      <div class="gradient-orb gradient-2"></div>
      <div class="gradient-orb gradient-3"></div>
      <div class="gradient-orb gradient-4"></div>
      
      <!-- Neural Network -->
      <svg class="neural-network" viewBox="0 0 1920 1080">
        <defs>
          <linearGradient id="synapseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#00ffff;stop-opacity:0.6" />
            <stop offset="50%" style="stop-color:#ff00ff;stop-opacity:0.3" />
            <stop offset="100%" style="stop-color:#ffff00;stop-opacity:0.6" />
          </linearGradient>
        </defs>
        <g class="synapses">
          <line v-for="i in 30" :key="`syn-${i}`"
                :x1="Math.random() * 1920"
                :y1="Math.random() * 1080"
                :x2="Math.random() * 1920"
                :y2="Math.random() * 1080"
                stroke="url(#synapseGradient)"
                stroke-width="1"
                :opacity="0.3 + Math.random() * 0.3" />
        </g>
        <g class="neurons">
          <circle v-for="i in 20" :key="`neuron-${i}`"
                  :cx="Math.random() * 1920"
                  :cy="Math.random() * 1080"
                  :r="3 + Math.random() * 5"
                  fill="#00ffff"
                  :opacity="0.6 + Math.random() * 0.4">
            <animate attributeName="r"
                     :values="`${3 + Math.random() * 5};${5 + Math.random() * 8};${3 + Math.random() * 5}`"
                     dur="3s"
                     repeatCount="indefinite" />
          </circle>
        </g>
      </svg>
      
      <!-- Particles -->
      <div class="particles">
        <div v-for="i in 80" :key="`particle-${i}`" class="particle"></div>
      </div>
    </div>

    <!-- Header -->
    <div class="hub-header">
      <router-link to="/" class="back-button">
        <i class="fas fa-arrow-left"></i>
        <span>Voltar</span>
      </router-link>
      
      <div class="header-content">
        <h1 class="main-title">
          <span class="title-gradient">Central de Notas & Resumos</span>
          <div class="title-subtitle">Sistema Inteligente de Gestão de Conhecimento</div>
        </h1>
        
        <div class="stats-bar">
          <div class="stat-item">
            <i class="fas fa-file-lines"></i>
            <span class="stat-value">{{ totalNotes }}</span>
            <span class="stat-label">Notas</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-brain"></i>
            <span class="stat-value">{{ totalResumes }}</span>
            <span class="stat-label">Resumos</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-project-diagram"></i>
            <span class="stat-value">{{ totalMindMaps }}</span>
            <span class="stat-label">Mapas</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-users"></i>
            <span class="stat-value">{{ sharedNotes }}</span>
            <span class="stat-label">Compartilhados</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Tools Grid -->
    <div class="tools-grid">
      <div v-for="tool in tools" 
           :key="tool.id"
           class="tool-card"
           :class="{ active: hoveredTool === tool.id }"
           @mouseenter="hoveredTool = tool.id"
           @mouseleave="hoveredTool = null"
           @click="navigateToTool(tool)">
        
        <!-- Tool Background -->
        <div class="card-bg" :style="{ background: tool.gradient }"></div>
        
        <!-- Tool Content -->
        <div class="card-content">
          <div class="tool-icon" :style="{ color: tool.color }">
            <i :class="`fas fa-${tool.icon}`"></i>
            <div class="icon-glow" :style="{ background: tool.color }"></div>
          </div>
          
          <h3 class="tool-title">{{ tool.title }}</h3>
          <p class="tool-description">{{ tool.description }}</p>
          
          <div class="tool-features">
            <div v-for="feature in tool.features" :key="feature" class="feature-tag">
              <i class="fas fa-check-circle"></i>
              <span>{{ feature }}</span>
            </div>
          </div>
          
          <div class="tool-metrics">
            <div v-for="metric in tool.metrics" :key="metric.label" class="metric">
              <div class="metric-icon">
                <i :class="`fas fa-${metric.icon}`"></i>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ metric.value }}</div>
                <div class="metric-label">{{ metric.label }}</div>
              </div>
            </div>
          </div>
          
          <button class="access-button" :style="{ background: tool.color }">
            <span>Acessar</span>
            <i class="fas fa-arrow-right"></i>
          </button>
        </div>
        
        <!-- Hover Effects -->
        <div class="card-particles">
          <span v-for="j in 12" :key="`p-${j}`" class="mini-particle"></span>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <h2 class="section-title">Ações Rápidas</h2>
      <div class="actions-grid">
        <button v-for="action in quickActions" 
                :key="action.id"
                class="action-button"
                @click="executeAction(action)">
          <i :class="`fas fa-${action.icon}`"></i>
          <span>{{ action.label }}</span>
        </button>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="recent-activity">
      <h2 class="section-title">Atividade Recente</h2>
      <div class="activity-timeline">
        <div v-for="activity in recentActivities" 
             :key="activity.id"
             class="activity-item">
          <div class="activity-icon" :style="{ background: activity.color }">
            <i :class="`fas fa-${activity.icon}`"></i>
          </div>
          <div class="activity-content">
            <h4>{{ activity.title }}</h4>
            <p>{{ activity.description }}</p>
            <span class="activity-time">{{ activity.time }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'NotesHub',
  
  setup() {
    const router = useRouter()
    const store = useStore()
    
    const hoveredTool = ref(null)
    
    // Stats
    const totalNotes = ref(42)
    const totalResumes = ref(18)
    const totalMindMaps = ref(7)
    const sharedNotes = ref(12)
    
    // Tools configuration
    const tools = [
      {
        id: 'editor',
        title: 'Editor Avançado',
        description: 'Editor de notas com IA integrada, formatação rica e organização inteligente',
        icon: 'edit',
        color: '#00ffff',
        gradient: 'linear-gradient(135deg, #00ffff22, #00ffff44)',
        route: '/resumos-notas/editor',
        features: ['Markdown', 'Rich Text', 'IA Assistant'],
        metrics: [
          { icon: 'rocket', value: '10x', label: 'Mais Rápido' },
          { icon: 'brain', value: '98%', label: 'Precisão IA' }
        ]
      },
      {
        id: 'ai-resumes',
        title: 'Gerador de Resumos IA',
        description: 'Crie resumos inteligentes e personalizados com deep learning',
        icon: 'robot',
        color: '#ff00ff',
        gradient: 'linear-gradient(135deg, #ff00ff22, #ff00ff44)',
        route: '/resumos-notas/ai-resumos',
        features: ['Auto-resumo', 'Multi-idiomas', 'Personalização'],
        metrics: [
          { icon: 'clock', value: '5min', label: 'Por Resumo' },
          { icon: 'star', value: '4.9', label: 'Avaliação' }
        ]
      },
      {
        id: 'mindmaps',
        title: 'Mapas Mentais 3D',
        description: 'Visualize e organize ideias com mapas mentais interativos e dinâmicos',
        icon: 'project-diagram',
        color: '#ffff00',
        gradient: 'linear-gradient(135deg, #ffff0022, #ffff0044)',
        route: '/resumos-notas/mapas-mentais',
        features: ['3D View', 'Colaborativo', 'Export HD'],
        metrics: [
          { icon: 'layer-group', value: '∞', label: 'Camadas' },
          { icon: 'palette', value: '16M', label: 'Cores' }
        ]
      },
      {
        id: 'templates',
        title: 'Biblioteca de Templates',
        description: 'Templates profissionais para todos os tipos de notas e resumos',
        icon: 'th-large',
        color: '#00ff88',
        gradient: 'linear-gradient(135deg, #00ff8822, #00ff8844)',
        route: '/resumos-notas/templates',
        features: ['1000+ Templates', 'Customizáveis', 'IA Sugestões'],
        metrics: [
          { icon: 'download', value: '50K+', label: 'Downloads' },
          { icon: 'heart', value: '98%', label: 'Satisfação' }
        ]
      },
      {
        id: 'insights',
        title: 'Análise & Insights',
        description: 'Dashboard analítico com insights profundos sobre seu aprendizado',
        icon: 'chart-line',
        color: '#ff8800',
        gradient: 'linear-gradient(135deg, #ff880022, #ff880044)',
        route: '/resumos-notas/insights',
        features: ['Analytics', 'Previsões', 'Relatórios'],
        metrics: [
          { icon: 'chart-pie', value: '20+', label: 'Gráficos' },
          { icon: 'bolt', value: 'Real', label: 'Time' }
        ]
      },
      {
        id: 'collaboration',
        title: 'Colaboração em Tempo Real',
        description: 'Trabalhe em equipe com edição simultânea e comunicação integrada',
        icon: 'users',
        color: '#8800ff',
        gradient: 'linear-gradient(135deg, #8800ff22, #8800ff44)',
        route: '/resumos-notas/colaboracao',
        features: ['Real-time', 'Video Chat', 'Versionamento'],
        metrics: [
          { icon: 'users', value: '100+', label: 'Simultâneos' },
          { icon: 'shield-alt', value: 'E2E', label: 'Criptografia' }
        ]
      },
      {
        id: 'organizer',
        title: 'Organizador Inteligente',
        description: 'Sistema avançado de organização com IA para categorizar e gerenciar suas notas',
        icon: 'layer-group',
        color: '#ff0088',
        gradient: 'linear-gradient(135deg, #ff008822, #ff008844)',
        route: '/resumos-notas/organizador',
        features: ['Categorização IA', 'Tags Inteligentes', 'Busca Semântica'],
        metrics: [
          { icon: 'folder-tree', value: '∞', label: 'Categorias' },
          { icon: 'search', value: '0.1s', label: 'Busca' }
        ]
      },
      {
        id: 'export',
        title: 'Central de Exportação',
        description: 'Exporte suas notas em múltiplos formatos com templates profissionais',
        icon: 'file-export',
        color: '#10b981',
        gradient: 'linear-gradient(135deg, #10b98122, #10b98144)',
        route: '/resumos-notas/exportar',
        features: ['Multi-formato', 'Templates', 'Exportação em Lote'],
        metrics: [
          { icon: 'file-alt', value: '6+', label: 'Formatos' },
          { icon: 'download', value: 'HD', label: 'Qualidade' }
        ]
      },
      {
        id: 'history',
        title: 'Histórico de Revisões',
        description: 'Controle de versões completo com timeline visual e comparação de mudanças',
        icon: 'history',
        color: '#f59e0b',
        gradient: 'linear-gradient(135deg, #f59e0b22, #f59e0b44)',
        route: '/resumos-notas/historico',
        features: ['Versionamento', 'Timeline Visual', 'Comparação'],
        metrics: [
          { icon: 'code-branch', value: '∞', label: 'Versões' },
          { icon: 'undo', value: '1-Click', label: 'Restaurar' }
        ]
      },
      {
        id: 'ai-enhanced',
        title: 'IA Avançada',
        description: 'Ferramentas de inteligência artificial de última geração para potencializar suas notas',
        icon: 'brain',
        color: '#9333ea',
        gradient: 'linear-gradient(135deg, #9333ea22, #9333ea44)',
        route: '/resumos-notas/ia-avancada',
        features: ['Resumo Inteligente', 'Gerador de Questões', 'Chat IA'],
        metrics: [
          { icon: 'microchip', value: 'GPT-4', label: 'Engine' },
          { icon: 'zap', value: '10x', label: 'Mais Rápido' }
        ]
      }
    ]
    
    // Quick Actions
    const quickActions = [
      { id: 'new-note', icon: 'plus', label: 'Nova Nota' },
      { id: 'quick-resume', icon: 'lightning-bolt', label: 'Resumo Rápido' },
      { id: 'import', icon: 'upload', label: 'Importar' },
      { id: 'export', icon: 'download', label: 'Exportar' },
      { id: 'search', icon: 'search', label: 'Buscar' },
      { id: 'sync', icon: 'sync', label: 'Sincronizar' }
    ]
    
    // Recent Activities
    const recentActivities = ref([
      {
        id: 1,
        title: 'Novo Resumo Criado',
        description: 'Resumo de Anatomia Cardiovascular gerado com IA',
        icon: 'file-medical',
        color: '#00ffff',
        time: 'Há 5 minutos'
      },
      {
        id: 2,
        title: 'Mapa Mental Atualizado',
        description: 'Sistema Nervoso Central - 15 novos nós adicionados',
        icon: 'project-diagram',
        color: '#ff00ff',
        time: 'Há 1 hora'
      },
      {
        id: 3,
        title: 'Colaboração Iniciada',
        description: '3 colegas entraram no documento compartilhado',
        icon: 'users',
        color: '#ffff00',
        time: 'Há 2 horas'
      }
    ])
    
    // Methods
    const navigateToTool = (tool) => {
      router.push(tool.route)
    }
    
    const executeAction = (action) => {
      switch(action.id) {
        case 'new-note':
          router.push('/resumos-notas/editor?new=true')
          break
        case 'quick-resume':
          router.push('/resumos-notas/ai-resumos?quick=true')
          break
        // Add more action handlers
      }
    }
    
    onMounted(() => {
      // Load stats from store or API
      // Animate particles
    })
    
    return {
      hoveredTool,
      totalNotes,
      totalResumes,
      totalMindMaps,
      sharedNotes,
      tools,
      quickActions,
      recentActivities,
      navigateToTool,
      executeAction
    }
  }
}
</script>

<style scoped>
.notes-hub {
  min-height: 100vh;
  background: var(--theme-background, #0a0a14);
  color: var(--theme-text, #ffffff);
  position: relative;
  overflow: hidden;
}

/* Animated Background */
.animated-bg {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(80px);
  animation: float-orb 20s infinite ease-in-out;
}

.gradient-1 {
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, #00ffff44, transparent);
  top: -200px;
  left: -200px;
  animation-delay: 0s;
}

.gradient-2 {
  width: 800px;
  height: 800px;
  background: radial-gradient(circle, #ff00ff33, transparent);
  bottom: -300px;
  right: -300px;
  animation-delay: 5s;
}

.gradient-3 {
  width: 500px;
  height: 500px;
  background: radial-gradient(circle, #ffff0033, transparent);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 10s;
}

.gradient-4 {
  width: 700px;
  height: 700px;
  background: radial-gradient(circle, #00ff8833, transparent);
  top: 20%;
  right: 10%;
  animation-delay: 15s;
}

@keyframes float-orb {
  0%, 100% {
    transform: translate(0, 0) scale(1);
  }
  25% {
    transform: translate(100px, -50px) scale(1.1);
  }
  50% {
    transform: translate(-50px, 100px) scale(0.9);
  }
  75% {
    transform: translate(-100px, -100px) scale(1.05);
  }
}

/* Neural Network */
.neural-network {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0.1;
}

/* Particles */
.particles {
  position: absolute;
  inset: 0;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: var(--theme-primary);
  border-radius: 50%;
  animation: float-particle 15s infinite linear;
}

@keyframes float-particle {
  from {
    transform: translateY(100vh) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  to {
    transform: translateY(-100px) translateX(100px);
    opacity: 0;
  }
}

.particle:nth-child(odd) {
  animation-duration: 20s;
  background: var(--theme-secondary);
}

.particle:nth-child(even) {
  animation-duration: 25s;
  animation-delay: 1s;
}

/* Header */
.hub-header {
  position: relative;
  z-index: 10;
  padding: 2rem;
  background: var(--theme-glass);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--theme-border);
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--theme-text);
  text-decoration: none;
  padding: 0.5rem 1rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  transition: all 0.3s ease;
  margin-bottom: 1rem;
}

.back-button:hover {
  background: var(--theme-glassHover);
  transform: translateX(-5px);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
}

.main-title {
  text-align: center;
  margin-bottom: 2rem;
}

.title-gradient {
  font-size: 3rem;
  font-weight: 900;
  background: linear-gradient(135deg, var(--theme-primary), var(--theme-secondary), var(--theme-accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.title-subtitle {
  font-size: 1.125rem;
  color: var(--theme-textMuted);
  margin-top: 0.5rem;
}

.stats-bar {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px var(--theme-shadow);
}

.stat-item svg {
  font-size: 1.5rem;
  color: var(--theme-primary);
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--theme-text);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--theme-textMuted);
}

/* Tools Grid */
.tools-grid {
  position: relative;
  z-index: 10;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 2rem;
  padding: 3rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.tool-card {
  position: relative;
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 20px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tool-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 20px 40px var(--theme-shadow);
}

.card-bg {
  position: absolute;
  inset: 0;
  opacity: 0.1;
  transition: opacity 0.3s ease;
}

.tool-card:hover .card-bg {
  opacity: 0.2;
}

.card-content {
  position: relative;
  padding: 2rem;
  z-index: 1;
}

.tool-icon {
  position: relative;
  width: 80px;
  height: 80px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
}

.icon-glow {
  position: absolute;
  inset: -20px;
  border-radius: 50%;
  filter: blur(20px);
  opacity: 0.3;
  animation: pulse-glow 3s infinite ease-in-out;
}

@keyframes pulse-glow {
  0%, 100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.5;
  }
}

.tool-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--theme-text);
  margin-bottom: 0.75rem;
}

.tool-description {
  color: var(--theme-textMuted);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.tool-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.feature-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 20px;
  font-size: 0.75rem;
  color: var(--theme-text);
}

.feature-tag svg {
  color: var(--theme-success);
  font-size: 0.625rem;
}

.tool-metrics {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.metric {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.metric-icon {
  width: 32px;
  height: 32px;
  background: var(--theme-glass);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--theme-primary);
}

.metric-value {
  font-size: 1.125rem;
  font-weight: bold;
  color: var(--theme-text);
}

.metric-label {
  font-size: 0.75rem;
  color: var(--theme-textMuted);
}

.access-button {
  width: 100%;
  padding: 1rem;
  border: none;
  border-radius: 12px;
  color: var(--theme-background);
  font-weight: bold;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.access-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Card Particles */
.card-particles {
  position: absolute;
  inset: 0;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tool-card:hover .card-particles {
  opacity: 1;
}

.mini-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: currentColor;
  border-radius: 50%;
  animation: burst 1s ease-out infinite;
}

@keyframes burst {
  0% {
    transform: translate(0, 0) scale(0);
    opacity: 1;
  }
  100% {
    transform: translate(var(--tx, 0), var(--ty, 0)) scale(1);
    opacity: 0;
  }
}

.mini-particle:nth-child(1) { --tx: -50px; --ty: -50px; animation-delay: 0s; left: 10%; top: 10%; }
.mini-particle:nth-child(2) { --tx: 50px; --ty: -50px; animation-delay: 0.1s; right: 10%; top: 10%; }
.mini-particle:nth-child(3) { --tx: -50px; --ty: 50px; animation-delay: 0.2s; left: 10%; bottom: 10%; }
.mini-particle:nth-child(4) { --tx: 50px; --ty: 50px; animation-delay: 0.3s; right: 10%; bottom: 10%; }
.mini-particle:nth-child(5) { --tx: 0; --ty: -70px; animation-delay: 0.4s; left: 50%; top: 20%; }
.mini-particle:nth-child(6) { --tx: 0; --ty: 70px; animation-delay: 0.5s; left: 50%; bottom: 20%; }
.mini-particle:nth-child(7) { --tx: -70px; --ty: 0; animation-delay: 0.6s; left: 20%; top: 50%; }
.mini-particle:nth-child(8) { --tx: 70px; --ty: 0; animation-delay: 0.7s; right: 20%; top: 50%; }
.mini-particle:nth-child(9) { --tx: -35px; --ty: -35px; animation-delay: 0.8s; left: 30%; top: 30%; }
.mini-particle:nth-child(10) { --tx: 35px; --ty: -35px; animation-delay: 0.9s; right: 30%; top: 30%; }
.mini-particle:nth-child(11) { --tx: -35px; --ty: 35px; animation-delay: 1s; left: 30%; bottom: 30%; }
.mini-particle:nth-child(12) { --tx: 35px; --ty: 35px; animation-delay: 1.1s; right: 30%; bottom: 30%; }

/* Quick Actions */
.quick-actions {
  position: relative;
  z-index: 10;
  padding: 3rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.section-title {
  font-size: 2rem;
  font-weight: bold;
  color: var(--theme-text);
  margin-bottom: 2rem;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.action-button {
  padding: 1.5rem 1rem;
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
  color: var(--theme-text);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.action-button:hover {
  background: var(--theme-glassHover);
  transform: translateY(-5px);
  box-shadow: 0 10px 30px var(--theme-shadow);
}

.action-button svg {
  font-size: 1.5rem;
  color: var(--theme-primary);
}

/* Recent Activity */
.recent-activity {
  position: relative;
  z-index: 10;
  padding: 3rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.activity-timeline {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.activity-item {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.activity-item:hover {
  transform: translateX(10px);
  box-shadow: 0 5px 20px var(--theme-shadow);
}

.activity-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--theme-background);
  flex-shrink: 0;
}

.activity-content h4 {
  color: var(--theme-text);
  margin-bottom: 0.25rem;
}

.activity-content p {
  color: var(--theme-textMuted);
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.activity-time {
  font-size: 0.75rem;
  color: var(--theme-primary);
}

/* Responsive */
@media (max-width: 768px) {
  .tools-grid {
    grid-template-columns: 1fr;
    padding: 2rem 1rem;
  }
  
  .stats-bar {
    gap: 1rem;
  }
  
  .stat-item {
    padding: 0.75rem 1rem;
  }
  
  .title-gradient {
    font-size: 2rem;
  }
  
  .actions-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Theme-specific adjustments */
[data-theme="light"] .neural-network {
  opacity: 0.05;
}

[data-theme="light"] .gradient-orb {
  filter: blur(100px);
}

/* Particles setup */
.particle:nth-child(1) { left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { left: 20%; animation-delay: 0.5s; }
.particle:nth-child(3) { left: 30%; animation-delay: 1s; }
.particle:nth-child(4) { left: 40%; animation-delay: 1.5s; }
.particle:nth-child(5) { left: 50%; animation-delay: 2s; }
.particle:nth-child(6) { left: 60%; animation-delay: 2.5s; }
.particle:nth-child(7) { left: 70%; animation-delay: 3s; }
.particle:nth-child(8) { left: 80%; animation-delay: 3.5s; }
.particle:nth-child(9) { left: 90%; animation-delay: 4s; }
.particle:nth-child(10) { left: 15%; animation-delay: 4.5s; }
.particle:nth-child(11) { left: 25%; animation-delay: 5s; }
.particle:nth-child(12) { left: 35%; animation-delay: 5.5s; }
.particle:nth-child(13) { left: 45%; animation-delay: 6s; }
.particle:nth-child(14) { left: 55%; animation-delay: 6.5s; }
.particle:nth-child(15) { left: 65%; animation-delay: 7s; }
.particle:nth-child(16) { left: 75%; animation-delay: 7.5s; }
.particle:nth-child(17) { left: 85%; animation-delay: 8s; }
.particle:nth-child(18) { left: 95%; animation-delay: 8.5s; }
.particle:nth-child(19) { left: 5%; animation-delay: 9s; }
.particle:nth-child(20) { left: 12%; animation-delay: 9.5s; }
</style>