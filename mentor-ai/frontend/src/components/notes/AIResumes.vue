<template>
  <div class="notes-ai-generator">
    <!-- Animated Background -->
    <div class="ai-background">
      <div class="brain-network"></div>
      <div class="data-flow"></div>
      <div class="gradient-waves"></div>
    </div>

    <!-- Header -->
    <div class="generator-header">
      <div class="header-top">
        <router-link to="/resumos-notas" class="back-btn">
          <i class="fas fa-arrow-left"></i>
          <span>Voltar</span>
        </router-link>

        <div class="header-title">
          <h1>
            <i class="fas fa-robot"></i>
            Gerador de Resumos IA
          </h1>
          <p>Transforme conteúdo complexo em resumos inteligentes e personalizados</p>
        </div>

        <div class="header-actions">
          <button @click="showHistory = true" class="history-btn">
            <i class="fas fa-history"></i>
            <span>Histórico</span>
          </button>
          <button @click="showSettings = true" class="settings-btn">
            <i class="fas fa-cog"></i>
            <span>Configurações</span>
          </button>
        </div>
      </div>

      <!-- Mode Selector -->
      <div class="mode-selector">
        <button v-for="mode in generationModes" 
                :key="mode.id"
                @click="selectedMode = mode.id"
                :class="{ active: selectedMode === mode.id }"
                class="mode-btn">
          <div class="mode-icon">
            <i :class="`fas fa-${mode.icon}`"></i>
          </div>
          <div class="mode-info">
            <h3>{{ mode.name }}</h3>
            <p>{{ mode.description }}</p>
          </div>
          <div class="mode-badge" v-if="mode.badge">
            <span>{{ mode.badge }}</span>
          </div>
        </button>
      </div>
    </div>

    <!-- Main Content -->
    <div class="generator-content">
      <!-- Input Section -->
      <div class="input-section">
        <div class="section-header">
          <h2>
            <i class="fas fa-file-import"></i>
            Conteúdo de Entrada
          </h2>
          <div class="input-actions">
            <button @click="importFile" class="action-btn">
              <i class="fas fa-upload"></i>
              Importar
            </button>
            <button @click="pasteFromClipboard" class="action-btn">
              <i class="fas fa-paste"></i>
              Colar
            </button>
            <button @click="startRecording" class="action-btn" :class="{ recording: isRecording }">
              <i :class="`fas fa-${isRecording ? 'stop' : 'microphone'}`"></i>
              {{ isRecording ? 'Parar' : 'Gravar' }}
            </button>
          </div>
        </div>

        <div class="input-container">
          <textarea v-model="inputContent" 
                    placeholder="Cole ou digite o conteúdo que deseja resumir..."
                    class="content-input"
                    @input="analyzeContent"></textarea>
          
          <div class="input-stats">
            <div class="stat">
              <i class="fas fa-file-word"></i>
              <span>{{ wordCount }} palavras</span>
            </div>
            <div class="stat">
              <i class="fas fa-paragraph"></i>
              <span>{{ paragraphCount }} parágrafos</span>
            </div>
            <div class="stat">
              <i class="fas fa-clock"></i>
              <span>~{{ estimatedReadTime }} min leitura</span>
            </div>
          </div>
        </div>

        <!-- Content Analysis -->
        <div class="content-analysis" v-if="contentAnalysis">
          <h3>Análise do Conteúdo</h3>
          <div class="analysis-grid">
            <div class="analysis-item">
              <i class="fas fa-brain"></i>
              <span>Complexidade: {{ contentAnalysis.complexity }}</span>
            </div>
            <div class="analysis-item">
              <i class="fas fa-graduation-cap"></i>
              <span>Nível: {{ contentAnalysis.level }}</span>
            </div>
            <div class="analysis-item">
              <i class="fas fa-tags"></i>
              <span>Tópicos: {{ contentAnalysis.topics.join(', ') }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Configuration Section -->
      <div class="config-section">
        <div class="section-header">
          <h2>
            <i class="fas fa-sliders-h"></i>
            Configurações de Geração
          </h2>
        </div>

        <div class="config-grid">
          <!-- Length Configuration -->
          <div class="config-card">
            <h3>
              <i class="fas fa-ruler"></i>
              Comprimento
            </h3>
            <div class="length-selector">
              <button v-for="length in lengthOptions" 
                      :key="length.id"
                      @click="selectedLength = length.id"
                      :class="{ active: selectedLength === length.id }"
                      class="length-btn">
                <span class="length-icon">{{ length.icon }}</span>
                <span class="length-name">{{ length.name }}</span>
                <span class="length-desc">{{ length.description }}</span>
              </button>
            </div>
            <div class="custom-length" v-if="selectedLength === 'custom'">
              <label>Número de palavras:</label>
              <input type="number" v-model="customWordCount" min="50" max="5000" />
            </div>
          </div>

          <!-- Style Configuration -->
          <div class="config-card">
            <h3>
              <i class="fas fa-palette"></i>
              Estilo
            </h3>
            <div class="style-options">
              <label v-for="style in styleOptions" :key="style.id" class="style-option">
                <input type="radio" v-model="selectedStyle" :value="style.id" />
                <div class="style-content">
                  <i :class="`fas fa-${style.icon}`"></i>
                  <span>{{ style.name }}</span>
                </div>
              </label>
            </div>
          </div>

          <!-- Focus Configuration -->
          <div class="config-card">
            <h3>
              <i class="fas fa-bullseye"></i>
              Foco
            </h3>
            <div class="focus-tags">
              <button v-for="focus in focusOptions" 
                      :key="focus.id"
                      @click="toggleFocus(focus.id)"
                      :class="{ active: selectedFocus.includes(focus.id) }"
                      class="focus-tag">
                <i :class="`fas fa-${focus.icon}`"></i>
                <span>{{ focus.name }}</span>
              </button>
            </div>
          </div>

          <!-- Advanced Options -->
          <div class="config-card">
            <h3>
              <i class="fas fa-cogs"></i>
              Opções Avançadas
            </h3>
            <div class="advanced-options">
              <label class="option-toggle">
                <input type="checkbox" v-model="includeKeyPoints" />
                <span>Incluir pontos-chave</span>
              </label>
              <label class="option-toggle">
                <input type="checkbox" v-model="generateQuestions" />
                <span>Gerar questões de estudo</span>
              </label>
              <label class="option-toggle">
                <input type="checkbox" v-model="includeCitations" />
                <span>Manter citações</span>
              </label>
              <label class="option-toggle">
                <input type="checkbox" v-model="multiLanguage" />
                <span>Resumo multilíngue</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- Generate Button -->
      <div class="generate-section">
        <button @click="generateSummary" 
                :disabled="!canGenerate || isGenerating"
                class="generate-btn">
          <div class="btn-content">
            <i :class="`fas fa-${isGenerating ? 'spinner fa-spin' : 'magic'}`"></i>
            <span>{{ isGenerating ? 'Gerando...' : 'Gerar Resumo' }}</span>
          </div>
          <div class="btn-glow"></div>
        </button>

        <div class="generation-tips" v-if="!isGenerating">
          <p><i class="fas fa-lightbulb"></i> Dica: {{ currentTip }}</p>
        </div>
      </div>

      <!-- Output Section -->
      <transition name="slide-up">
        <div class="output-section" v-if="generatedSummary">
          <div class="section-header">
            <h2>
              <i class="fas fa-file-alt"></i>
              Resumo Gerado
            </h2>
            <div class="output-actions">
              <button @click="copySummary" class="action-btn">
                <i class="fas fa-copy"></i>
                Copiar
              </button>
              <button @click="editSummary" class="action-btn">
                <i class="fas fa-edit"></i>
                Editar
              </button>
              <button @click="exportSummary" class="action-btn">
                <i class="fas fa-download"></i>
                Exportar
              </button>
              <button @click="shareSummary" class="action-btn">
                <i class="fas fa-share"></i>
                Compartilhar
              </button>
            </div>
          </div>

          <div class="summary-container">
            <!-- Summary Header -->
            <div class="summary-header">
              <h3>{{ generatedSummary.title }}</h3>
              <div class="summary-meta">
                <span><i class="fas fa-compress"></i> {{ generatedSummary.compressionRate }}% compressão</span>
                <span><i class="fas fa-star"></i> Score: {{ generatedSummary.qualityScore }}/10</span>
                <span><i class="fas fa-clock"></i> {{ generatedSummary.readTime }} min</span>
              </div>
            </div>

            <!-- Main Summary -->
            <div class="summary-content" v-html="generatedSummary.content"></div>

            <!-- Key Points -->
            <div class="key-points" v-if="generatedSummary.keyPoints">
              <h4><i class="fas fa-key"></i> Pontos-Chave</h4>
              <ul>
                <li v-for="(point, index) in generatedSummary.keyPoints" :key="index">
                  {{ point }}
                </li>
              </ul>
            </div>

            <!-- Study Questions -->
            <div class="study-questions" v-if="generatedSummary.questions">
              <h4><i class="fas fa-question-circle"></i> Questões de Estudo</h4>
              <div class="questions-list">
                <div v-for="(question, index) in generatedSummary.questions" 
                     :key="index"
                     class="question-item">
                  <span class="question-number">{{ index + 1 }}</span>
                  <p>{{ question.text }}</p>
                  <button @click="toggleAnswer(index)" class="show-answer-btn">
                    {{ question.showAnswer ? 'Ocultar' : 'Ver' }} Resposta
                  </button>
                  <transition name="fade">
                    <p v-if="question.showAnswer" class="answer">
                      {{ question.answer }}
                    </p>
                  </transition>
                </div>
              </div>
            </div>

            <!-- Related Topics -->
            <div class="related-topics" v-if="generatedSummary.relatedTopics">
              <h4><i class="fas fa-link"></i> Tópicos Relacionados</h4>
              <div class="topics-cloud">
                <span v-for="topic in generatedSummary.relatedTopics" 
                      :key="topic"
                      @click="searchTopic(topic)"
                      class="topic-tag">
                  {{ topic }}
                </span>
              </div>
            </div>
          </div>

          <!-- Summary Actions -->
          <div class="summary-actions">
            <button @click="regenerateSummary" class="action-btn secondary">
              <i class="fas fa-redo"></i>
              Regenerar com Ajustes
            </button>
            <button @click="saveToLibrary" class="action-btn primary">
              <i class="fas fa-save"></i>
              Salvar na Biblioteca
            </button>
          </div>
        </div>
      </transition>
    </div>

    <!-- History Modal -->
    <transition name="modal-fade">
      <div v-if="showHistory" class="modal-overlay" @click.self="showHistory = false">
        <div class="modal-content">
          <div class="modal-header">
            <h2><i class="fas fa-history"></i> Histórico de Resumos</h2>
            <button @click="showHistory = false" class="close-btn">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="modal-body">
            <div class="history-list">
              <div v-for="item in summaryHistory" 
                   :key="item.id"
                   @click="loadHistoryItem(item)"
                   class="history-item">
                <div class="history-icon">
                  <i :class="`fas fa-${getModeIcon(item.mode)}`"></i>
                </div>
                <div class="history-info">
                  <h4>{{ item.title }}</h4>
                  <p>{{ item.preview }}</p>
                  <span class="history-date">{{ formatDate(item.date) }}</span>
                </div>
                <div class="history-actions">
                  <button @click.stop="deleteHistoryItem(item.id)" class="delete-btn">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>

    <!-- Settings Modal -->
    <transition name="modal-fade">
      <div v-if="showSettings" class="modal-overlay" @click.self="showSettings = false">
        <div class="modal-content">
          <div class="modal-header">
            <h2><i class="fas fa-cog"></i> Configurações do Gerador</h2>
            <button @click="showSettings = false" class="close-btn">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="modal-body">
            <div class="settings-section">
              <h3>Preferências de IA</h3>
              <div class="settings-group">
                <label>
                  <span>Modelo de IA</span>
                  <select v-model="aiModel">
                    <option value="gpt4">GPT-4 (Mais Preciso)</option>
                    <option value="gpt3">GPT-3.5 (Mais Rápido)</option>
                    <option value="claude">Claude (Balanceado)</option>
                  </select>
                </label>
                <label>
                  <span>Temperatura (Criatividade)</span>
                  <input type="range" v-model="aiTemperature" min="0" max="1" step="0.1" />
                  <span class="range-value">{{ aiTemperature }}</span>
                </label>
              </div>
            </div>
            <div class="settings-section">
              <h3>Preferências de Saída</h3>
              <div class="settings-group">
                <label>
                  <span>Formato Padrão</span>
                  <select v-model="defaultFormat">
                    <option value="markdown">Markdown</option>
                    <option value="html">HTML</option>
                    <option value="plain">Texto Simples</option>
                  </select>
                </label>
                <label>
                  <span>Idioma Padrão</span>
                  <select v-model="defaultLanguage">
                    <option value="pt">Português</option>
                    <option value="en">Inglês</option>
                    <option value="es">Espanhol</option>
                  </select>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

export default {
  name: 'NotesAIGenerator',
  
  setup() {
    const store = useStore()
    const router = useRouter()
    
    // State
    const inputContent = ref('')
    const selectedMode = ref('summary')
    const selectedLength = ref('medium')
    const selectedStyle = ref('academic')
    const selectedFocus = ref([])
    const customWordCount = ref(500)
    const isGenerating = ref(false)
    const generatedSummary = ref(null)
    const showHistory = ref(false)
    const showSettings = ref(false)
    const isRecording = ref(false)
    
    // Options
    const includeKeyPoints = ref(true)
    const generateQuestions = ref(false)
    const includeCitations = ref(true)
    const multiLanguage = ref(false)
    
    // Settings
    const aiModel = ref('gpt4')
    const aiTemperature = ref(0.7)
    const defaultFormat = ref('markdown')
    const defaultLanguage = ref('pt')
    
    // Generation Modes
    const generationModes = [
      {
        id: 'summary',
        name: 'Resumo Conciso',
        description: 'Resumo direto e objetivo do conteúdo',
        icon: 'compress-alt',
        badge: 'Popular'
      },
      {
        id: 'synthesis',
        name: 'Síntese Analítica',
        description: 'Análise aprofundada com insights',
        icon: 'microscope'
      },
      {
        id: 'critical',
        name: 'Análise Crítica',
        description: 'Avaliação crítica e comparativa',
        icon: 'balance-scale'
      },
      {
        id: 'mindmap',
        name: 'Mapa Mental',
        description: 'Estrutura hierárquica visual',
        icon: 'project-diagram',
        badge: 'Novo'
      },
      {
        id: 'outline',
        name: 'Esquema',
        description: 'Tópicos organizados em níveis',
        icon: 'list-ul'
      },
      {
        id: 'flashcards',
        name: 'Flashcards',
        description: 'Cartões de estudo otimizados',
        icon: 'layer-group'
      }
    ]
    
    // Length Options
    const lengthOptions = [
      {
        id: 'micro',
        name: 'Micro',
        description: '~100 palavras',
        icon: '🔸'
      },
      {
        id: 'short',
        name: 'Curto',
        description: '~250 palavras',
        icon: '🔹'
      },
      {
        id: 'medium',
        name: 'Médio',
        description: '~500 palavras',
        icon: '🔷'
      },
      {
        id: 'long',
        name: 'Longo',
        description: '~1000 palavras',
        icon: '🔶'
      },
      {
        id: 'custom',
        name: 'Personalizado',
        description: 'Definir tamanho',
        icon: '⚙️'
      }
    ]
    
    // Style Options
    const styleOptions = [
      {
        id: 'academic',
        name: 'Acadêmico',
        icon: 'graduation-cap'
      },
      {
        id: 'casual',
        name: 'Casual',
        icon: 'comment'
      },
      {
        id: 'technical',
        name: 'Técnico',
        icon: 'cog'
      },
      {
        id: 'creative',
        name: 'Criativo',
        icon: 'palette'
      }
    ]
    
    // Focus Options
    const focusOptions = [
      { id: 'concepts', name: 'Conceitos', icon: 'lightbulb' },
      { id: 'facts', name: 'Fatos', icon: 'check-circle' },
      { id: 'procedures', name: 'Procedimentos', icon: 'tasks' },
      { id: 'comparisons', name: 'Comparações', icon: 'exchange-alt' },
      { id: 'examples', name: 'Exemplos', icon: 'flask' },
      { id: 'definitions', name: 'Definições', icon: 'book' }
    ]
    
    // Tips
    const tips = [
      'Textos mais longos geram resumos mais detalhados',
      'Use o modo Síntese para análises mais profundas',
      'Ative questões de estudo para melhor retenção',
      'Experimente diferentes estilos para cada tipo de conteúdo',
      'Salve seus resumos favoritos na biblioteca'
    ]
    
    const currentTip = ref(tips[0])
    
    // Content Analysis
    const contentAnalysis = ref(null)
    
    // History
    const summaryHistory = ref([
      {
        id: 1,
        title: 'Sistema Cardiovascular',
        preview: 'Resumo completo sobre anatomia e fisiologia...',
        mode: 'summary',
        date: new Date('2024-01-15')
      },
      {
        id: 2,
        title: 'Farmacologia Básica',
        preview: 'Síntese dos principais grupos farmacológicos...',
        mode: 'synthesis',
        date: new Date('2024-01-14')
      }
    ])
    
    // Computed
    const wordCount = computed(() => {
      return inputContent.value.split(/\s+/).filter(word => word.length > 0).length
    })
    
    const paragraphCount = computed(() => {
      return inputContent.value.split(/\n\n+/).filter(p => p.trim().length > 0).length
    })
    
    const estimatedReadTime = computed(() => {
      return Math.ceil(wordCount.value / 200)
    })
    
    const canGenerate = computed(() => {
      return inputContent.value.trim().length > 50 && !isGenerating.value
    })
    
    // Methods
    const analyzeContent = () => {
      if (inputContent.value.length > 100) {
        // Simulate content analysis
        contentAnalysis.value = {
          complexity: 'Intermediário',
          level: 'Graduação',
          topics: ['Anatomia', 'Fisiologia', 'Patologia']
        }
      }
    }
    
    const toggleFocus = (focusId) => {
      const index = selectedFocus.value.indexOf(focusId)
      if (index > -1) {
        selectedFocus.value.splice(index, 1)
      } else {
        selectedFocus.value.push(focusId)
      }
    }
    
    const generateSummary = async () => {
      isGenerating.value = true
      
      // Simulate AI generation
      setTimeout(() => {
        generatedSummary.value = {
          title: 'Resumo: Sistema Cardiovascular',
          content: `
            <p>O sistema cardiovascular é responsável pelo transporte de nutrientes, oxigênio e hormônios por todo o corpo, além da remoção de resíduos metabólicos.</p>
            
            <h4>Componentes Principais</h4>
            <p>O sistema é composto pelo coração (bomba muscular), vasos sanguíneos (artérias, veias e capilares) e sangue (tecido líquido).</p>
            
            <h4>Função Cardíaca</h4>
            <p>O coração bombeia aproximadamente 5 litros de sangue por minuto através de contrações rítmicas coordenadas pelo sistema de condução elétrica.</p>
            
            <h4>Circulação</h4>
            <p>A circulação sistêmica fornece sangue oxigenado aos tecidos, enquanto a circulação pulmonar realiza a troca gasosa nos pulmões.</p>
          `,
          compressionRate: 78,
          qualityScore: 9.2,
          readTime: 3,
          keyPoints: [
            'Transporte de substâncias vitais e remoção de resíduos',
            'Coração como bomba central do sistema',
            'Dois circuitos: sistêmico e pulmonar',
            'Regulação através de mecanismos neurais e hormonais'
          ],
          questions: [
            {
              text: 'Quais são os principais componentes do sistema cardiovascular?',
              answer: 'Coração, vasos sanguíneos (artérias, veias e capilares) e sangue.',
              showAnswer: false
            },
            {
              text: 'Qual é o débito cardíaco normal em repouso?',
              answer: 'Aproximadamente 5 litros por minuto.',
              showAnswer: false
            }
          ],
          relatedTopics: ['Pressão Arterial', 'Ciclo Cardíaco', 'Eletrocardiograma', 'Doenças Cardiovasculares']
        }
        
        isGenerating.value = false
      }, 3000)
    }
    
    const copySummary = () => {
      // Copy to clipboard
      console.log('Copying summary...')
    }
    
    const editSummary = () => {
      // Open editor
      router.push('/resumos-notas/editor')
    }
    
    const exportSummary = () => {
      // Export options
      console.log('Exporting summary...')
    }
    
    const shareSummary = () => {
      // Share options
      console.log('Sharing summary...')
    }
    
    const toggleAnswer = (index) => {
      generatedSummary.value.questions[index].showAnswer = !generatedSummary.value.questions[index].showAnswer
    }
    
    const searchTopic = (topic) => {
      // Search for related content
      console.log('Searching for:', topic)
    }
    
    const regenerateSummary = () => {
      generateSummary()
    }
    
    const saveToLibrary = () => {
      // Save to user library
      console.log('Saving to library...')
    }
    
    const importFile = () => {
      // File import logic
      console.log('Importing file...')
    }
    
    const pasteFromClipboard = async () => {
      try {
        const text = await navigator.clipboard.readText()
        inputContent.value = text
      } catch (err) {
        console.error('Failed to read clipboard:', err)
      }
    }
    
    const startRecording = () => {
      isRecording.value = !isRecording.value
      // Voice recording logic
    }
    
    const getModeIcon = (modeId) => {
      const mode = generationModes.find(m => m.id === modeId)
      return mode ? mode.icon : 'file-alt'
    }
    
    const formatDate = (date) => {
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date)
    }
    
    const loadHistoryItem = (item) => {
      // Load historical summary
      console.log('Loading:', item)
      showHistory.value = false
    }
    
    const deleteHistoryItem = (id) => {
      summaryHistory.value = summaryHistory.value.filter(item => item.id !== id)
    }
    
    // Lifecycle
    onMounted(() => {
      // Rotate tips
      setInterval(() => {
        const currentIndex = tips.indexOf(currentTip.value)
        currentTip.value = tips[(currentIndex + 1) % tips.length]
      }, 5000)
    })
    
    return {
      // State
      inputContent,
      selectedMode,
      selectedLength,
      selectedStyle,
      selectedFocus,
      customWordCount,
      isGenerating,
      generatedSummary,
      showHistory,
      showSettings,
      isRecording,
      contentAnalysis,
      summaryHistory,
      currentTip,
      
      // Options
      includeKeyPoints,
      generateQuestions,
      includeCitations,
      multiLanguage,
      
      // Settings
      aiModel,
      aiTemperature,
      defaultFormat,
      defaultLanguage,
      
      // Data
      generationModes,
      lengthOptions,
      styleOptions,
      focusOptions,
      
      // Computed
      wordCount,
      paragraphCount,
      estimatedReadTime,
      canGenerate,
      
      // Methods
      analyzeContent,
      toggleFocus,
      generateSummary,
      copySummary,
      editSummary,
      exportSummary,
      shareSummary,
      toggleAnswer,
      searchTopic,
      regenerateSummary,
      saveToLibrary,
      importFile,
      pasteFromClipboard,
      startRecording,
      getModeIcon,
      formatDate,
      loadHistoryItem,
      deleteHistoryItem
    }
  }
}
</script>

<style scoped>
.notes-ai-generator {
  min-height: 100vh;
  background: var(--theme-background);
  position: relative;
  overflow: hidden;
}

/* Animated Background */
.ai-background {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.brain-network {
  position: absolute;
  inset: 0;
  background-image: 
    radial-gradient(circle at 20% 50%, var(--theme-primary) 1px, transparent 1px),
    radial-gradient(circle at 80% 80%, var(--theme-secondary) 1px, transparent 1px),
    radial-gradient(circle at 50% 20%, var(--theme-accent) 1px, transparent 1px);
  background-size: 100px 100px, 150px 150px, 200px 200px;
  opacity: 0.1;
  animation: networkPulse 10s ease-in-out infinite;
}

@keyframes networkPulse {
  0%, 100% { opacity: 0.1; }
  50% { opacity: 0.15; }
}

.data-flow {
  position: absolute;
  inset: 0;
  background: linear-gradient(180deg, transparent, var(--theme-primary) 50%, transparent);
  opacity: 0.05;
  animation: dataFlow 5s linear infinite;
}

@keyframes dataFlow {
  from { transform: translateY(-100%); }
  to { transform: translateY(100%); }
}

.gradient-waves {
  position: absolute;
  inset: 0;
  background: 
    radial-gradient(ellipse at top, var(--theme-primary) 0%, transparent 50%),
    radial-gradient(ellipse at bottom, var(--theme-secondary) 0%, transparent 50%);
  opacity: 0.1;
  filter: blur(100px);
}

/* Header */
.generator-header {
  position: relative;
  z-index: 10;
  background: var(--theme-glass);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--theme-border);
  padding: 2rem;
}

.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  color: var(--theme-text);
  text-decoration: none;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: var(--theme-glassHover);
  transform: translateX(-3px);
}

.header-title {
  text-align: center;
  flex: 1;
}

.header-title h1 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  font-size: 2.5rem;
  color: var(--theme-text);
  margin-bottom: 0.5rem;
}

.header-title p {
  color: var(--theme-textMuted);
  font-size: 1.125rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.history-btn,
.settings-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  color: var(--theme-text);
  cursor: pointer;
  transition: all 0.3s ease;
}

.history-btn:hover,
.settings-btn:hover {
  background: var(--theme-glassHover);
  transform: translateY(-2px);
}

/* Mode Selector */
.mode-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.mode-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--theme-surface);
  border: 2px solid var(--theme-border);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.mode-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px var(--theme-shadow);
}

.mode-btn.active {
  border-color: var(--theme-primary);
  background: var(--theme-surfaceLight);
}

.mode-btn.active::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, var(--theme-primary), transparent);
  opacity: 0.1;
}

.mode-icon {
  width: 48px;
  height: 48px;
  background: var(--theme-glass);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--theme-primary);
}

.mode-info {
  flex: 1;
}

.mode-info h3 {
  color: var(--theme-text);
  margin-bottom: 0.25rem;
}

.mode-info p {
  color: var(--theme-textMuted);
  font-size: 0.875rem;
}

.mode-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
}

.mode-badge span {
  padding: 0.25rem 0.75rem;
  background: var(--theme-primary);
  color: var(--theme-background);
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: bold;
}

/* Main Content */
.generator-content {
  position: relative;
  z-index: 10;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Input Section */
.input-section {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.section-header h2 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--theme-text);
}

.input-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  color: var(--theme-text);
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: var(--theme-glassHover);
  transform: translateY(-2px);
}

.action-btn.recording {
  background: var(--theme-danger);
  color: white;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.input-container {
  position: relative;
}

.content-input {
  width: 100%;
  min-height: 300px;
  padding: 1rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
  color: var(--theme-text);
  font-family: inherit;
  font-size: 1rem;
  resize: vertical;
  outline: none;
  transition: all 0.3s ease;
}

.content-input:focus {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 3px rgba(var(--theme-primaryRGB), 0.1);
}

.input-stats {
  display: flex;
  gap: 1.5rem;
  margin-top: 1rem;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--theme-textMuted);
  font-size: 0.875rem;
}

.stat i {
  color: var(--theme-primary);
}

/* Content Analysis */
.content-analysis {
  margin-top: 1.5rem;
  padding: 1rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
}

.content-analysis h3 {
  color: var(--theme-text);
  margin-bottom: 1rem;
}

.analysis-grid {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.analysis-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--theme-text);
}

.analysis-item i {
  color: var(--theme-secondary);
}

/* Configuration Section */
.config-section {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.config-card {
  padding: 1.5rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
}

.config-card h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--theme-text);
  margin-bottom: 1rem;
}

/* Length Selector */
.length-selector {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.length-btn {
  display: grid;
  grid-template-columns: 40px 1fr auto;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.length-btn:hover {
  background: var(--theme-surfaceHover);
}

.length-btn.active {
  background: var(--theme-primary);
  color: var(--theme-background);
  border-color: var(--theme-primary);
}

.length-icon {
  font-size: 1.25rem;
  text-align: center;
}

.length-name {
  font-weight: 500;
}

.length-desc {
  font-size: 0.875rem;
  opacity: 0.8;
}

.custom-length {
  margin-top: 1rem;
}

.custom-length label {
  display: block;
  color: var(--theme-text);
  margin-bottom: 0.5rem;
}

.custom-length input {
  width: 100%;
  padding: 0.5rem;
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 6px;
  color: var(--theme-text);
  outline: none;
}

/* Style Options */
.style-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.style-option {
  display: block;
  cursor: pointer;
}

.style-option input {
  display: none;
}

.style-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.style-option input:checked + .style-content {
  background: var(--theme-primary);
  color: var(--theme-background);
  border-color: var(--theme-primary);
}

/* Focus Tags */
.focus-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.focus-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 1rem;
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 20px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.focus-tag:hover {
  background: var(--theme-surfaceHover);
}

.focus-tag.active {
  background: var(--theme-secondary);
  color: var(--theme-background);
  border-color: var(--theme-secondary);
}

/* Advanced Options */
.advanced-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.option-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  color: var(--theme-text);
}

.option-toggle input {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

/* Generate Section */
.generate-section {
  text-align: center;
  padding: 2rem 0;
}

.generate-btn {
  position: relative;
  padding: 1.25rem 3rem;
  background: linear-gradient(135deg, var(--theme-primary), var(--theme-secondary));
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1.125rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.generate-btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(var(--theme-primaryRGB), 0.4);
}

.generate-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-content {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.btn-glow {
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, var(--theme-primary), var(--theme-secondary));
  border-radius: 12px;
  opacity: 0;
  filter: blur(20px);
  transition: opacity 0.3s ease;
}

.generate-btn:hover:not(:disabled) .btn-glow {
  opacity: 0.5;
}

.generation-tips {
  margin-top: 1rem;
  color: var(--theme-textMuted);
}

.generation-tips p {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Output Section */
.output-section {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 16px;
  padding: 2rem;
  margin-top: 2rem;
}

.output-actions {
  display: flex;
  gap: 0.5rem;
}

.summary-container {
  margin-top: 2rem;
}

.summary-header {
  margin-bottom: 1.5rem;
}

.summary-header h3 {
  color: var(--theme-text);
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.summary-meta {
  display: flex;
  gap: 1.5rem;
  color: var(--theme-textMuted);
  font-size: 0.875rem;
}

.summary-meta span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.summary-content {
  padding: 1.5rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
  color: var(--theme-text);
  line-height: 1.8;
}

.summary-content h4 {
  color: var(--theme-primary);
  margin: 1.5rem 0 0.75rem;
}

.summary-content p {
  margin-bottom: 1rem;
}

/* Key Points */
.key-points {
  margin-top: 2rem;
  padding: 1.5rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
}

.key-points h4 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--theme-text);
  margin-bottom: 1rem;
}

.key-points ul {
  list-style: none;
  padding: 0;
}

.key-points li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--theme-text);
}

.key-points li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--theme-success);
  font-weight: bold;
}

/* Study Questions */
.study-questions {
  margin-top: 2rem;
  padding: 1.5rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
}

.study-questions h4 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--theme-text);
  margin-bottom: 1rem;
}

.questions-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.question-item {
  padding: 1rem;
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
}

.question-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: var(--theme-primary);
  color: var(--theme-background);
  border-radius: 50%;
  font-size: 0.875rem;
  font-weight: bold;
  margin-right: 0.5rem;
}

.question-item p {
  color: var(--theme-text);
  margin-bottom: 0.5rem;
}

.show-answer-btn {
  padding: 0.25rem 0.75rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 6px;
  color: var(--theme-primary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.show-answer-btn:hover {
  background: var(--theme-primary);
  color: var(--theme-background);
}

.answer {
  margin-top: 0.5rem;
  padding: 0.75rem;
  background: var(--theme-success);
  color: white;
  border-radius: 6px;
  font-style: italic;
}

/* Related Topics */
.related-topics {
  margin-top: 2rem;
  padding: 1.5rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
}

.related-topics h4 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--theme-text);
  margin-bottom: 1rem;
}

.topics-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.topic-tag {
  padding: 0.5rem 1rem;
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 20px;
  color: var(--theme-text);
  cursor: pointer;
  transition: all 0.3s ease;
}

.topic-tag:hover {
  background: var(--theme-primary);
  color: var(--theme-background);
  transform: scale(1.05);
}

/* Summary Actions */
.summary-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

.action-btn.secondary {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
}

.action-btn.primary {
  background: var(--theme-success);
  color: white;
  border: none;
}

.action-btn.primary:hover {
  background: var(--theme-successHover);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-content {
  background: var(--theme-surface);
  border-radius: 20px;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--theme-border);
}

.modal-header h2 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--theme-text);
}

.close-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  color: var(--theme-text);
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: var(--theme-danger);
  color: white;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

/* History List */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.history-item:hover {
  background: var(--theme-glassHover);
  transform: translateX(5px);
}

.history-icon {
  width: 48px;
  height: 48px;
  background: var(--theme-primary);
  color: var(--theme-background);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.history-info {
  flex: 1;
}

.history-info h4 {
  color: var(--theme-text);
  margin-bottom: 0.25rem;
}

.history-info p {
  color: var(--theme-textMuted);
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.history-date {
  color: var(--theme-primary);
  font-size: 0.75rem;
}

.history-actions {
  display: flex;
  gap: 0.5rem;
}

.delete-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 6px;
  color: var(--theme-danger);
  cursor: pointer;
  transition: all 0.3s ease;
}

.delete-btn:hover {
  background: var(--theme-danger);
  color: white;
}

/* Settings */
.settings-section {
  margin-bottom: 2rem;
}

.settings-section h3 {
  color: var(--theme-text);
  margin-bottom: 1rem;
}

.settings-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.settings-group label {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  color: var(--theme-text);
}

.settings-group select,
.settings-group input[type="range"] {
  padding: 0.5rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 6px;
  color: var(--theme-text);
  outline: none;
}

.range-value {
  text-align: center;
  color: var(--theme-primary);
  font-weight: bold;
}

/* Transitions */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.5s ease;
}

.slide-up-enter-from {
  transform: translateY(50px);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-50px);
  opacity: 0;
}

.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.modal-fade-enter-active .modal-content,
.modal-fade-leave-active .modal-content {
  transition: transform 0.3s ease;
}

.modal-fade-enter-from .modal-content,
.modal-fade-leave-to .modal-content {
  transform: scale(0.9);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .header-top {
    flex-direction: column;
    gap: 1rem;
  }
  
  .mode-selector {
    grid-template-columns: 1fr;
  }
  
  .config-grid {
    grid-template-columns: 1fr;
  }
  
  .style-options {
    grid-template-columns: 1fr;
  }
  
  .summary-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .summary-actions {
    flex-direction: column;
  }
  
  .action-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>