<template>
  <div class="notes-insights">
    <!-- Animated Data Background -->
    <div class="data-visualization-bg">
      <canvas ref="dataCanvas" class="data-canvas"></canvas>
      <div class="floating-metrics">
        <div v-for="i in 20" :key="`metric-${i}`" class="floating-metric">
          <span class="metric-value">{{ randomMetric() }}</span>
        </div>
      </div>
    </div>

    <!-- Header -->
    <div class="insights-header">
      <router-link to="/resumos-notas" class="nav-back">
        <i class="fas fa-arrow-left"></i>
        <span>Voltar</span>
      </router-link>
      
      <div class="header-center">
        <h1 class="main-title">
          <div class="title-visual">
            <font-awesome-icon icon="chart-line" class="title-icon" />
            <div class="icon-pulse"></div>
          </div>
          <span>Análise & Insights</span>
        </h1>
        <p class="subtitle">Dashboard analítico com insights profundos sobre seu aprendizado</p>
      </div>

      <div class="time-controls">
        <button v-for="period in timePeriods" 
                :key="period.id"
                @click="selectedPeriod = period.id"
                :class="{ active: selectedPeriod === period.id }"
                class="time-btn">
          {{ period.label }}
        </button>
      </div>
    </div>

    <!-- Main Dashboard -->
    <div class="insights-dashboard">
      <!-- Key Metrics -->
      <div class="metrics-row">
        <div v-for="metric in keyMetrics" 
             :key="metric.id"
             class="metric-card"
             :style="{ '--metric-color': metric.color }">
          <div class="metric-header">
            <div class="metric-icon">
              <i :class="`fas fa-${\1}`"></i>
            </div>
            <div class="metric-change" :class="metric.trend">
              <i :class="`fas fa-${\1}`"></i>
              <span>{{ metric.change }}%</span>
            </div>
          </div>
          <div class="metric-value">{{ metric.value }}</div>
          <div class="metric-label">{{ metric.label }}</div>
          <div class="metric-chart">
            <canvas :ref="`chart-${metric.id}`"></canvas>
          </div>
        </div>
      </div>

      <!-- Analytics Grid -->
      <div class="analytics-grid">
        <!-- Study Progress Chart -->
        <div class="analytics-card large">
          <div class="card-header">
            <h3>
              <i class="fas fa-chart-line"></i>
              Progresso de Estudos
            </h3>
            <div class="card-actions">
              <button v-for="view in chartViews" 
                      :key="view"
                      @click="progressChartView = view"
                      :class="{ active: progressChartView === view }"
                      class="view-btn">
                {{ view }}
              </button>
            </div>
          </div>
          <div class="chart-container">
            <canvas ref="progressChart"></canvas>
          </div>
        </div>

        <!-- Heat Map -->
        <div class="analytics-card">
          <div class="card-header">
            <h3>
              <i class="fas fa-fire"></i>
              Mapa de Atividades
            </h3>
          </div>
          <div class="heatmap-container">
            <div class="heatmap-grid">
              <div v-for="week in 52" :key="`week-${week}`" class="heatmap-week">
                <div v-for="day in 7" 
                     :key="`day-${week}-${day}`"
                     class="heatmap-cell"
                     :style="{ background: getHeatmapColor(week, day) }"
                     @mouseenter="showTooltip($event, week, day)"
                     @mouseleave="hideTooltip">
                </div>
              </div>
            </div>
            <div class="heatmap-legend">
              <span>Menos</span>
              <div class="legend-scale">
                <span v-for="i in 5" :key="`scale-${i}`" 
                      class="scale-block"
                      :style="{ opacity: i * 0.2 }">
                </span>
              </div>
              <span>Mais</span>
            </div>
          </div>
        </div>

        <!-- Category Distribution -->
        <div class="analytics-card">
          <div class="card-header">
            <h3>
              <i class="fas fa-chart-pie"></i>
              Distribuição por Categoria
            </h3>
          </div>
          <div class="chart-container">
            <canvas ref="categoryChart"></canvas>
          </div>
          <div class="category-legend">
            <div v-for="cat in topCategories" 
                 :key="cat.name"
                 class="legend-item">
              <span class="legend-color" :style="{ background: cat.color }"></span>
              <span class="legend-name">{{ cat.name }}</span>
              <span class="legend-value">{{ cat.percentage }}%</span>
            </div>
          </div>
        </div>

        <!-- Learning Velocity -->
        <div class="analytics-card">
          <div class="card-header">
            <h3>
              <i class="fas fa-rocket"></i>
              Velocidade de Aprendizado
            </h3>
          </div>
          <div class="velocity-gauge">
            <div class="gauge-container">
              <svg viewBox="0 0 200 120">
                <path d="M 30 100 A 70 70 0 0 1 170 100" 
                      fill="none" 
                      stroke="var(--theme-border)" 
                      stroke-width="20"
                      stroke-linecap="round" />
                <path d="M 30 100 A 70 70 0 0 1 170 100" 
                      fill="none" 
                      :stroke="velocityColor" 
                      stroke-width="20"
                      stroke-linecap="round"
                      :stroke-dasharray="`${velocityProgress} 220`" />
                <text x="100" y="90" text-anchor="middle" class="gauge-value">
                  {{ velocityScore }}
                </text>
                <text x="100" y="110" text-anchor="middle" class="gauge-label">
                  pontos/dia
                </text>
              </svg>
            </div>
            <div class="velocity-insights">
              <p>{{ velocityInsight }}</p>
            </div>
          </div>
        </div>

        <!-- AI Recommendations -->
        <div class="analytics-card">
          <div class="card-header">
            <h3>
              <i class="fas fa-brain"></i>
              Recomendações da IA
            </h3>
          </div>
          <div class="ai-recommendations">
            <div v-for="rec in aiRecommendations" 
                 :key="rec.id"
                 class="recommendation-item">
              <div class="rec-icon" :style="{ background: rec.color }">
                <i :class="`fas fa-${\1}`"></i>
              </div>
              <div class="rec-content">
                <h4>{{ rec.title }}</h4>
                <p>{{ rec.description }}</p>
                <div class="rec-action">
                  <button @click="applyRecommendation(rec)" class="rec-btn">
                    Aplicar
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Performance Matrix -->
        <div class="analytics-card large">
          <div class="card-header">
            <h3>
              <i class="fas fa-th"></i>
              Matriz de Performance
            </h3>
          </div>
          <div class="performance-matrix">
            <div class="matrix-axes">
              <span class="y-label">Dificuldade</span>
              <span class="x-label">Tempo de Estudo</span>
            </div>
            <div class="matrix-grid">
              <div v-for="topic in performanceTopics" 
                   :key="topic.id"
                   class="matrix-point"
                   :style="{
                     left: topic.x + '%',
                     bottom: topic.y + '%',
                     '--point-color': topic.color
                   }"
                   @mouseenter="showTopicDetails($event, topic)"
                   @mouseleave="hideTooltip">
                <span class="point-label">{{ topic.abbr }}</span>
              </div>
              
              <!-- Quadrants -->
              <div class="quadrant q1">
                <span>Alta Performance</span>
              </div>
              <div class="quadrant q2">
                <span>Eficiência</span>
              </div>
              <div class="quadrant q3">
                <span>Desenvolvimento</span>
              </div>
              <div class="quadrant q4">
                <span>Desafio</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Detailed Stats -->
      <div class="detailed-stats">
        <h2 class="section-title">Estatísticas Detalhadas</h2>
        
        <div class="stats-tabs">
          <button v-for="tab in statsTabs" 
                  :key="tab.id"
                  @click="activeStatsTab = tab.id"
                  :class="{ active: activeStatsTab === tab.id }"
                  class="stats-tab">
            <i :class="`fas fa-${\1}`"></i>
            <span>{{ tab.label }}</span>
          </button>
        </div>

        <div class="stats-content">
          <!-- Content Stats -->
          <div v-if="activeStatsTab === 'content'" class="content-stats">
            <div class="stats-grid">
              <div v-for="stat in contentStats" 
                   :key="stat.id"
                   class="stat-item">
                <div class="stat-icon">
                  <i :class="`fas fa-${\1}`"></i>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ stat.value }}</div>
                  <div class="stat-label">{{ stat.label }}</div>
                  <div class="stat-trend">
                    <span :class="stat.trend">
                      {{ stat.trend === 'up' ? '+' : '-' }}{{ stat.change }}%
                    </span>
                    vs. período anterior
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Time Stats -->
          <div v-if="activeStatsTab === 'time'" class="time-stats">
            <div class="time-breakdown">
              <h3>Distribuição de Tempo</h3>
              <div class="time-chart">
                <canvas ref="timeChart"></canvas>
              </div>
            </div>
            <div class="peak-hours">
              <h3>Horários de Pico</h3>
              <div class="hours-heatmap">
                <div v-for="hour in 24" :key="`hour-${hour}`" class="hour-bar">
                  <div class="bar-fill" 
                       :style="{ height: getHourActivity(hour) + '%' }">
                  </div>
                  <span class="hour-label">{{ hour }}h</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Goals Stats -->
          <div v-if="activeStatsTab === 'goals'" class="goals-stats">
            <div class="goals-overview">
              <div class="goal-card" v-for="goal in studyGoals" :key="goal.id">
                <div class="goal-header">
                  <h4>{{ goal.name }}</h4>
                  <span class="goal-deadline">{{ goal.deadline }}</span>
                </div>
                <div class="goal-progress">
                  <div class="progress-bar">
                    <div class="progress-fill" 
                         :style="{ width: goal.progress + '%' }">
                    </div>
                  </div>
                  <span class="progress-text">{{ goal.progress }}%</span>
                </div>
                <div class="goal-tasks">
                  <div v-for="task in goal.tasks" 
                       :key="task.id"
                       class="task-item">
                    <input type="checkbox" 
                           :checked="task.completed"
                           @change="toggleTask(goal.id, task.id)" />
                    <span>{{ task.name }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Export Section -->
      <div class="export-section">
        <h3>Exportar Relatório</h3>
        <div class="export-options">
          <button v-for="format in exportFormats" 
                  :key="format.id"
                  @click="exportReport(format)"
                  class="export-btn">
            <i :class="`fas fa-${\1}`"></i>
            <span>{{ format.name }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Tooltip -->
    <div v-if="tooltip.show" 
         class="custom-tooltip"
         :style="{ 
           left: tooltip.x + 'px', 
           top: tooltip.y + 'px' 
         }">
      <div class="tooltip-content">
        <h4>{{ tooltip.title }}</h4>
        <p>{{ tooltip.content }}</p>
        <div v-if="tooltip.stats" class="tooltip-stats">
          <div v-for="stat in tooltip.stats" 
               :key="stat.label"
               class="tooltip-stat">
            <span class="stat-label">{{ stat.label }}:</span>
            <span class="stat-value">{{ stat.value }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import Chart from 'chart.js/auto'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

export default {
  name: 'NotesInsights',
  
  setup() {
    const store = useStore()
    const router = useRouter()
    
    // Refs
    const dataCanvas = ref(null)
    const progressChart = ref(null)
    const categoryChart = ref(null)
    const timeChart = ref(null)
    
    // State
    const selectedPeriod = ref('month')
    const progressChartView = ref('Linha')
    const activeStatsTab = ref('content')
    
    // Time Periods
    const timePeriods = [
      { id: 'week', label: 'Semana' },
      { id: 'month', label: 'Mês' },
      { id: 'quarter', label: 'Trimestre' },
      { id: 'year', label: 'Ano' }
    ]
    
    // Key Metrics
    const keyMetrics = ref([
      {
        id: 'total-notes',
        icon: 'file-lines',
        label: 'Total de Notas',
        value: '1,234',
        change: 12,
        trend: 'up',
        color: '#00ffff'
      },
      {
        id: 'study-hours',
        icon: 'clock',
        label: 'Horas de Estudo',
        value: '156h',
        change: 8,
        trend: 'up',
        color: '#ff00ff'
      },
      {
        id: 'completion-rate',
        icon: 'check-circle',
        label: 'Taxa de Conclusão',
        value: '87%',
        change: 5,
        trend: 'up',
        color: '#ffff00'
      },
      {
        id: 'engagement',
        icon: 'fire',
        label: 'Engajamento',
        value: '94%',
        change: 3,
        trend: 'down',
        color: '#00ff88'
      }
    ])
    
    // Chart Views
    const chartViews = ['Linha', 'Barra', 'Área']
    
    // Categories
    const topCategories = ref([
      { name: 'Anatomia', percentage: 35, color: '#00ffff' },
      { name: 'Fisiologia', percentage: 25, color: '#ff00ff' },
      { name: 'Farmacologia', percentage: 20, color: '#ffff00' },
      { name: 'Patologia', percentage: 12, color: '#00ff88' },
      { name: 'Outros', percentage: 8, color: '#ff8800' }
    ])
    
    // Learning Velocity
    const velocityScore = ref(87)
    const velocityProgress = computed(() => (velocityScore.value / 100) * 220)
    const velocityColor = computed(() => {
      if (velocityScore.value >= 80) return '#00ff88'
      if (velocityScore.value >= 60) return '#ffff00'
      if (velocityScore.value >= 40) return '#ff8800'
      return '#ff0088'
    })
    const velocityInsight = computed(() => {
      if (velocityScore.value >= 80) return 'Excelente ritmo! Você está 23% acima da média.'
      if (velocityScore.value >= 60) return 'Bom progresso! Continue assim.'
      return 'Considere aumentar o tempo de estudo diário.'
    })
    
    // AI Recommendations
    const aiRecommendations = ref([
      {
        id: 1,
        title: 'Revisar Conceitos Básicos',
        description: 'Detectamos lacunas em anatomia básica. Recomendamos uma revisão.',
        icon: 'lightbulb',
        color: '#00ffff'
      },
      {
        id: 2,
        title: 'Otimizar Horário de Estudo',
        description: 'Seus melhores resultados são entre 19h-22h. Ajuste sua rotina.',
        icon: 'clock',
        color: '#ff00ff'
      },
      {
        id: 3,
        title: 'Aumentar Prática',
        description: 'Adicione mais exercícios práticos para consolidar o aprendizado.',
        icon: 'dumbbell',
        color: '#ffff00'
      }
    ])
    
    // Performance Topics
    const performanceTopics = ref([
      { id: 1, name: 'Sistema Cardiovascular', abbr: 'CV', x: 75, y: 80, color: '#00ffff' },
      { id: 2, name: 'Sistema Nervoso', abbr: 'SN', x: 60, y: 70, color: '#ff00ff' },
      { id: 3, name: 'Farmacologia Básica', abbr: 'FB', x: 40, y: 50, color: '#ffff00' },
      { id: 4, name: 'Anatomia Óssea', abbr: 'AO', x: 85, y: 65, color: '#00ff88' },
      { id: 5, name: 'Histologia', abbr: 'HI', x: 30, y: 40, color: '#ff8800' },
      { id: 6, name: 'Embriologia', abbr: 'EM', x: 20, y: 75, color: '#8800ff' }
    ])
    
    // Stats Tabs
    const statsTabs = [
      { id: 'content', label: 'Conteúdo', icon: 'file-alt' },
      { id: 'time', label: 'Tempo', icon: 'clock' },
      { id: 'goals', label: 'Metas', icon: 'bullseye' }
    ]
    
    // Content Stats
    const contentStats = ref([
      { id: 1, icon: 'file-lines', label: 'Notas Criadas', value: '234', change: 15, trend: 'up' },
      { id: 2, icon: 'brain', label: 'Resumos Gerados', value: '89', change: 23, trend: 'up' },
      { id: 3, icon: 'project-diagram', label: 'Mapas Mentais', value: '45', change: 8, trend: 'up' },
      { id: 4, icon: 'layer-group', label: 'Flashcards', value: '567', change: 12, trend: 'down' },
      { id: 5, icon: 'share-alt', label: 'Compartilhamentos', value: '123', change: 34, trend: 'up' },
      { id: 6, icon: 'bookmark', label: 'Favoritos', value: '78', change: 5, trend: 'up' }
    ])
    
    // Study Goals
    const studyGoals = ref([
      {
        id: 1,
        name: 'Dominar Anatomia Cardiovascular',
        deadline: '30 dias',
        progress: 68,
        tasks: [
          { id: 1, name: 'Revisar vasos sanguíneos', completed: true },
          { id: 2, name: 'Estudar ciclo cardíaco', completed: true },
          { id: 3, name: 'Praticar questões', completed: false },
          { id: 4, name: 'Criar mapa mental', completed: false }
        ]
      },
      {
        id: 2,
        name: 'Completar Módulo de Farmacologia',
        deadline: '15 dias',
        progress: 45,
        tasks: [
          { id: 1, name: 'Farmacocinética', completed: true },
          { id: 2, name: 'Farmacodinâmica', completed: false },
          { id: 3, name: 'Interações medicamentosas', completed: false }
        ]
      }
    ])
    
    // Export Formats
    const exportFormats = [
      { id: 'pdf', name: 'PDF', icon: 'file-pdf' },
      { id: 'excel', name: 'Excel', icon: 'file-excel' },
      { id: 'ppt', name: 'PowerPoint', icon: 'file-powerpoint' }
    ]
    
    // Tooltip
    const tooltip = reactive({
      show: false,
      x: 0,
      y: 0,
      title: '',
      content: '',
      stats: null
    })
    
    // Chart instances
    let charts = {}
    
    // Methods
    const randomMetric = () => {
      const metrics = ['98%', '1.2K', '456', '87%', '23h', '4.8', '92%']
      return metrics[Math.floor(Math.random() * metrics.length)]
    }
    
    const getHeatmapColor = (week, day) => {
      const intensity = Math.random()
      if (intensity > 0.8) return 'var(--theme-primary)'
      if (intensity > 0.6) return 'rgba(0, 255, 255, 0.7)'
      if (intensity > 0.4) return 'rgba(0, 255, 255, 0.5)'
      if (intensity > 0.2) return 'rgba(0, 255, 255, 0.3)'
      return 'var(--theme-glass)'
    }
    
    const getHourActivity = (hour) => {
      // Simulate peak hours
      if (hour >= 19 && hour <= 22) return 80 + Math.random() * 20
      if (hour >= 14 && hour <= 17) return 60 + Math.random() * 20
      if (hour >= 9 && hour <= 12) return 40 + Math.random() * 20
      if (hour >= 6 && hour <= 8) return 20 + Math.random() * 20
      return Math.random() * 20
    }
    
    const showTooltip = (event, week, day) => {
      const date = new Date()
      date.setDate(date.getDate() - (52 - week) * 7 + day)
      
      tooltip.show = true
      tooltip.x = event.clientX + 10
      tooltip.y = event.clientY - 10
      tooltip.title = date.toLocaleDateString('pt-BR')
      tooltip.content = `${Math.floor(Math.random() * 10)} notas criadas`
    }
    
    const showTopicDetails = (event, topic) => {
      tooltip.show = true
      tooltip.x = event.clientX + 10
      tooltip.y = event.clientY - 10
      tooltip.title = topic.name
      tooltip.stats = [
        { label: 'Tempo de estudo', value: `${topic.x}h` },
        { label: 'Dificuldade', value: `${topic.y}%` },
        { label: 'Progresso', value: '78%' }
      ]
    }
    
    const hideTooltip = () => {
      tooltip.show = false
    }
    
    const applyRecommendation = (rec) => {
      console.log('Applying recommendation:', rec)
    }
    
    const toggleTask = (goalId, taskId) => {
      const goal = studyGoals.value.find(g => g.id === goalId)
      const task = goal.tasks.find(t => t.id === taskId)
      task.completed = !task.completed
      
      // Recalculate progress
      const completedTasks = goal.tasks.filter(t => t.completed).length
      goal.progress = Math.round((completedTasks / goal.tasks.length) * 100)
    }
    
    const exportReport = (format) => {
      console.log('Exporting report as:', format)
    }
    
    // Initialize Charts
    const initCharts = () => {
      // Progress Chart
      if (progressChart.value) {
        const ctx = progressChart.value.getContext('2d')
        charts.progress = new Chart(ctx, {
          type: 'line',
          data: {
            labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
            datasets: [{
              label: 'Notas Criadas',
              data: [65, 78, 90, 120, 145, 178],
              borderColor: '#00ffff',
              backgroundColor: 'rgba(0, 255, 255, 0.1)',
              tension: 0.4
            }, {
              label: 'Horas de Estudo',
              data: [28, 32, 45, 52, 61, 72],
              borderColor: '#ff00ff',
              backgroundColor: 'rgba(255, 0, 255, 0.1)',
              tension: 0.4
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                labels: {
                  color: 'var(--theme-text)'
                }
              }
            },
            scales: {
              y: {
                grid: {
                  color: 'var(--theme-border)'
                },
                ticks: {
                  color: 'var(--theme-textMuted)'
                }
              },
              x: {
                grid: {
                  color: 'var(--theme-border)'
                },
                ticks: {
                  color: 'var(--theme-textMuted)'
                }
              }
            }
          }
        })
      }
      
      // Category Chart
      if (categoryChart.value) {
        const ctx = categoryChart.value.getContext('2d')
        charts.category = new Chart(ctx, {
          type: 'doughnut',
          data: {
            labels: topCategories.value.map(c => c.name),
            datasets: [{
              data: topCategories.value.map(c => c.percentage),
              backgroundColor: topCategories.value.map(c => c.color),
              borderWidth: 0
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              }
            }
          }
        })
      }
      
      // Time Chart
      if (timeChart.value) {
        const ctx = timeChart.value.getContext('2d')
        charts.time = new Chart(ctx, {
          type: 'bar',
          data: {
            labels: ['Manhã', 'Tarde', 'Noite', 'Madrugada'],
            datasets: [{
              label: 'Horas',
              data: [25, 45, 68, 12],
              backgroundColor: ['#00ffff', '#ff00ff', '#ffff00', '#00ff88']
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              }
            },
            scales: {
              y: {
                grid: {
                  color: 'var(--theme-border)'
                },
                ticks: {
                  color: 'var(--theme-textMuted)'
                }
              },
              x: {
                grid: {
                  display: false
                },
                ticks: {
                  color: 'var(--theme-textMuted)'
                }
              }
            }
          }
        })
      }
      
      // Mini charts for metrics
      keyMetrics.value.forEach(metric => {
        const canvasRef = `chart-${metric.id}`
        const canvas = Array.isArray(canvasRef) ? canvasRef[0] : canvasRef
        
        if (canvas) {
          const ctx = canvas.getContext('2d')
          new Chart(ctx, {
            type: 'line',
            data: {
              labels: ['', '', '', '', '', ''],
              datasets: [{
                data: Array(6).fill(0).map(() => Math.random() * 100),
                borderColor: metric.color,
                borderWidth: 2,
                fill: false,
                tension: 0.4,
                pointRadius: 0
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: { display: false }
              },
              scales: {
                y: { display: false },
                x: { display: false }
              }
            }
          })
        }
      })
    }
    
    // Data Canvas Animation
    const animateDataCanvas = () => {
      if (!dataCanvas.value) return
      
      const ctx = dataCanvas.value.getContext('2d')
      const width = window.innerWidth
      const height = window.innerHeight
      
      dataCanvas.value.width = width
      dataCanvas.value.height = height
      
      const particles = []
      const particleCount = 50
      
      for (let i = 0; i < particleCount; i++) {
        particles.push({
          x: Math.random() * width,
          y: Math.random() * height,
          vx: (Math.random() - 0.5) * 0.5,
          vy: (Math.random() - 0.5) * 0.5,
          size: Math.random() * 3 + 1,
          connections: []
        })
      }
      
      const animate = () => {
        ctx.fillStyle = 'rgba(10, 10, 10, 0.05)'
        ctx.fillRect(0, 0, width, height)
        
        // Update and draw particles
        particles.forEach((particle, i) => {
          particle.x += particle.vx
          particle.y += particle.vy
          
          if (particle.x < 0 || particle.x > width) particle.vx *= -1
          if (particle.y < 0 || particle.y > height) particle.vy *= -1
          
          ctx.beginPath()
          ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
          ctx.fillStyle = 'rgba(0, 255, 255, 0.5)'
          ctx.fill()
          
          // Draw connections
          particles.slice(i + 1).forEach(other => {
            const distance = Math.sqrt(
              (particle.x - other.x) ** 2 + 
              (particle.y - other.y) ** 2
            )
            
            if (distance < 150) {
              ctx.beginPath()
              ctx.moveTo(particle.x, particle.y)
              ctx.lineTo(other.x, other.y)
              ctx.strokeStyle = `rgba(0, 255, 255, ${0.2 * (1 - distance / 150)})`
              ctx.stroke()
            }
          })
        })
        
        requestAnimationFrame(animate)
      }
      
      animate()
    }
    
    // Lifecycle
    onMounted(() => {
      animateDataCanvas()
      nextTick(() => {
        initCharts()
      })
    })
    
    onUnmounted(() => {
      // Cleanup charts
      Object.values(charts).forEach(chart => {
        if (chart) chart.destroy()
      })
    })
    
    // Watch for period changes
    watch(selectedPeriod, () => {
      // Update data based on selected period
      console.log('Period changed to:', selectedPeriod.value)
    })
    
    watch(progressChartView, () => {
      // Update chart type
      if (charts.progress) {
        const currentType = charts.progress.config.type
        const newType = progressChartView.value === 'Linha' ? 'line' : 
                       progressChartView.value === 'Barra' ? 'bar' : 'line'
        
        if (currentType !== newType) {
          charts.progress.config.type = newType
          if (newType === 'line') {
            charts.progress.config.data.datasets.forEach(dataset => {
              dataset.fill = true
            })
          } else {
            charts.progress.config.data.datasets.forEach(dataset => {
              dataset.fill = false
            })
          }
          charts.progress.update()
        }
      }
    })
    
    return {
      // Refs
      dataCanvas,
      progressChart,
      categoryChart,
      timeChart,
      
      // State
      selectedPeriod,
      progressChartView,
      activeStatsTab,
      tooltip,
      
      // Data
      timePeriods,
      keyMetrics,
      chartViews,
      topCategories,
      velocityScore,
      velocityProgress,
      velocityColor,
      velocityInsight,
      aiRecommendations,
      performanceTopics,
      statsTabs,
      contentStats,
      studyGoals,
      exportFormats,
      
      // Methods
      randomMetric,
      getHeatmapColor,
      getHourActivity,
      showTooltip,
      showTopicDetails,
      hideTooltip,
      applyRecommendation,
      toggleTask,
      exportReport
    }
  }
}
</script>

<style scoped>
.notes-insights {
  min-height: 100vh;
  background: var(--theme-background);
  position: relative;
  overflow-x: hidden;
}

/* Data Visualization Background */
.data-visualization-bg {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.data-canvas {
  width: 100%;
  height: 100%;
  opacity: 0.3;
}

.floating-metrics {
  position: absolute;
  inset: 0;
  overflow: hidden;
}

.floating-metric {
  position: absolute;
  padding: 0.5rem 1rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 20px;
  color: var(--theme-primary);
  font-weight: bold;
  font-size: 0.875rem;
  animation: float-metric 20s infinite linear;
}

@keyframes float-metric {
  from {
    transform: translateX(-100px);
  }
  to {
    transform: translateX(calc(100vw + 100px));
  }
}

/* Position metrics */
.floating-metric:nth-child(1) { top: 10%; animation-delay: 0s; }
.floating-metric:nth-child(2) { top: 15%; animation-delay: 1s; }
.floating-metric:nth-child(3) { top: 20%; animation-delay: 2s; }
.floating-metric:nth-child(4) { top: 25%; animation-delay: 3s; }
.floating-metric:nth-child(5) { top: 30%; animation-delay: 4s; }
.floating-metric:nth-child(6) { top: 35%; animation-delay: 5s; }
.floating-metric:nth-child(7) { top: 40%; animation-delay: 6s; }
.floating-metric:nth-child(8) { top: 45%; animation-delay: 7s; }
.floating-metric:nth-child(9) { top: 50%; animation-delay: 8s; }
.floating-metric:nth-child(10) { top: 55%; animation-delay: 9s; }
.floating-metric:nth-child(11) { top: 60%; animation-delay: 10s; }
.floating-metric:nth-child(12) { top: 65%; animation-delay: 11s; }
.floating-metric:nth-child(13) { top: 70%; animation-delay: 12s; }
.floating-metric:nth-child(14) { top: 75%; animation-delay: 13s; }
.floating-metric:nth-child(15) { top: 80%; animation-delay: 14s; }
.floating-metric:nth-child(16) { top: 85%; animation-delay: 15s; }
.floating-metric:nth-child(17) { top: 90%; animation-delay: 16s; }
.floating-metric:nth-child(18) { top: 95%; animation-delay: 17s; }
.floating-metric:nth-child(19) { top: 5%; animation-delay: 18s; }
.floating-metric:nth-child(20) { top: 12%; animation-delay: 19s; }

/* Header */
.insights-header {
  position: relative;
  z-index: 10;
  padding: 2rem;
  background: var(--theme-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--theme-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 2rem;
}

.nav-back {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  color: var(--theme-text);
  text-decoration: none;
  transition: all 0.3s ease;
}

.nav-back:hover {
  background: var(--theme-surfaceLight);
  transform: translateX(-5px);
}

.header-center {
  flex: 1;
  text-align: center;
}

.main-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--theme-text);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.title-visual {
  position: relative;
}

.title-icon {
  font-size: 2.5rem;
  color: var(--theme-primary);
}

.icon-pulse {
  position: absolute;
  inset: -15px;
  border: 2px solid var(--theme-primary);
  border-radius: 50%;
  animation: pulse-expand 2s ease-out infinite;
}

@keyframes pulse-expand {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

.subtitle {
  font-size: 1.125rem;
  color: var(--theme-textMuted);
}

.time-controls {
  display: flex;
  gap: 0.5rem;
  background: var(--theme-surface);
  padding: 0.25rem;
  border-radius: 12px;
  border: 1px solid var(--theme-border);
}

.time-btn {
  padding: 0.5rem 1rem;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: var(--theme-text);
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-btn:hover {
  background: var(--theme-glass);
}

.time-btn.active {
  background: var(--theme-primary);
  color: var(--theme-background);
}

/* Dashboard */
.insights-dashboard {
  position: relative;
  z-index: 10;
  max-width: 1600px;
  margin: 0 auto;
  padding: 2rem;
}

/* Metrics Row */
.metrics-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.metric-card {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 16px;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--metric-color);
}

.metric-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px var(--theme-shadow);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.metric-icon {
  width: 48px;
  height: 48px;
  background: var(--theme-glass);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--metric-color);
  font-size: 1.25rem;
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.metric-change.up {
  color: var(--theme-success);
}

.metric-change.down {
  color: var(--theme-danger);
}

.metric-value {
  font-size: 2rem;
  font-weight: bold;
  color: var(--theme-text);
  margin-bottom: 0.5rem;
}

.metric-label {
  font-size: 0.875rem;
  color: var(--theme-textMuted);
  margin-bottom: 1rem;
}

.metric-chart {
  height: 50px;
  margin: 0 -1.5rem -1.5rem;
}

/* Analytics Grid */
.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.analytics-card {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
}

.analytics-card.large {
  grid-column: span 2;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.card-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.125rem;
  color: var(--theme-text);
  margin: 0;
}

.card-header h3 svg {
  color: var(--theme-primary);
}

.card-actions {
  display: flex;
  gap: 0.5rem;
}

.view-btn {
  padding: 0.25rem 0.75rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 6px;
  color: var(--theme-text);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-btn:hover {
  background: var(--theme-glassHover);
}

.view-btn.active {
  background: var(--theme-primary);
  color: var(--theme-background);
  border-color: var(--theme-primary);
}

.chart-container {
  flex: 1;
  position: relative;
  min-height: 200px;
}

/* Heatmap */
.heatmap-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.heatmap-grid {
  display: flex;
  gap: 2px;
  overflow-x: auto;
  padding-bottom: 1rem;
}

.heatmap-week {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.heatmap-cell {
  width: 12px;
  height: 12px;
  background: var(--theme-glass);
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.heatmap-cell:hover {
  transform: scale(1.2);
  box-shadow: 0 0 10px var(--theme-primary);
}

.heatmap-legend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--theme-textMuted);
}

.legend-scale {
  display: flex;
  gap: 2px;
}

.scale-block {
  width: 12px;
  height: 12px;
  background: var(--theme-primary);
  border-radius: 2px;
}

/* Category Legend */
.category-legend {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 3px;
  flex-shrink: 0;
}

.legend-name {
  flex: 1;
  color: var(--theme-text);
}

.legend-value {
  color: var(--theme-textMuted);
  font-weight: 500;
}

/* Velocity Gauge */
.velocity-gauge {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.gauge-container {
  width: 100%;
  max-width: 200px;
}

.gauge-value {
  font-size: 2rem;
  font-weight: bold;
  fill: var(--theme-text);
}

.gauge-label {
  font-size: 0.875rem;
  fill: var(--theme-textMuted);
}

.velocity-insights {
  text-align: center;
  padding: 1rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
}

.velocity-insights p {
  color: var(--theme-text);
  font-size: 0.875rem;
  margin: 0;
}

/* AI Recommendations */
.ai-recommendations {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recommendation-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.recommendation-item:hover {
  background: var(--theme-glassHover);
  transform: translateX(-5px);
}

.rec-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  color: white;
  flex-shrink: 0;
}

.rec-content {
  flex: 1;
}

.rec-content h4 {
  color: var(--theme-text);
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.rec-content p {
  color: var(--theme-textMuted);
  font-size: 0.75rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.rec-btn {
  padding: 0.25rem 0.75rem;
  background: var(--theme-primary);
  border: none;
  border-radius: 6px;
  color: var(--theme-background);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.rec-btn:hover {
  transform: scale(1.05);
}

/* Performance Matrix */
.performance-matrix {
  position: relative;
  height: 400px;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
  overflow: hidden;
}

.matrix-axes {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
  padding: 1rem;
  pointer-events: none;
}

.y-label {
  position: absolute;
  left: -2rem;
  top: 50%;
  transform: rotate(-90deg);
  color: var(--theme-textMuted);
  font-size: 0.875rem;
}

.x-label {
  position: absolute;
  bottom: -1.5rem;
  left: 50%;
  transform: translateX(-50%);
  color: var(--theme-textMuted);
  font-size: 0.875rem;
}

.matrix-grid {
  position: relative;
  width: 100%;
  height: 100%;
}

.quadrant {
  position: absolute;
  width: 50%;
  height: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  color: var(--theme-textMuted);
  opacity: 0.3;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.quadrant.q1 {
  top: 0;
  right: 0;
  background: rgba(0, 255, 136, 0.05);
}

.quadrant.q2 {
  top: 0;
  left: 0;
  background: rgba(255, 255, 0, 0.05);
}

.quadrant.q3 {
  bottom: 0;
  left: 0;
  background: rgba(255, 136, 0, 0.05);
}

.quadrant.q4 {
  bottom: 0;
  right: 0;
  background: rgba(255, 0, 255, 0.05);
}

.matrix-point {
  position: absolute;
  width: 40px;
  height: 40px;
  background: var(--point-color);
  border: 2px solid white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.75rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  transform: translate(-50%, 50%);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.matrix-point:hover {
  transform: translate(-50%, 50%) scale(1.2);
  z-index: 10;
}

/* Detailed Stats */
.detailed-stats {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.5rem;
  color: var(--theme-text);
  margin-bottom: 1.5rem;
}

.stats-tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--theme-border);
}

.stats-tab {
  padding: 0.75rem 1.5rem;
  background: transparent;
  border: none;
  color: var(--theme-textMuted);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  position: relative;
}

.stats-tab:hover {
  color: var(--theme-text);
}

.stats-tab.active {
  color: var(--theme-primary);
}

.stats-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--theme-primary);
}

/* Content Stats */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: var(--theme-glassHover);
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  background: var(--theme-surface);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--theme-primary);
  font-size: 1.25rem;
  flex-shrink: 0;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--theme-text);
}

.stat-trend {
  font-size: 0.75rem;
  color: var(--theme-textMuted);
}

.stat-trend .up {
  color: var(--theme-success);
}

.stat-trend .down {
  color: var(--theme-danger);
}

/* Time Stats */
.time-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.time-breakdown h3,
.peak-hours h3 {
  font-size: 1.125rem;
  color: var(--theme-text);
  margin-bottom: 1rem;
}

.time-chart {
  height: 200px;
}

.hours-heatmap {
  display: flex;
  align-items: flex-end;
  gap: 4px;
  height: 150px;
  padding: 1rem 0;
}

.hour-bar {
  flex: 1;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
}

.bar-fill {
  width: 100%;
  background: var(--theme-primary);
  border-radius: 2px 2px 0 0;
  transition: height 0.3s ease;
}

.hour-label {
  position: absolute;
  bottom: -20px;
  font-size: 0.625rem;
  color: var(--theme-textMuted);
}

/* Goals Stats */
.goals-overview {
  display: grid;
  gap: 1.5rem;
}

.goal-card {
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
  padding: 1.5rem;
}

.goal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.goal-header h4 {
  color: var(--theme-text);
  margin: 0;
}

.goal-deadline {
  font-size: 0.875rem;
  color: var(--theme-textMuted);
}

.goal-progress {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: var(--theme-surface);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--theme-primary);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--theme-primary);
}

.goal-tasks {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--theme-text);
  font-size: 0.875rem;
}

.task-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

/* Export Section */
.export-section {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
}

.export-section h3 {
  color: var(--theme-text);
  margin-bottom: 1rem;
}

.export-options {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.export-btn {
  padding: 0.75rem 1.5rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  color: var(--theme-text);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.export-btn:hover {
  background: var(--theme-primary);
  color: var(--theme-background);
  border-color: var(--theme-primary);
  transform: translateY(-2px);
}

/* Tooltip */
.custom-tooltip {
  position: fixed;
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 5px 20px var(--theme-shadow);
  z-index: 1000;
  pointer-events: none;
  max-width: 250px;
}

.tooltip-content h4 {
  color: var(--theme-text);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.tooltip-content p {
  color: var(--theme-textMuted);
  font-size: 0.75rem;
  margin-bottom: 0.5rem;
}

.tooltip-stats {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.75rem;
}

.tooltip-stat {
  display: flex;
  justify-content: space-between;
}

.tooltip-stat .stat-label {
  color: var(--theme-textMuted);
}

.tooltip-stat .stat-value {
  color: var(--theme-primary);
  font-weight: 500;
}

/* Responsive */
@media (max-width: 1200px) {
  .analytics-card.large {
    grid-column: span 1;
  }
  
  .time-stats {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .insights-header {
    flex-direction: column;
    text-align: center;
  }
  
  .metrics-row {
    grid-template-columns: 1fr;
  }
  
  .analytics-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-tabs {
    overflow-x: auto;
  }
  
  .export-options {
    flex-wrap: wrap;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--theme-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--theme-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--theme-primary);
}
</style>