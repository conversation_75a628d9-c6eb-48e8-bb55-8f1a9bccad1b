<template>
  <div class="notes-templates">
    <!-- Background Effects -->
    <div class="templates-background">
      <div class="neural-network"></div>
      <div class="gradient-orbs"></div>
    </div>

    <!-- Header -->
    <div class="templates-header">
      <div class="header-content">
        <h1>
          <i class="fas fa-file-alt"></i>
          Templates de Notas
        </h1>
        <p>Modelos profissionais para acelerar sua documentação médica</p>
      </div>

      <!-- Search and Filter -->
      <div class="header-controls">
        <div class="search-box">
          <i class="fas fa-search"></i>
          <input v-model="searchQuery" 
                 placeholder="Buscar templates..." 
                 @input="handleSearch" />
        </div>

        <div class="filter-controls">
          <button v-for="category in categories" 
                  :key="category.id"
                  @click="activeCategory = category.id"
                  :class="{ active: activeCategory === category.id }"
                  class="category-btn">
            <i :class="`fas fa-${category.icon}`"></i>
            <span>{{ category.name }}</span>
            <span class="count">{{ category.count }}</span>
          </button>
        </div>
        
        <!-- Action Buttons -->
        <div class="action-buttons">
          <button class="action-btn ai-btn" @click="showAIGenerator = true">
            <i class="fas fa-robot"></i>
            <span>Gerar com IA</span>
          </button>
          <button class="action-btn marketplace-btn" @click="showMarketplace = true">
            <i class="fas fa-store"></i>
            <span>Marketplace</span>
          </button>
          <button class="action-btn import-btn" @click="handleImportClick">
            <i class="fas fa-upload"></i>
            <span>Importar</span>
          </button>
          <button class="action-btn export-btn" @click="handleExportClick">
            <i class="fas fa-download"></i>
            <span>Exportar</span>
          </button>
        </div>
        
        <!-- Sort Options -->
        <div class="sort-options">
          <label>Ordenar por:</label>
          <select v-model="sortBy" class="sort-select">
            <option value="popular">Mais Populares</option>
            <option value="recent">Mais Recentes</option>
            <option value="alphabetical">Alfabética</option>
            <option value="rating">Melhor Avaliado</option>
          </select>
        </div>

        <button @click="showCreateModal = true" class="create-template-btn">
          <i class="fas fa-plus"></i>
          <span>Criar Template</span>
        </button>
      </div>
    </div>

    <!-- Templates Grid -->
    <div class="templates-container">
      <div class="templates-grid">
        <div v-for="template in sortedTemplates" 
             :key="template.id"
             class="template-card"
             @click="selectTemplate(template)">
          
          <!-- Template Header -->
          <div class="template-header">
            <div class="template-icon" :style="{ background: template.color }">
              <i :class="`fas fa-${template.icon}`"></i>
            </div>
            <div class="template-meta">
              <span class="template-category">{{ template.category }}</span>
              <span class="template-usage">
                <i class="fas fa-fire"></i>
                {{ template.usage }} usos
              </span>
            </div>
          </div>

          <!-- Template Content -->
          <div class="template-content">
            <h3>{{ template.name }}</h3>
            <p>{{ template.description }}</p>

            <!-- Template Fields Preview -->
            <div class="fields-preview">
              <div v-for="(field, index) in template.fields.slice(0, 3)" 
                   :key="index"
                   class="field-tag">
                <i class="fas fa-tag"></i>
                {{ field.label }}
              </div>
              <span v-if="template.fields.length > 3" class="more-fields">
                +{{ template.fields.length - 3 }} campos
              </span>
            </div>
          </div>

          <!-- Template Actions -->
          <div class="template-actions">
            <button @click.stop="useTemplate(template)" class="use-btn">
              <i class="fas fa-arrow-right"></i>
              Usar Template
            </button>
            <button @click.stop="toggleFavorite(template)" 
                    class="favorite-btn"
                    :class="{ active: template.isFavorite }">
              <i :class="`${template.isFavorite ? 'fas' : 'far'} fa-star`"></i>
            </button>
            <button @click.stop="showTemplateMenu(template, $event)" class="menu-btn">
              <i class="fas fa-ellipsis-v"></i>
            </button>
          </div>

          <!-- Template Stats -->
          <div class="template-stats">
            <div class="stat">
              <i class="fas fa-clock"></i>
              <span>{{ template.estimatedTime }}</span>
            </div>
            <div class="stat">
              <i class="fas fa-layer-group"></i>
              <span>{{ template.sections }} seções</span>
            </div>
            <div class="stat">
              <i class="fas fa-robot"></i>
              <span>IA Enhanced</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="filteredTemplates.length === 0" class="empty-state">
        <div class="empty-illustration">
          <i class="fas fa-file-excel"></i>
        </div>
        <h3>Nenhum template encontrado</h3>
        <p>Tente ajustar seus filtros ou criar um novo template</p>
        <button @click="showCreateModal = true" class="create-btn">
          <i class="fas fa-plus"></i>
          Criar Novo Template
        </button>
      </div>
    </div>

    <!-- Template Preview Modal -->
    <transition name="modal-fade">
      <div v-if="selectedTemplate" class="template-modal" @click.self="closePreview">
        <div class="modal-content">
          <div class="modal-header">
            <div class="modal-title">
              <div class="template-icon" :style="{ background: selectedTemplate.color }">
                <i :class="`fas fa-${selectedTemplate.icon}`"></i>
              </div>
              <div>
                <h2>{{ selectedTemplate.name }}</h2>
                <p>{{ selectedTemplate.category }} • {{ selectedTemplate.estimatedTime }}</p>
              </div>
            </div>
            <button @click="closePreview" class="close-btn">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <div class="modal-body">
            <!-- Template Sections -->
            <div class="template-sections">
              <div v-for="section in selectedTemplate.sections" 
                   :key="section.id"
                   class="template-section">
                <h3>
                  <i :class="`fas fa-${section.icon}`"></i>
                  {{ section.title }}
                </h3>
                <p>{{ section.description }}</p>

                <!-- Section Fields -->
                <div class="section-fields">
                  <div v-for="field in section.fields" 
                       :key="field.id"
                       class="field-item">
                    <label>{{ field.label }}</label>
                    <div class="field-preview">
                      <span class="field-type">{{ field.type }}</span>
                      <span v-if="field.required" class="field-required">Obrigatório</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Template Features -->
            <div class="template-features">
              <h3>Recursos do Template</h3>
              <div class="features-grid">
                <div class="feature">
                  <i class="fas fa-brain"></i>
                  <span>Sugestões IA</span>
                </div>
                <div class="feature">
                  <i class="fas fa-spell-check"></i>
                  <span>Auto-correção</span>
                </div>
                <div class="feature">
                  <i class="fas fa-language"></i>
                  <span>Multi-idioma</span>
                </div>
                <div class="feature">
                  <i class="fas fa-file-export"></i>
                  <span>Exportação PDF</span>
                </div>
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <button @click="editTemplate(selectedTemplate)" class="edit-btn">
              <i class="fas fa-edit"></i>
              Editar Template
            </button>
            <button @click="duplicateTemplate(selectedTemplate)" class="duplicate-btn">
              <i class="fas fa-copy"></i>
              Duplicar
            </button>
            <button @click="useTemplate(selectedTemplate)" class="primary-btn">
              <i class="fas fa-arrow-right"></i>
              Usar Este Template
            </button>
          </div>
        </div>
      </div>
    </transition>

    <!-- Create Template Modal -->
    <transition name="modal-fade">
      <div v-if="showCreateModal" class="create-modal" @click.self="showCreateModal = false">
        <div class="modal-content">
          <div class="modal-header">
            <h2>
              <i class="fas fa-plus-circle"></i>
              Criar Novo Template
            </h2>
            <button @click="showCreateModal = false" class="close-btn">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <div class="modal-body">
            <form @submit.prevent="createTemplate" class="template-form">
              <!-- Basic Info -->
              <div class="form-section">
                <h3>Informações Básicas</h3>
                
                <div class="form-group">
                  <label>Nome do Template</label>
                  <input v-model="newTemplate.name" 
                         type="text" 
                         placeholder="Ex: Consulta de Rotina"
                         required />
                </div>

                <div class="form-group">
                  <label>Descrição</label>
                  <textarea v-model="newTemplate.description" 
                            placeholder="Descreva o propósito deste template..."
                            rows="3"></textarea>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <label>Categoria</label>
                    <select v-model="newTemplate.category" required>
                      <option value="">Selecione...</option>
                      <option value="consulta">Consulta</option>
                      <option value="procedimento">Procedimento</option>
                      <option value="laudo">Laudo</option>
                      <option value="prescricao">Prescrição</option>
                    </select>
                  </div>

                  <div class="form-group">
                    <label>Ícone</label>
                    <div class="icon-picker">
                      <button type="button" 
                              v-for="icon in availableIcons" 
                              :key="icon"
                              @click="newTemplate.icon = icon"
                              :class="{ active: newTemplate.icon === icon }"
                              class="icon-option">
                        <i :class="`fas fa-${icon}`"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Template Sections -->
              <div class="form-section">
                <h3>Seções do Template</h3>
                
                <div v-for="(section, index) in newTemplate.sections" 
                     :key="index"
                     class="section-builder">
                  <div class="section-header">
                    <input v-model="section.title" 
                           placeholder="Título da seção"
                           class="section-title-input" />
                    <button type="button" 
                            @click="removeSection(index)"
                            class="remove-section-btn">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>

                  <!-- Section Fields -->
                  <div class="fields-builder">
                    <div v-for="(field, fieldIndex) in section.fields" 
                         :key="fieldIndex"
                         class="field-builder">
                      <input v-model="field.label" 
                             placeholder="Nome do campo" />
                      <select v-model="field.type">
                        <option value="text">Texto</option>
                        <option value="textarea">Área de Texto</option>
                        <option value="number">Número</option>
                        <option value="date">Data</option>
                        <option value="select">Seleção</option>
                        <option value="checkbox">Checkbox</option>
                      </select>
                      <label class="required-checkbox">
                        <input type="checkbox" v-model="field.required" />
                        Obrigatório
                      </label>
                      <button type="button" 
                              @click="removeField(index, fieldIndex)"
                              class="remove-field-btn">
                        <i class="fas fa-times"></i>
                      </button>
                    </div>
                    
                    <button type="button" 
                            @click="addField(index)"
                            class="add-field-btn">
                      <i class="fas fa-plus"></i>
                      Adicionar Campo
                    </button>
                  </div>
                </div>

                <button type="button" 
                        @click="addSection"
                        class="add-section-btn">
                  <i class="fas fa-plus"></i>
                  Adicionar Seção
                </button>
              </div>

              <!-- Form Actions -->
              <div class="form-actions">
                <button type="button" 
                        @click="showCreateModal = false"
                        class="cancel-btn">
                  Cancelar
                </button>
                <button type="submit" class="save-btn">
                  <i class="fas fa-save"></i>
                  Criar Template
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </transition>

    <!-- Quick Actions Menu -->
    <transition name="menu-fade">
      <div v-if="quickMenuTemplate" 
           class="quick-menu"
           :style="quickMenuStyle"
           @click.stop>
        <button @click="editTemplate(quickMenuTemplate)" class="menu-item">
          <i class="fas fa-edit"></i>
          Editar
        </button>
        <button @click="duplicateTemplate(quickMenuTemplate)" class="menu-item">
          <i class="fas fa-copy"></i>
          Duplicar
        </button>
        <button @click="shareTemplate(quickMenuTemplate)" class="menu-item">
          <i class="fas fa-share"></i>
          Compartilhar
        </button>
        <div class="menu-divider"></div>
        <button @click="deleteTemplate(quickMenuTemplate)" class="menu-item danger">
          <i class="fas fa-trash"></i>
          Excluir
        </button>
      </div>
    </transition>
    
    <!-- AI Generator Modal -->
    <transition name="modal-fade">
      <div v-if="showAIGenerator" class="modal-overlay" @click.self="showAIGenerator = false">
        <div class="modal-content ai-generator-modal">
          <div class="modal-header">
            <h2>
              <i class="fas fa-robot"></i>
              Gerador de Templates com IA
            </h2>
            <button class="close-btn" @click="showAIGenerator = false">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <div class="modal-body">
            <div class="generator-section">
              <h3>Descreva o template desejado</h3>
              <textarea 
                v-model="aiPrompt"
                placeholder="Ex: Template para resumo de aula de anatomia com seções para sistemas, funções e ilustrações..."
                class="ai-prompt-input"
                rows="4"
              ></textarea>
            </div>
            
            <div class="generator-options">
              <h3>Opções de Geração</h3>
              <div class="option-grid">
                <label class="option-item">
                  <input type="checkbox" v-model="aiOptions.includeImages">
                  <span>Incluir espaços para imagens</span>
                </label>
                <label class="option-item">
                  <input type="checkbox" v-model="aiOptions.includeCharts">
                  <span>Incluir gráficos e diagramas</span>
                </label>
                <label class="option-item">
                  <input type="checkbox" v-model="aiOptions.includeQuestions">
                  <span>Incluir seção de questões</span>
                </label>
                <label class="option-item">
                  <input type="checkbox" v-model="aiOptions.includeReferences">
                  <span>Incluir referências bibliográficas</span>
                </label>
              </div>
            </div>
            
            <div class="generator-style">
              <h3>Estilo do Template</h3>
              <div class="style-options">
                <button 
                  v-for="style in templateStyles" 
                  :key="style.id"
                  class="style-btn"
                  :class="{ active: selectedStyle === style.id }"
                  @click="selectedStyle = style.id"
                >
                  <i :class="`fas fa-${style.icon}`"></i>
                  <span>{{ style.name }}</span>
                </button>
              </div>
            </div>
            
            <div class="generator-preview" v-if="generatedTemplate">
              <h3>Preview do Template</h3>
              <div class="preview-content">
                <div v-html="generatedTemplate.preview"></div>
              </div>
            </div>
          </div>
          
          <div class="modal-footer">
            <button class="cancel-btn" @click="showAIGenerator = false">Cancelar</button>
            <button class="primary-btn ai-generate-btn" @click="generateAITemplate" :disabled="!aiPrompt || isGeneratingAI">
              <i class="fas" :class="isGeneratingAI ? 'fa-spinner fa-spin' : 'fa-magic'"></i>
              <span>{{ isGeneratingAI ? 'Gerando...' : 'Gerar Template' }}</span>
            </button>
          </div>
        </div>
      </div>
    </transition>
    
    <!-- Marketplace Modal -->
    <transition name="modal-fade">
      <div v-if="showMarketplace" class="modal-overlay" @click.self="showMarketplace = false">
        <div class="modal-content marketplace-modal">
          <div class="modal-header">
            <h2>
              <i class="fas fa-store"></i>
              Marketplace de Templates
            </h2>
            <button class="close-btn" @click="showMarketplace = false">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <div class="modal-body">
            <div class="marketplace-stats">
              <div class="stat-card">
                <i class="fas fa-download"></i>
                <div class="stat-info">
                  <span class="stat-value">10K+</span>
                  <span class="stat-label">Templates</span>
                </div>
              </div>
              <div class="stat-card">
                <i class="fas fa-users"></i>
                <div class="stat-info">
                  <span class="stat-value">5K+</span>
                  <span class="stat-label">Criadores</span>
                </div>
              </div>
              <div class="stat-card">
                <i class="fas fa-star"></i>
                <div class="stat-info">
                  <span class="stat-value">4.8</span>
                  <span class="stat-label">Avaliação Média</span>
                </div>
              </div>
            </div>
            
            <div class="marketplace-categories">
              <h3>Categorias Populares</h3>
              <div class="category-grid">
                <div v-for="cat in marketplaceCategories" :key="cat.id" class="marketplace-category">
                  <i :class="`fas fa-${cat.icon}`"></i>
                  <span>{{ cat.name }}</span>
                  <span class="count">{{ cat.count }}</span>
                </div>
              </div>
            </div>
            
            <div class="marketplace-featured">
              <h3>Templates em Destaque</h3>
              <div class="featured-grid">
                <div v-for="template in featuredTemplates" :key="template.id" class="featured-template">
                  <div class="template-preview">
                    <div class="template-icon-preview" :style="{ background: template.color }">
                      <i :class="`fas fa-${template.icon}`"></i>
                    </div>
                  </div>
                  <div class="template-info">
                    <h4>{{ template.name }}</h4>
                    <div class="template-meta">
                      <span class="author">
                        <i class="fas fa-user"></i>
                        {{ template.author }}
                      </span>
                      <span class="rating">
                        <i class="fas fa-star"></i>
                        {{ template.rating }}
                      </span>
                      <span class="downloads">
                        <i class="fas fa-download"></i>
                        {{ template.downloads }}
                      </span>
                    </div>
                    <div class="template-price">
                      <span v-if="template.price === 0" class="free">Grátis</span>
                      <span v-else class="price">R$ {{ template.price }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="modal-footer">
            <button class="cancel-btn" @click="showMarketplace = false">Fechar</button>
            <button class="primary-btn" @click="goToMarketplace">
              <i class="fas fa-external-link-alt"></i>
              <span>Ver Todos os Templates</span>
            </button>
          </div>
        </div>
      </div>
    </transition>
    
    <!-- Version History Modal -->
    <transition name="modal-fade">
      <div v-if="showVersionHistory && selectedTemplate" class="modal-overlay" @click.self="showVersionHistory = false">
        <div class="modal-content version-history-modal">
          <div class="modal-header">
            <h2>
              <i class="fas fa-history"></i>
              Histórico de Versões - {{ selectedTemplate.name }}
            </h2>
            <button class="close-btn" @click="showVersionHistory = false">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <div class="modal-body">
            <div class="version-timeline">
              <div v-for="version in templateVersions" :key="version.id" class="version-item">
                <div class="version-marker"></div>
                <div class="version-content">
                  <div class="version-header">
                    <h4>Versão {{ version.version }}</h4>
                    <span class="version-date">{{ formatDate(version.date) }}</span>
                  </div>
                  <p class="version-description">{{ version.description }}</p>
                  <div class="version-changes">
                    <span v-for="change in version.changes" :key="change" class="change-tag">
                      {{ change }}
                    </span>
                  </div>
                  <div class="version-actions">
                    <button class="btn-small" @click="previewVersion(version)">
                      <i class="fas fa-eye"></i>
                      Preview
                    </button>
                    <button class="btn-small" @click="restoreVersion(version)">
                      <i class="fas fa-undo"></i>
                      Restaurar
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="modal-footer">
            <button class="cancel-btn" @click="showVersionHistory = false">Fechar</button>
          </div>
        </div>
      </div>
    </transition>
    
    <!-- Hidden File Input -->
    <input 
      ref="fileInput"
      type="file"
      accept=".json"
      style="display: none"
      @change="importTemplate"
    >
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

export default {
  name: 'NotesTemplates',
  
  setup() {
    const store = useStore()
    const router = useRouter()
    
    // State
    const searchQuery = ref('')
    const activeCategory = ref('all')
    const selectedTemplate = ref(null)
    const showCreateModal = ref(false)
    const showMarketplace = ref(false)
    const showAIGenerator = ref(false)
    const showVersionHistory = ref(false)
    const quickMenuTemplate = ref(null)
    const quickMenuStyle = ref({})
    const sortBy = ref('popular')
    const viewMode = ref('grid')
    const isGeneratingAI = ref(false)
    const aiPrompt = ref('')
    const selectedStyle = ref('academic')
    const generatedTemplate = ref(null)
    const fileInput = ref(null)
    
    // AI Options
    const aiOptions = reactive({
      includeImages: true,
      includeCharts: false,
      includeQuestions: true,
      includeReferences: true
    })
    
    // Template Styles
    const templateStyles = ref([
      { id: 'academic', name: 'Acadêmico', icon: 'graduation-cap' },
      { id: 'clinical', name: 'Clínico', icon: 'stethoscope' },
      { id: 'research', name: 'Pesquisa', icon: 'microscope' },
      { id: 'minimal', name: 'Minimalista', icon: 'minus' }
    ])
    
    // Marketplace Categories
    const marketplaceCategories = ref([
      { id: 'cardiology', name: 'Cardiologia', icon: 'heartbeat', count: 245 },
      { id: 'neurology', name: 'Neurologia', icon: 'brain', count: 189 },
      { id: 'pediatrics', name: 'Pediatria', icon: 'baby', count: 156 },
      { id: 'surgery', name: 'Cirurgia', icon: 'procedures', count: 201 }
    ])
    
    // Featured Templates
    const featuredTemplates = ref([
      {
        id: 101,
        name: 'Anamnese Completa 2024',
        author: 'Dr. Silva',
        rating: 4.9,
        downloads: '2.5K',
        price: 0,
        icon: 'stethoscope',
        color: '#4F46E5'
      },
      {
        id: 102,
        name: 'Laudo Radiológico Avançado',
        author: 'Dra. Santos',
        rating: 4.8,
        downloads: '1.8K',
        price: 29.90,
        icon: 'x-ray',
        color: '#10B981'
      },
      {
        id: 103,
        name: 'Protocolo Cirúrgico',
        author: 'Dr. Oliveira',
        rating: 4.7,
        downloads: '1.2K',
        price: 49.90,
        icon: 'procedures',
        color: '#F59E0B'
      }
    ])
    
    // Template Versions
    const templateVersions = ref([
      {
        id: 1,
        version: '2.1',
        date: new Date('2024-03-15'),
        description: 'Adicionado suporte para múltiplos idiomas e melhorias na seção de diagnóstico',
        changes: ['Multi-idioma', 'Diagnóstico expandido', 'Correções']
      },
      {
        id: 2,
        version: '2.0',
        date: new Date('2024-02-01'),
        description: 'Grande atualização com novo design e campos adicionais',
        changes: ['Novo design', 'Campos extras', 'Performance']
      },
      {
        id: 3,
        version: '1.0',
        date: new Date('2023-12-01'),
        description: 'Versão inicial do template',
        changes: ['Lançamento inicial']
      }
    ])
    
    // Categories
    const categories = ref([
      { id: 'all', name: 'Todos', icon: 'globe', count: 24 },
      { id: 'consulta', name: 'Consultas', icon: 'stethoscope', count: 8 },
      { id: 'procedimento', name: 'Procedimentos', icon: 'procedures', count: 6 },
      { id: 'laudo', name: 'Laudos', icon: 'file-medical', count: 5 },
      { id: 'prescricao', name: 'Prescrições', icon: 'prescription', count: 5 },
      { id: 'pesquisa', name: 'Pesquisa', icon: 'microscope', count: 3 },
      { id: 'educacional', name: 'Educacional', icon: 'graduation-cap', count: 4 }
    ])
    
    // Templates Data
    const templates = ref([
      {
        id: 1,
        name: 'Consulta de Rotina',
        description: 'Template completo para consultas médicas de rotina com anamnese detalhada',
        category: 'consulta',
        icon: 'stethoscope',
        color: '#4F46E5',
        usage: 1250,
        estimatedTime: '15-20 min',
        sections: 3,
        isFavorite: true,
        fields: [
          { id: 1, label: 'Queixa Principal', type: 'textarea', required: true },
          { id: 2, label: 'História da Doença Atual', type: 'textarea', required: true },
          { id: 3, label: 'Antecedentes', type: 'textarea', required: false },
          { id: 4, label: 'Exame Físico', type: 'textarea', required: true }
        ],
        sections: [
          {
            id: 1,
            title: 'Anamnese',
            icon: 'clipboard-list',
            description: 'Coleta detalhada do histórico do paciente',
            fields: [
              { id: 1, label: 'Queixa Principal', type: 'textarea', required: true },
              { id: 2, label: 'História da Doença Atual', type: 'textarea', required: true }
            ]
          },
          {
            id: 2,
            title: 'Exame Físico',
            icon: 'user-md',
            description: 'Avaliação física completa',
            fields: [
              { id: 3, label: 'Sinais Vitais', type: 'text', required: true },
              { id: 4, label: 'Exame Geral', type: 'textarea', required: true }
            ]
          }
        ]
      },
      {
        id: 2,
        name: 'Laudo de Exame',
        description: 'Modelo estruturado para laudos de exames de imagem',
        category: 'laudo',
        icon: 'x-ray',
        color: '#10B981',
        usage: 890,
        estimatedTime: '10-15 min',
        sections: 4,
        isFavorite: false,
        fields: [
          { id: 1, label: 'Indicação Clínica', type: 'text', required: true },
          { id: 2, label: 'Técnica', type: 'textarea', required: true },
          { id: 3, label: 'Achados', type: 'textarea', required: true }
        ]
      },
      {
        id: 3,
        name: 'Prescrição Médica',
        description: 'Template para prescrições com cálculo automático de doses',
        category: 'prescricao',
        icon: 'prescription-bottle',
        color: '#F59E0B',
        usage: 2100,
        estimatedTime: '5-10 min',
        sections: 2,
        isFavorite: true,
        fields: [
          { id: 1, label: 'Medicamento', type: 'text', required: true },
          { id: 2, label: 'Dose', type: 'text', required: true },
          { id: 3, label: 'Frequência', type: 'select', required: true }
        ]
      }
    ])
    
    // New Template Form
    const newTemplate = reactive({
      name: '',
      description: '',
      category: '',
      icon: 'file-alt',
      color: '#4F46E5',
      sections: [
        {
          title: '',
          fields: [
            { label: '', type: 'text', required: false }
          ]
        }
      ]
    })
    
    // Available Icons
    const availableIcons = ref([
      'file-alt', 'stethoscope', 'clipboard-list', 'user-md',
      'prescription', 'vials', 'procedures', 'x-ray',
      'notes-medical', 'file-medical', 'heartbeat', 'hospital'
    ])
    
    // Computed
    const filteredTemplates = computed(() => {
      let filtered = templates.value
      
      // Filter by category
      if (activeCategory.value !== 'all') {
        filtered = filtered.filter(t => t.category === activeCategory.value)
      }
      
      // Filter by search
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(t => 
          t.name.toLowerCase().includes(query) ||
          t.description.toLowerCase().includes(query)
        )
      }
      
      return filtered
    })
    
    // Methods
    const handleSearch = () => {
      // Search handled by computed property
    }
    
    const selectTemplate = (template) => {
      selectedTemplate.value = template
    }
    
    const closePreview = () => {
      selectedTemplate.value = null
    }
    
    const useTemplate = (template) => {
      // Navigate to editor with template
      router.push({
        name: 'notes-editor',
        params: { templateId: template.id }
      })
    }
    
    const toggleFavorite = (template) => {
      template.isFavorite = !template.isFavorite
    }
    
    const showTemplateMenu = (template, event) => {
      const rect = event.target.getBoundingClientRect()
      quickMenuTemplate.value = template
      quickMenuStyle.value = {
        top: `${rect.bottom + 5}px`,
        left: `${rect.left}px`
      }
    }
    
    const editTemplate = (template) => {
      console.log('Edit template:', template)
      quickMenuTemplate.value = null
    }
    
    const duplicateTemplate = (template) => {
      const duplicate = {
        ...template,
        id: Date.now(),
        name: `${template.name} (Cópia)`,
        usage: 0,
        isFavorite: false
      }
      templates.value.push(duplicate)
      quickMenuTemplate.value = null
    }
    
    const shareTemplate = (template) => {
      console.log('Share template:', template)
      quickMenuTemplate.value = null
    }
    
    const deleteTemplate = (template) => {
      if (confirm(`Tem certeza que deseja excluir o template "${template.name}"?`)) {
        const index = templates.value.findIndex(t => t.id === template.id)
        if (index > -1) {
          templates.value.splice(index, 1)
        }
      }
      quickMenuTemplate.value = null
    }
    
    const createTemplate = () => {
      const template = {
        id: Date.now(),
        ...newTemplate,
        usage: 0,
        estimatedTime: '10-15 min',
        sections: newTemplate.sections.length,
        isFavorite: false,
        fields: newTemplate.sections.flatMap(s => s.fields)
      }
      
      templates.value.push(template)
      showCreateModal.value = false
      
      // Reset form
      Object.assign(newTemplate, {
        name: '',
        description: '',
        category: '',
        icon: 'file-alt',
        sections: [{ title: '', fields: [{ label: '', type: 'text', required: false }] }]
      })
    }
    
    const addSection = () => {
      newTemplate.sections.push({
        title: '',
        fields: [{ label: '', type: 'text', required: false }]
      })
    }
    
    const removeSection = (index) => {
      if (newTemplate.sections.length > 1) {
        newTemplate.sections.splice(index, 1)
      }
    }
    
    const addField = (sectionIndex) => {
      newTemplate.sections[sectionIndex].fields.push({
        label: '',
        type: 'text',
        required: false
      })
    }
    
    const removeField = (sectionIndex, fieldIndex) => {
      if (newTemplate.sections[sectionIndex].fields.length > 1) {
        newTemplate.sections[sectionIndex].fields.splice(fieldIndex, 1)
      }
    }
    
    // Click outside handler
    const handleClickOutside = (event) => {
      if (quickMenuTemplate.value && !event.target.closest('.quick-menu')) {
        quickMenuTemplate.value = null
      }
    }
    
    // Lifecycle
    onMounted(() => {
      document.addEventListener('click', handleClickOutside)
    })
    
    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside)
    })
    
    // AI Template Generation
    const generateAITemplate = async () => {
      if (!aiPrompt.value.trim()) return
      
      isGeneratingAI.value = true
      generatedTemplate.value = null
      
      // Simulate AI generation
      setTimeout(() => {
        const aiTemplate = {
          id: Date.now(),
          name: `Template IA - ${aiPrompt.value.substring(0, 30)}...`,
          description: `Template gerado por IA baseado em: ${aiPrompt.value}`,
          category: 'consulta',
          icon: 'robot',
          color: '#8B5CF6',
          usage: 0,
          estimatedTime: '10-15 min',
          sections: 3,
          isFavorite: false,
          isAIGenerated: true,
          fields: [
            { id: 1, label: 'Campo Gerado 1', type: 'textarea', required: true },
            { id: 2, label: 'Campo Gerado 2', type: 'text', required: false }
          ],
          preview: '<div class="preview"><h3>Template Gerado</h3><p>Preview do template...</p></div>'
        }
        
        generatedTemplate.value = aiTemplate
        
        // Add to templates after preview
        setTimeout(() => {
          templates.value.unshift(aiTemplate)
          isGeneratingAI.value = false
          showAIGenerator.value = false
          aiPrompt.value = ''
          generatedTemplate.value = null
        }, 1000)
      }, 2000)
    }
    
    // Template Version Control
    const saveTemplateVersion = (template) => {
      const version = {
        id: Date.now(),
        templateId: template.id,
        version: '1.0',
        changes: 'Versão inicial',
        author: 'Usuário',
        date: new Date()
      }
      console.log('Saving version:', version)
    }
    
    // Import/Export Templates
    const handleExportClick = () => {
      if (selectedTemplate.value) {
        exportTemplate(selectedTemplate.value)
      } else if (templates.value.length > 0) {
        exportTemplate(templates.value[0])
      }
    }
    
    const exportTemplate = (template) => {
      const data = JSON.stringify(template, null, 2)
      const blob = new Blob([data], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${template.name.replace(/\s+/g, '_')}.json`
      a.click()
    }
    
    const handleImportClick = () => {
      fileInput.value?.click()
    }
    
    const importTemplate = (event) => {
      const file = event.target.files[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            const template = JSON.parse(e.target.result)
            template.id = Date.now()
            templates.value.push(template)
          } catch (error) {
            console.error('Error importing template:', error)
          }
        }
        reader.readAsText(file)
      }
      // Reset input
      event.target.value = ''
    }
    
    // Additional methods
    const goToMarketplace = () => {
      console.log('Navigate to marketplace')
      // Implementation for marketplace navigation
    }
    
    const formatDate = (date) => {
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: 'long',
        year: 'numeric'
      }).format(date)
    }
    
    const previewVersion = (version) => {
      console.log('Preview version:', version)
    }
    
    const restoreVersion = (version) => {
      console.log('Restore version:', version)
    }
    
    // Sort Templates
    const sortTemplates = () => {
      switch (sortBy.value) {
        case 'popular':
          return [...filteredTemplates.value].sort((a, b) => b.usage - a.usage)
        case 'recent':
          return [...filteredTemplates.value].sort((a, b) => b.id - a.id)
        case 'alphabetical':
          return [...filteredTemplates.value].sort((a, b) => a.name.localeCompare(b.name))
        default:
          return filteredTemplates.value
      }
    }
    
    const sortedTemplates = computed(() => sortTemplates())
    
    // Show version history for a template
    const showTemplateVersionHistory = (template) => {
      selectedTemplate.value = template
      showVersionHistory.value = true
    }
    
    return {
      // State
      searchQuery,
      activeCategory,
      selectedTemplate,
      showCreateModal,
      showMarketplace,
      showAIGenerator,
      showVersionHistory,
      quickMenuTemplate,
      quickMenuStyle,
      newTemplate,
      sortBy,
      viewMode,
      isGeneratingAI,
      aiPrompt,
      selectedStyle,
      generatedTemplate,
      aiOptions,
      templateStyles,
      marketplaceCategories,
      featuredTemplates,
      templateVersions,
      fileInput,
      
      // Data
      categories,
      templates,
      availableIcons,
      
      // Computed
      filteredTemplates,
      sortedTemplates,
      
      // Methods
      handleSearch,
      selectTemplate,
      closePreview,
      useTemplate,
      toggleFavorite,
      showTemplateMenu,
      editTemplate,
      duplicateTemplate,
      shareTemplate,
      deleteTemplate,
      createTemplate,
      addSection,
      removeSection,
      addField,
      removeField,
      generateAITemplate,
      saveTemplateVersion,
      exportTemplate,
      importTemplate,
      handleExportClick,
      handleImportClick,
      goToMarketplace,
      formatDate,
      previewVersion,
      restoreVersion,
      showTemplateVersionHistory
    }
  }
}
</script>

<style scoped>
.notes-templates {
  min-height: 100vh;
  background: var(--theme-background);
  position: relative;
  overflow: hidden;
}

/* Background Effects */
.templates-background {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.neural-network {
  position: absolute;
  inset: 0;
  background-image: 
    radial-gradient(circle at 20% 50%, var(--theme-primary) 1px, transparent 1px),
    radial-gradient(circle at 80% 80%, var(--theme-secondary) 1px, transparent 1px);
  background-size: 50px 50px, 80px 80px;
  opacity: 0.1;
  animation: drift 20s ease-in-out infinite;
}

@keyframes drift {
  0%, 100% { transform: translate(0, 0); }
  50% { transform: translate(-20px, -20px); }
}

.gradient-orbs {
  position: absolute;
  inset: 0;
  background: 
    radial-gradient(circle at 10% 20%, var(--theme-primary) 0%, transparent 40%),
    radial-gradient(circle at 80% 80%, var(--theme-secondary) 0%, transparent 40%),
    radial-gradient(circle at 40% 60%, var(--theme-accent) 0%, transparent 40%);
  opacity: 0.05;
  filter: blur(80px);
}

/* Header */
.templates-header {
  position: relative;
  z-index: 10;
  padding: 2rem;
  background: var(--theme-glass);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--theme-border);
}

.header-content {
  text-align: center;
  margin-bottom: 2rem;
}

.header-content h1 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--theme-text);
  margin-bottom: 0.5rem;
}

.header-content p {
  font-size: 1.125rem;
  color: var(--theme-textMuted);
}

/* Header Controls */
.header-controls {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.search-box {
  flex: 1;
  position: relative;
}

.search-box i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--theme-textMuted);
}

.search-box input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
  color: var(--theme-text);
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
}

.search-box input:focus {
  border-color: var(--theme-primary);
  transform: translateY(-2px);
  box-shadow: 0 5px 20px var(--theme-shadow);
}

.filter-controls {
  display: flex;
  gap: 0.5rem;
  padding: 0.25rem;
  background: var(--theme-surface);
  border-radius: 12px;
}

.category-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: transparent;
  border: none;
  color: var(--theme-text);
  font-size: 0.875rem;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.category-btn:hover {
  background: var(--theme-glass);
}

.category-btn.active {
  background: var(--theme-primary);
  color: var(--theme-background);
}

.category-btn .count {
  padding: 0.125rem 0.5rem;
  background: var(--theme-glass);
  border-radius: 12px;
  font-size: 0.75rem;
}

.category-btn.active .count {
  background: rgba(255, 255, 255, 0.2);
}

.create-template-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--theme-success);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.create-template-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(34, 197, 94, 0.4);
}

/* Templates Container */
.templates-container {
  position: relative;
  z-index: 10;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* Template Card */
.template-card {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.template-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--theme-primary), var(--theme-secondary));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.template-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px var(--theme-shadow);
}

.template-card:hover::before {
  transform: scaleX(1);
}

.template-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.template-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  color: white;
  font-size: 1.25rem;
}

.template-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.875rem;
  color: var(--theme-textMuted);
}

.template-usage {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--theme-warning);
}

.template-content h3 {
  color: var(--theme-text);
  margin-bottom: 0.5rem;
}

.template-content p {
  color: var(--theme-textMuted);
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.fields-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.field-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 20px;
  font-size: 0.75rem;
  color: var(--theme-text);
}

.more-fields {
  padding: 0.25rem 0.75rem;
  background: var(--theme-primary);
  color: var(--theme-background);
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
}

.template-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.use-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--theme-primary);
  border: none;
  border-radius: 8px;
  color: var(--theme-background);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.use-btn:hover {
  background: var(--theme-primaryHover);
}

.favorite-btn,
.menu-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  color: var(--theme-text);
  cursor: pointer;
  transition: all 0.3s ease;
}

.favorite-btn:hover,
.menu-btn:hover {
  background: var(--theme-glassHover);
}

.favorite-btn.active {
  color: var(--theme-warning);
}

.template-stats {
  display: flex;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--theme-border);
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--theme-textMuted);
}

.stat i {
  color: var(--theme-primary);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
}

.empty-illustration {
  font-size: 5rem;
  color: var(--theme-border);
  margin-bottom: 2rem;
}

.empty-state h3 {
  color: var(--theme-text);
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: var(--theme-textMuted);
  margin-bottom: 2rem;
}

.create-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 2rem;
  background: var(--theme-primary);
  border: none;
  border-radius: 8px;
  color: var(--theme-background);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.create-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px var(--theme-primaryShadow);
}

/* Modal Styles */
.template-modal,
.create-modal {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-content {
  background: var(--theme-surface);
  border-radius: 20px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2rem;
  border-bottom: 1px solid var(--theme-border);
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.modal-title h2 {
  color: var(--theme-text);
  margin: 0;
}

.modal-title p {
  color: var(--theme-textMuted);
  font-size: 0.875rem;
  margin: 0;
}

.close-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  color: var(--theme-text);
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: var(--theme-danger);
  color: white;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

.template-sections {
  margin-bottom: 2rem;
}

.template-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
}

.template-section h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--theme-text);
  margin-bottom: 0.5rem;
}

.template-section p {
  color: var(--theme-textMuted);
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.section-fields {
  display: grid;
  gap: 1rem;
}

.field-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
}

.field-item label {
  color: var(--theme-text);
  font-weight: 500;
}

.field-preview {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.field-type {
  padding: 0.25rem 0.75rem;
  background: var(--theme-glass);
  border-radius: 20px;
  font-size: 0.75rem;
  color: var(--theme-textMuted);
}

.field-required {
  padding: 0.25rem 0.75rem;
  background: var(--theme-danger);
  color: white;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
}

.template-features {
  padding: 1.5rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
}

.template-features h3 {
  color: var(--theme-text);
  margin-bottom: 1rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  color: var(--theme-text);
  font-size: 0.875rem;
}

.feature i {
  color: var(--theme-primary);
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2rem;
  border-top: 1px solid var(--theme-border);
  gap: 1rem;
}

.edit-btn,
.duplicate-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  color: var(--theme-text);
  cursor: pointer;
  transition: all 0.3s ease;
}

.edit-btn:hover,
.duplicate-btn:hover {
  background: var(--theme-glassHover);
  transform: translateY(-2px);
}

.primary-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 2rem;
  background: var(--theme-primary);
  border: none;
  border-radius: 8px;
  color: var(--theme-background);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: auto;
}

.primary-btn:hover {
  background: var(--theme-primaryHover);
  transform: translateY(-2px);
  box-shadow: 0 5px 20px var(--theme-primaryShadow);
}

/* Form Styles */
.template-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  padding: 1.5rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
}

.form-section h3 {
  color: var(--theme-text);
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  color: var(--theme-text);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.75rem 1rem;
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  color: var(--theme-text);
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  border-color: var(--theme-primary);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.icon-picker {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 0.5rem;
}

.icon-option {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  color: var(--theme-text);
  cursor: pointer;
  transition: all 0.3s ease;
}

.icon-option:hover {
  background: var(--theme-glassHover);
}

.icon-option.active {
  background: var(--theme-primary);
  color: var(--theme-background);
}

.section-builder {
  padding: 1rem;
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.section-header {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.section-title-input {
  flex: 1;
  padding: 0.5rem 1rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  color: var(--theme-text);
  font-weight: 500;
  outline: none;
}

.remove-section-btn {
  padding: 0.5rem 1rem;
  background: var(--theme-danger);
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-section-btn:hover {
  background: var(--theme-dangerHover);
}

.fields-builder {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-builder {
  display: grid;
  grid-template-columns: 2fr 1fr auto auto;
  gap: 0.5rem;
  align-items: center;
}

.field-builder input,
.field-builder select {
  padding: 0.5rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 6px;
  color: var(--theme-text);
  font-size: 0.875rem;
  outline: none;
}

.required-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--theme-text);
}

.remove-field-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 6px;
  color: var(--theme-danger);
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-field-btn:hover {
  background: var(--theme-danger);
  color: white;
}

.add-field-btn,
.add-section-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  color: var(--theme-primary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-field-btn:hover,
.add-section-btn:hover {
  background: var(--theme-primary);
  color: var(--theme-background);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--theme-border);
}

.cancel-btn {
  padding: 0.75rem 1.5rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  color: var(--theme-text);
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: var(--theme-glassHover);
}

.save-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 2rem;
  background: var(--theme-success);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-btn:hover {
  background: var(--theme-successHover);
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(34, 197, 94, 0.4);
}

/* Quick Menu */
.quick-menu {
  position: fixed;
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  padding: 0.5rem;
  box-shadow: 0 5px 20px var(--theme-shadow);
  z-index: 1000;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  color: var(--theme-text);
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
}

.menu-item:hover {
  background: var(--theme-glass);
}

.menu-item.danger {
  color: var(--theme-danger);
}

.menu-divider {
  height: 1px;
  background: var(--theme-border);
  margin: 0.5rem 0;
}

/* Transitions */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.modal-fade-enter-active .modal-content,
.modal-fade-leave-active .modal-content {
  transition: transform 0.3s ease;
}

.modal-fade-enter-from .modal-content,
.modal-fade-leave-to .modal-content {
  transform: scale(0.9);
}

.menu-fade-enter-active,
.menu-fade-leave-active {
  transition: all 0.2s ease;
}

.menu-fade-enter-from,
.menu-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  color: var(--theme-text);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: var(--theme-glassHover);
  transform: translateY(-2px);
}

.action-btn.ai-btn {
  background: linear-gradient(135deg, #ff00ff22, #00ffff22);
  border-color: #ff00ff44;
}

.action-btn.marketplace-btn {
  background: linear-gradient(135deg, #00ff8822, #ffff0022);
  border-color: #00ff8844;
}

/* Sort Options */
.sort-options {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sort-options label {
  font-size: 0.875rem;
  color: var(--theme-textMuted);
}

.sort-select {
  padding: 0.5rem 1rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  color: var(--theme-text);
  font-size: 0.875rem;
  cursor: pointer;
  outline: none;
}

/* Modal Overlay */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  padding: 2rem;
}

/* AI Generator Modal Styles */
.generator-section {
  margin-bottom: 2rem;
}

.generator-section h3 {
  font-size: 1.125rem;
  margin-bottom: 1rem;
  color: var(--theme-primary);
}

.ai-prompt-input {
  width: 100%;
  padding: 1rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
  color: var(--theme-text);
  font-size: 1rem;
  resize: vertical;
  outline: none;
}

.ai-prompt-input:focus {
  border-color: var(--theme-primary);
}

.generator-options {
  margin-bottom: 2rem;
}

.generator-options h3 {
  font-size: 1.125rem;
  margin-bottom: 1rem;
  color: var(--theme-secondary);
}

.option-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.option-item:hover {
  background: var(--theme-glassHover);
  border-color: var(--theme-primary);
}

.option-item input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--theme-primary);
}

.generator-style {
  margin-bottom: 2rem;
}

.generator-style h3 {
  font-size: 1.125rem;
  margin-bottom: 1rem;
  color: var(--theme-accent);
}

.style-options {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.style-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  color: var(--theme-textMuted);
  cursor: pointer;
  transition: all 0.3s ease;
}

.style-btn.active {
  background: linear-gradient(135deg, #00ffff22, #ff00ff22);
  border-color: var(--theme-primary);
  color: var(--theme-text);
}

.generator-preview {
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
  padding: 1.5rem;
}

.preview-content {
  max-height: 300px;
  overflow-y: auto;
}

.ai-generate-btn {
  background: linear-gradient(135deg, #ff00ff, #00ffff);
}

.ai-generate-btn:hover:not(:disabled) {
  box-shadow: 0 5px 20px rgba(255, 0, 255, 0.4);
}

/* Marketplace Modal Styles */
.marketplace-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--theme-glass), transparent);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
}

.stat-card i {
  font-size: 2rem;
  color: var(--theme-primary);
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--theme-text);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--theme-textMuted);
}

.marketplace-categories {
  margin-bottom: 2rem;
}

.marketplace-categories h3 {
  font-size: 1.125rem;
  margin-bottom: 1rem;
  color: var(--theme-secondary);
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.marketplace-category {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.marketplace-category:hover {
  background: var(--theme-glassHover);
  transform: translateY(-2px);
}

.marketplace-category i {
  font-size: 2rem;
  color: var(--theme-accent);
}

.marketplace-category .count {
  font-size: 0.75rem;
  color: var(--theme-textMuted);
}

.featured-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.featured-template {
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.featured-template:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
}

.template-icon-preview {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: white;
}

.template-info {
  padding: 1rem;
}

.template-info h4 {
  font-size: 1rem;
  margin-bottom: 0.5rem;
  color: var(--theme-text);
}

.template-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.75rem;
  font-size: 0.75rem;
  color: var(--theme-textMuted);
}

.template-meta span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.template-price {
  display: flex;
  justify-content: flex-end;
}

.template-price .free {
  color: var(--theme-success);
  font-weight: bold;
}

.template-price .price {
  color: var(--theme-warning);
  font-weight: bold;
}

/* Version History Modal Styles */
.version-timeline {
  position: relative;
  padding-left: 2rem;
}

.version-timeline::before {
  content: '';
  position: absolute;
  left: 0.5rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, var(--theme-primary), var(--theme-secondary));
}

.version-item {
  position: relative;
  margin-bottom: 2rem;
}

.version-marker {
  position: absolute;
  left: -1.5rem;
  top: 0.5rem;
  width: 1rem;
  height: 1rem;
  background: var(--theme-primary);
  border: 2px solid var(--theme-surface);
  border-radius: 50%;
}

.version-content {
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
  padding: 1.5rem;
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.version-header h4 {
  font-size: 1rem;
  color: var(--theme-primary);
}

.version-date {
  font-size: 0.75rem;
  color: var(--theme-textMuted);
}

.version-description {
  color: var(--theme-text);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.version-changes {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.change-tag {
  padding: 0.25rem 0.75rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 20px;
  font-size: 0.75rem;
  color: var(--theme-textMuted);
}

.version-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-small {
  padding: 0.5rem 1rem;
  background: var(--theme-glass);
  border: 1px solid var(--theme-border);
  border-radius: 6px;
  color: var(--theme-textMuted);
  font-size: 0.75rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: all 0.3s ease;
}

.btn-small:hover {
  background: var(--theme-glassHover);
  color: var(--theme-text);
}

/* Responsive */
@media (max-width: 768px) {
  .header-controls {
    flex-direction: column;
    gap: 1rem;
  }
  
  .filter-controls {
    width: 100%;
    overflow-x: auto;
  }
  
  .action-buttons {
    width: 100%;
    justify-content: center;
  }
  
  .sort-options {
    width: 100%;
    justify-content: center;
  }
  
  .templates-grid {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .field-builder {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-footer {
    flex-direction: column;
  }
  
  .primary-btn {
    width: 100%;
    justify-content: center;
  }
  
  .option-grid {
    grid-template-columns: 1fr;
  }
  
  .marketplace-stats {
    grid-template-columns: 1fr;
  }
  
  .featured-grid {
    grid-template-columns: 1fr;
  }
}
</style>