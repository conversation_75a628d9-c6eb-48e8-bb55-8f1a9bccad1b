<template>
  <div class="study-plan-page">
    <!-- Ultra Modern Background -->
    <div class="background-effects">
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
      <div class="gradient-orb orb-4"></div>
      <div class="floating-particles"></div>
      <div class="grid-overlay"></div>
      <div class="animated-lines">
        <div class="line line-1"></div>
        <div class="line line-2"></div>
        <div class="line line-3"></div>
      </div>
      <div class="glow-effects">
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
      </div>
    </div>

    <!-- Main Layout -->
    <div class="study-plan-layout">
      <!-- Scheduler Column -->
      <div class="scheduler-column">
        <!-- Header Section -->
        <div class="scheduler-header">
          <div class="header-background"></div>
          <div class="header-content">
            <div class="header-left">
              <button class="back-button" @click="navigateBack" title="Voltar para planos">
                <i class="fas fa-arrow-left"></i>
              </button>
              <div class="header-icon">
                <div class="icon-wrapper">
                  <font-awesome-icon icon="brain" />
                  <div class="icon-ring ring-1"></div>
                  <div class="icon-ring ring-2"></div>
                  <div class="icon-pulse"></div>
                </div>
              </div>
              <div class="header-text">
                <h1 class="main-title">
                  <span class="title-gradient">Criar Novo</span>
                  <span class="title-accent">Plano</span>
                </h1>
                <p class="subtitle">Configure seu plano de estudos personalizado</p>
              </div>
            </div>
        
            <div class="header-stats">
              <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                  <font-awesome-icon icon="book-open" />
                </div>
                <div class="stat-info">
                  <span class="stat-value">{{ totalSubjects }}</span>
                  <span class="stat-label">Matérias</span>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #f093fb, #f5576c);">
                  <font-awesome-icon icon="clock" />
                </div>
                <div class="stat-info">
                  <span class="stat-value">{{ studyHours }}h</span>
                  <span class="stat-label">Hoje</span>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #4facfe, #00f2fe);">
                  <font-awesome-icon icon="chart-line" />
                </div>
                <div class="stat-info">
                  <span class="stat-value">{{ efficiency }}%</span>
                  <span class="stat-label">Eficiência</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Main Content -->
        <div class="main-container">
          <!-- Content Grid -->
          <div class="content-grid">
            <!-- Left Column -->
            <div class="left-column">
              <!-- Active Plans Section -->
              <section class="modern-card">
            <div class="section-header">
              <div class="section-title">
                <h2 class="section-heading">
                  <span class="title-gradient">Planos</span>
                  <span class="title-accent">Ativos</span>
                </h2>
              </div>
              <button @click="createNewPlan" class="create-plan-btn">
                <i class="fas fa-plus"></i>
                <span>Novo Plano</span>
              </button>
            </div>

            <div class="plans-grid">
              <div 
                v-for="plan in activePlans" 
                :key="plan.id"
                class="plan-card"
                @click="selectPlan(plan)"
                :class="{ 'is-active': selectedPlan?.id === plan.id }"
              >
                <div class="plan-header">
                  <div class="plan-icon" :style="{ background: plan.color }">
                    <i :class="plan.icon"></i>
                  </div>
                  <div class="plan-info">
                    <h3>{{ plan.name }}</h3>
                    <p>{{ plan.description }}</p>
                  </div>
                  <div class="plan-actions">
                    <button @click.stop="togglePlanDetails(plan.id)" class="icon-btn">
                      <i :class="expandedPlans.includes(plan.id) ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
                    </button>
                    <button @click.stop="editPlan(plan)" class="icon-btn">
                      <i class="fas fa-edit"></i>
                    </button>
                  </div>
                </div>
                
                <!-- Quick Stats -->
                <div class="plan-stats" v-if="!expandedPlans.includes(plan.id)">
                  <div class="stat-item">
                    <i class="fas fa-tasks"></i>
                    <span>{{ plan.completedTasks }}/{{ plan.totalTasks }}</span>
                  </div>
                  <div class="stat-item">
                    <i class="fas fa-clock"></i>
                    <span>{{ plan.estimatedTime }}h</span>
                  </div>
                  <div class="stat-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>{{ plan.deadline }}</span>
                  </div>
                </div>
                
                <!-- Progress Bar -->
                <div class="plan-progress" v-if="!expandedPlans.includes(plan.id)">
                  <div class="progress-label">
                    <span>Progresso</span>
                    <span>{{ plan.progress }}%</span>
                  </div>
                  <div class="progress-bar">
                    <div class="progress-fill" :style="{ width: plan.progress + '%' }"></div>
                  </div>
                </div>
                
                <!-- Expanded Details -->
                <transition name="expand">
                  <div v-if="expandedPlans.includes(plan.id)" class="plan-details" @click.stop>
                    <!-- Tab Navigation -->
                    <div class="detail-tabs">
                      <button 
                        v-for="tab in ['Visão Geral', 'Tarefas', 'Estatísticas', 'Linha do Tempo']"
                        :key="tab"
                        class="tab-btn"
                        :class="{ active: selectedTab[plan.id] === tab }"
                        @click.stop="selectTab(plan.id, tab)"
                      >
                        <i :class="getTabIcon(tab)"></i>
                        {{ tab }}
                      </button>
                    </div>
                    
                    <!-- Tab Content -->
                    <div class="tab-content">
                      <!-- Visão Geral Tab -->
                      <div v-if="selectedTab[plan.id] === 'Visão Geral'" class="overview-tab">
                        <div class="overview-grid">
                          <div class="overview-card">
                            <div class="overview-icon">
                              <i class="fas fa-bullseye"></i>
                            </div>
                            <div class="overview-info">
                              <h4>Objetivos</h4>
                              <ul class="objectives-list-view">
                                <li v-for="(obj, idx) in (plan.objectives || [])" :key="idx">
                                  <i class="fas fa-check-circle" :class="{ completed: obj.completed }"></i>
                                  {{ obj.text || obj }}
                                </li>
                                <li v-if="!plan.objectives || plan.objectives.length === 0" class="empty-state">
                                  Nenhum objetivo definido
                                </li>
                              </ul>
                            </div>
                          </div>
                          
                          <div class="overview-card">
                            <div class="overview-icon">
                              <i class="fas fa-chart-pie"></i>
                            </div>
                            <div class="overview-info">
                              <h4>Distribuição</h4>
                              <div class="mini-chart">
                                <div class="chart-bar" :style="{ height: '60%', background: '#667eea' }">
                                  <span>60%</span>
                                </div>
                                <div class="chart-bar" :style="{ height: '40%', background: '#f093fb' }">
                                  <span>40%</span>
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          <div class="overview-card">
                            <div class="overview-icon">
                              <i class="fas fa-fire"></i>
                            </div>
                            <div class="overview-info">
                              <h4>Desempenho</h4>
                              <div class="performance-meter">
                                <div class="meter-fill" :style="{ width: '85%' }"></div>
                              </div>
                              <p class="performance-text">Excelente ritmo!</p>
                            </div>
                          </div>
                        </div>
                        
                        <div class="study-schedule">
                          <h4><i class="fas fa-calendar-alt"></i> Cronograma Sugerido</h4>
                          <div class="schedule-items">
                            <div class="schedule-item">
                              <span class="schedule-time">Segunda - Sexta</span>
                              <span class="schedule-duration">2h por dia</span>
                            </div>
                            <div class="schedule-item">
                              <span class="schedule-time">Fim de semana</span>
                              <span class="schedule-duration">3h por dia</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Tarefas Tab -->
                      <div v-if="selectedTab[plan.id] === 'Tarefas'" class="tasks-tab">
                        <div class="tasks-header">
                          <button class="add-task-btn" @click.stop="addNewTask(plan.id)">
                            <i class="fas fa-plus"></i>
                            Nova Tarefa
                          </button>
                          <div class="task-filters">
                            <button 
                              class="filter-btn" 
                              :class="{ active: taskFilter === 'all' }"
                              @click.stop="setTaskFilter('all')"
                            >
                              <i class="fas fa-list"></i>
                              Todas
                            </button>
                            <button 
                              class="filter-btn" 
                              :class="{ active: taskFilter === 'pending' }"
                              @click.stop="setTaskFilter('pending')"
                            >
                              <i class="fas fa-clock"></i>
                              Pendentes
                            </button>
                            <button 
                              class="filter-btn" 
                              :class="{ active: taskFilter === 'completed' }"
                              @click.stop="setTaskFilter('completed')"
                            >
                              <i class="fas fa-check"></i>
                              Concluídas
                            </button>
                          </div>
                        </div>
                        
                        <div class="tasks-list">
                          <div 
                            v-for="task in getFilteredTasks(plan.id)" 
                            :key="task.id" 
                            class="task-item"
                            :class="{ completed: task.completed }"
                          >
                            <div class="task-checkbox">
                              <input 
                                type="checkbox" 
                                :checked="task.completed" 
                                @change.stop="toggleTask(plan.id, task.id)"
                              >
                            </div>
                            <div class="task-content" @click.stop="editTask(plan.id, task)">
                              <h5>{{ task.title }}</h5>
                              <p v-if="task.description" class="task-description">{{ task.description }}</p>
                              <div class="task-meta">
                                <span><i class="fas fa-clock"></i> {{ task.estimatedTime }}h</span>
                                <span><i class="fas fa-tag"></i> {{ task.category }}</span>
                                <span v-if="task.dueDate"><i class="fas fa-calendar"></i> {{ formatDateShort(task.dueDate) }}</span>
                              </div>
                            </div>
                            <div class="task-actions">
                              <span class="task-priority" :class="task.priority">
                                <i :class="getPriorityIcon(task.priority)"></i>
                                {{ task.priority }}
                              </span>
                              <button class="task-delete" @click.stop="deleteTask(plan.id, task.id)">
                                <i class="fas fa-trash"></i>
                              </button>
                            </div>
                          </div>
                          
                          <div v-if="!getFilteredTasks(plan.id).length" class="empty-tasks">
                            <i class="fas fa-tasks"></i>
                            <p>{{ taskFilter === 'completed' ? 'Nenhuma tarefa concluída' : 
                                 taskFilter === 'pending' ? 'Nenhuma tarefa pendente' : 
                                 'Nenhuma tarefa cadastrada' }}</p>
                            <button v-if="taskFilter === 'all'" class="add-first-task" @click.stop="addNewTask(plan.id)">
                              Adicionar primeira tarefa
                            </button>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Estatísticas Tab -->
                      <div v-if="selectedTab[plan.id] === 'Estatísticas'" class="stats-tab">
                        <div class="stats-overview">
                          <div class="stat-box">
                            <i class="fas fa-hourglass-half"></i>
                            <div class="stat-content">
                              <span class="stat-value">{{ getStudyHours(plan.id) }}h</span>
                              <span class="stat-label">Horas Estudadas</span>
                            </div>
                          </div>
                          <div class="stat-box">
                            <i class="fas fa-check-double"></i>
                            <div class="stat-content">
                              <span class="stat-value">{{ plan.completedTasks }}</span>
                              <span class="stat-label">Tarefas Completas</span>
                            </div>
                          </div>
                          <div class="stat-box">
                            <i class="fas fa-trophy"></i>
                            <div class="stat-content">
                              <span class="stat-value">{{ getEfficiency(plan.id) }}%</span>
                              <span class="stat-label">Eficiência</span>
                            </div>
                          </div>
                          <div class="stat-box">
                            <i class="fas fa-fire-alt"></i>
                            <div class="stat-content">
                              <span class="stat-value">{{ getStreak(plan.id) }}</span>
                              <span class="stat-label">Dias Consecutivos</span>
                            </div>
                          </div>
                        </div>
                        
                        <div class="progress-chart">
                          <h4>Progresso Semanal</h4>
                          <div class="week-chart">
                            <div v-for="day in weekProgress" :key="day.name" class="day-bar">
                              <div class="bar-fill" :style="{ height: day.progress + '%' }">
                                <span class="bar-value">{{ day.hours }}h</span>
                              </div>
                              <span class="day-name">{{ day.name }}</span>
                            </div>
                          </div>
                        </div>
                        
                        <div class="insights-section">
                          <h4><i class="fas fa-lightbulb"></i> Insights</h4>
                          <div class="insight-cards">
                            <div class="insight-card">
                              <i class="fas fa-chart-line"></i>
                              <p>Você está 20% acima da média de estudo desta semana!</p>
                            </div>
                            <div class="insight-card">
                              <i class="fas fa-clock"></i>
                              <p>Melhor horário de produtividade: 14h - 16h</p>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Linha do Tempo Tab -->
                      <div v-if="selectedTab[plan.id] === 'Linha do Tempo'" class="timeline-tab">
                        <div class="timeline-container">
                          <div v-for="event in getPlanTimeline(plan.id)" :key="event.id" class="timeline-item">
                            <div class="timeline-marker" :class="event.type">
                              <i :class="event.icon"></i>
                            </div>
                            <div class="timeline-content">
                              <h5>{{ event.title }}</h5>
                              <p>{{ event.description }}</p>
                              <span class="timeline-date">{{ formatDate(event.date) }}</span>
                            </div>
                          </div>
                        </div>
                        
                        <div class="milestones-section">
                          <h4><i class="fas fa-flag-checkered"></i> Marcos Importantes</h4>
                          <div class="milestones-grid">
                            <div class="milestone-card" :class="{ achieved: true }">
                              <i class="fas fa-play-circle"></i>
                              <h5>Início do Plano</h5>
                              <p>{{ formatDate(plan.startDate) }}</p>
                            </div>
                            <div class="milestone-card" :class="{ achieved: plan.progress > 50 }">
                              <i class="fas fa-star-half-alt"></i>
                              <h5>50% Concluído</h5>
                              <p>{{ plan.progress > 50 ? 'Alcançado!' : 'Em progresso' }}</p>
                            </div>
                            <div class="milestone-card" :class="{ achieved: plan.progress === 100 }">
                              <i class="fas fa-trophy"></i>
                              <h5>Meta Final</h5>
                              <p>{{ plan.deadline }}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="detail-actions">
                      <button class="action-btn secondary" @click.stop="exportPlan(plan.id)">
                        <i class="fas fa-download"></i>
                        Exportar
                      </button>
                      <button class="action-btn secondary" @click.stop="sharePlan(plan.id)">
                        <i class="fas fa-share"></i>
                        Compartilhar
                      </button>
                      <button class="action-btn primary" @click.stop="startStudySession(plan.id)">
                        <i class="fas fa-play"></i>
                        Iniciar Sessão
                      </button>
                    </div>
                  </div>
                </transition>
              </div>
            </div>
          </section>

              <!-- Study Sessions -->
              <section class="modern-card">
            <div class="section-header">
              <div class="section-title">
                <h2 class="section-heading">
                  <span class="title-gradient">Sessões</span>
                  <span class="title-accent">Hoje</span>
                </h2>
              </div>
              <div class="view-toggle">
                <button 
                  v-for="view in ['list', 'timeline']" 
                  :key="view"
                  @click="sessionView = view"
                  :class="['toggle-btn', { active: sessionView === view }]"
                >
                  <i :class="view === 'list' ? 'fas fa-list' : 'fas fa-stream'"></i>
                </button>
              </div>
            </div>

            <div v-if="sessionView === 'list'" class="sessions-list">
              <div 
                v-for="session in todaySessions" 
                :key="session.id"
                class="session-card"
                :class="{ 
                  completed: session.status === 'completed',
                  current: session.status === 'in-progress' 
                }"
              >
                <div class="session-time">
                  <i class="fas fa-clock"></i>
                  <span>{{ session.startTime }} - {{ session.endTime }}</span>
                </div>
                <div class="session-content">
                  <h4>{{ session.subject }}</h4>
                  <p>{{ session.topic }}</p>
                  <div class="session-tags">
                    <span v-for="tag in session.tags" :key="tag" class="tag">{{ tag }}</span>
                  </div>
                </div>
                <div class="session-actions">
                  <button 
                    v-if="session.status === 'pending'" 
                    @click="startSession(session)"
                    class="btn btn-sm btn-primary"
                  >
                    <i class="fas fa-play"></i>
                    Iniciar
                  </button>
                  <button 
                    v-else-if="session.status === 'in-progress'" 
                    @click="pauseSession(session)"
                    class="btn btn-sm btn-secondary"
                  >
                    <i class="fas fa-pause"></i>
                    Pausar
                  </button>
                  <div v-else class="completion-badge">
                    <i class="fas fa-check-circle"></i>
                    Concluído
                  </div>
                </div>
              </div>
            </div>

            <div v-else class="timeline-view">
              <!-- Timeline implementation -->
            </div>
              </section>
            </div>

            <!-- Right Column -->
            <div class="right-column">
          <!-- Calendar Integration -->
          <section class="calendar-section">
            <div class="section-header">
              <div class="section-title">
                <h2 class="section-heading">
                  <span class="title-gradient">Calendário</span>
                  <span class="title-accent">Estudos</span>
                </h2>
              </div>
            </div>
            
            <div class="calendar-wrapper">
              <SharedCalendar 
                :events="calendarEvents"
                :selected-date="selectedCalendarDate"
                :use-vuex-store="false"
                @date-selected="handleDateSelected"
                @month-changed="handleMonthChanged"
              />
            </div>
          </section>

          <!-- AI Insights -->
          <section class="insights-section">
            <div class="section-header">
              <div class="section-title">
                <h2 class="section-heading">
                  <span class="title-gradient">Insights</span>
                  <span class="title-accent">Inteligentes</span>
                </h2>
              </div>
              <button @click="refreshInsights" class="refresh-btn">
                <i class="fas fa-sync-alt"></i>
              </button>
            </div>

            <div class="insights-list">
              <div v-for="insight in aiInsights" :key="insight.id" class="insight-card">
                <div class="insight-icon" :class="insight.type">
                  <i :class="insight.icon"></i>
                </div>
                <div class="insight-content">
                  <h4>{{ insight.title }}</h4>
                  <p>{{ insight.message }}</p>
                  <button v-if="insight.action" @click="insight.action" class="insight-action">
                    {{ insight.actionText }}
                    <i class="fas fa-arrow-right"></i>
                  </button>
                </div>
              </div>
            </div>
          </section>

          <!-- Quick Stats -->
          <section class="stats-section">
            <div class="stats-grid">
              <div class="stat-box">
                <div class="stat-icon-wrapper">
                  <i class="fas fa-fire-flame-curved"></i>
                </div>
                <div class="stat-content">
                  <span class="stat-number">{{ streakDays }}</span>
                  <span class="stat-desc">Dias de sequência</span>
                </div>
              </div>
              
              <div class="stat-box">
                <div class="stat-icon-wrapper">
                  <i class="fas fa-circle-check"></i>
                </div>
                <div class="stat-content">
                  <span class="stat-number">{{ completedGoals }}</span>
                  <span class="stat-desc">Metas atingidas</span>
                </div>
              </div>
              
              <div class="stat-box">
                <div class="stat-icon-wrapper">
                  <i class="fas fa-lightbulb"></i>
                </div>
                <div class="stat-content">
                  <span class="stat-number">{{ retentionRate }}%</span>
                  <span class="stat-desc">Taxa de retenção</span>
                </div>
              </div>
            </div>
          </section>
            </div>
          </div>
        </div>
      </div>
    </div>


    <!-- Modal Novo Plano -->
    <div v-if="showNewPlanModal" class="modal-overlay" @click.self="cancelPlanCreation">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Criar Novo Plano</h3>
          <button class="modal-close" @click="cancelPlanCreation">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-form">
          <!-- Progress Indicator -->
          <div class="form-progress">
            <div class="progress-step" :class="{ active: formStep >= 1, completed: formStep > 1 }">
              <span class="step-number">1</span>
              <span class="step-label">Básico</span>
            </div>
            <div class="progress-line" :class="{ active: formStep > 1 }"></div>
            <div class="progress-step" :class="{ active: formStep >= 2, completed: formStep > 2 }">
              <span class="step-number">2</span>
              <span class="step-label">Detalhes</span>
            </div>
            <div class="progress-line" :class="{ active: formStep > 2 }"></div>
            <div class="progress-step" :class="{ active: formStep >= 3 }">
              <span class="step-number">3</span>
              <span class="step-label">Personalização</span>
            </div>
          </div>

          <!-- Step 1: Basic Info -->
          <div v-if="formStep === 1" class="form-step">
            <div class="form-group">
              <label>
                <i class="fas fa-book"></i>
                Nome do Plano
              </label>
              <input 
                v-model="newPlan.name" 
                type="text" 
                placeholder="Ex: Anatomia Sistêmica"
                required
                class="form-input"
                @input="updatePlanPreview"
              />
              <span class="char-count">{{ newPlan.name.length }}/50</span>
            </div>
            
            <div class="form-group">
              <label>
                <i class="fas fa-align-left"></i>
                Descrição
              </label>
              <textarea 
                v-model="newPlan.description" 
                placeholder="Descreva o objetivo deste plano..."
                rows="4"
                class="form-input"
                @input="updatePlanPreview"
              ></textarea>
              <span class="char-count">{{ newPlan.description.length }}/200</span>
            </div>

            <div class="form-group">
              <label>
                <i class="fas fa-graduation-cap"></i>
                Categoria
              </label>
              <select v-model="newPlan.category" class="form-input" required>
                <option value="">Selecione uma categoria</option>
                <option value="medicina">Medicina</option>
                <option value="biologia">Biologia</option>
                <option value="quimica">Química</option>
                <option value="fisica">Física</option>
                <option value="matematica">Matemática</option>
                <option value="outras">Outras</option>
              </select>
            </div>
          </div>

          <!-- Step 2: Details -->
          <div v-if="formStep === 2" class="form-step">
            <div class="form-row">
              <div class="form-group">
                <label>
                  <i class="fas fa-calendar-alt"></i>
                  Data de Início
                </label>
                <input 
                  v-model="newPlan.startDate" 
                  type="date"
                  :min="today"
                  required
                  class="form-input"
                />
              </div>
              
              <div class="form-group">
                <label>
                  <i class="fas fa-flag-checkered"></i>
                  Prazo Final
                </label>
                <input 
                  v-model="newPlan.deadline" 
                  type="date"
                  :min="newPlan.startDate || today"
                  required
                  class="form-input"
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>
                  <i class="fas fa-clock"></i>
                  Tempo Estimado
                </label>
                <div class="time-input-group">
                  <input 
                    v-model.number="newPlan.estimatedTime" 
                    type="number" 
                    min="1"
                    placeholder="40"
                    required
                    class="form-input"
                  />
                  <span class="time-unit">horas</span>
                </div>
                <div class="time-suggestions">
                  <button type="button" @click="newPlan.estimatedTime = 20" class="suggestion-chip">20h</button>
                  <button type="button" @click="newPlan.estimatedTime = 40" class="suggestion-chip">40h</button>
                  <button type="button" @click="newPlan.estimatedTime = 60" class="suggestion-chip">60h</button>
                  <button type="button" @click="newPlan.estimatedTime = 80" class="suggestion-chip">80h</button>
                </div>
              </div>

              <div class="form-group">
                <label>
                  <i class="fas fa-chart-line"></i>
                  Dificuldade
                </label>
                <div class="difficulty-selector">
                  <button 
                    v-for="level in ['Fácil', 'Médio', 'Difícil']" 
                    :key="level"
                    type="button"
                    class="difficulty-option"
                    :class="{ selected: newPlan.difficulty === level }"
                    @click="newPlan.difficulty = level"
                  >
                    <i :class="getDifficultyIcon(level)"></i>
                    {{ level }}
                  </button>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label>
                <i class="fas fa-tasks"></i>
                Objetivos de Aprendizado
              </label>
              <div class="objectives-list">
                <div v-for="(objective, index) in newPlan.objectives" :key="index" class="objective-item">
                  <input 
                    v-model="objective.text" 
                    type="text" 
                    placeholder="Digite um objetivo..."
                    class="form-input"
                  />
                  <button type="button" @click="removeObjective(index)" class="remove-btn">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
                <button type="button" @click="addObjective" class="add-objective-btn">
                  <i class="fas fa-plus"></i>
                  Adicionar Objetivo
                </button>
              </div>
            </div>
          </div>

          <!-- Step 3: Customization -->
          <div v-if="formStep === 3" class="form-step">
            <div class="form-group">
              <label>
                <i class="fas fa-palette"></i>
                Cor do Plano
              </label>
              <div class="color-picker-advanced">
                <div class="preset-colors">
                  <button 
                    v-for="color in planColors" 
                    :key="color"
                    type="button"
                    class="color-option"
                    :style="{ background: color }"
                    :class="{ selected: newPlan.color === color }"
                    @click="newPlan.color = color"
                  >
                    <i v-if="newPlan.color === color" class="fas fa-check"></i>
                  </button>
                </div>
                <div class="custom-color">
                  <label>Cor personalizada:</label>
                  <input 
                    v-model="newPlan.color" 
                    type="color" 
                    class="color-input"
                  />
                </div>
              </div>
            </div>

            <div class="form-group">
              <label>
                <i class="fas fa-icons"></i>
                Ícone do Plano
              </label>
              <div class="icon-picker">
                <button 
                  v-for="icon in planIcons" 
                  :key="icon"
                  type="button"
                  class="icon-option"
                  :class="{ selected: newPlan.icon === icon }"
                  @click="newPlan.icon = icon"
                >
                  <i :class="icon"></i>
                </button>
              </div>
            </div>

            <div class="form-group">
              <label>
                <i class="fas fa-bell"></i>
                Notificações
              </label>
              <div class="notification-settings">
                <label class="switch-label">
                  <input 
                    v-model="newPlan.notifications.enabled" 
                    type="checkbox"
                    class="switch-input"
                  />
                  <span class="switch-slider"></span>
                  <span class="switch-text">Ativar lembretes</span>
                </label>
                
                <div v-if="newPlan.notifications.enabled" class="notification-options">
                  <label class="checkbox-label">
                    <input 
                      v-model="newPlan.notifications.daily" 
                      type="checkbox"
                    />
                    <span>Lembrete diário</span>
                  </label>
                  <label class="checkbox-label">
                    <input 
                      v-model="newPlan.notifications.deadlineAlert" 
                      type="checkbox"
                    />
                    <span>Alerta de prazo (3 dias antes)</span>
                  </label>
                </div>
              </div>
            </div>

            <!-- Plan Preview -->
            <div class="plan-preview">
              <h4>Prévia do Plano</h4>
              <div class="preview-card" :style="{ borderColor: newPlan.color }">
                <div class="preview-icon" :style="{ background: newPlan.color }">
                  <i :class="newPlan.icon"></i>
                </div>
                <div class="preview-info">
                  <h5>{{ newPlan.name || 'Nome do Plano' }}</h5>
                  <p>{{ newPlan.description || 'Descrição do plano...' }}</p>
                  <div class="preview-meta">
                    <span><i class="fas fa-clock"></i> {{ newPlan.estimatedTime }}h</span>
                    <span><i class="fas fa-calendar"></i> {{ formatDate(newPlan.deadline) }}</span>
                    <span><i class="fas fa-signal"></i> {{ newPlan.difficulty }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="modal-actions">
            <button v-if="formStep > 1" type="button" class="btn-back" @click="previousStep">
              <i class="fas fa-arrow-left"></i>
              Voltar
            </button>
            <button type="button" class="btn-cancel" @click="cancelPlanCreation">
              Cancelar
            </button>
            <button v-if="formStep < 3" type="button" class="btn-next" @click="nextStep" :disabled="!canProceed">
              Próximo
              <i class="fas fa-arrow-right"></i>
            </button>
            <button v-else type="button" class="btn-submit" @click.prevent="submitNewPlan">
              <i class="fas fa-check"></i>
              Criar Plano
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Modal Compartilhar -->
    <div v-if="showShareModal" class="modal-overlay" @click.self="showShareModal = false">
      <div class="modal-content share-modal">
        <div class="modal-header">
          <h3>Compartilhar Plano</h3>
          <button class="modal-close" @click="showShareModal = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="share-info">
            <h4>{{ selectedPlanForAction?.name }}</h4>
            <p>Compartilhe este plano de estudos com outras pessoas</p>
          </div>
          
          <div class="share-options">
            <div class="share-link-section">
              <label>Link de compartilhamento</label>
              <div class="link-input-group">
                <input 
                  type="text" 
                  :value="shareLink" 
                  readonly 
                  class="share-link-input"
                />
                <button @click="copyShareLink" class="copy-btn">
                  <i class="fas fa-copy"></i>
                  Copiar
                </button>
              </div>
            </div>
            
            <div class="share-methods">
              <h5>Compartilhar via:</h5>
              <div class="share-buttons">
                <button class="share-method-btn email">
                  <i class="fas fa-envelope"></i>
                  Email
                </button>
                <button class="share-method-btn whatsapp">
                  <i class="fab fa-whatsapp"></i>
                  WhatsApp
                </button>
                <button class="share-method-btn teams">
                  <i class="fab fa-microsoft"></i>
                  Teams
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Modal Iniciar Sessão -->
    <div v-if="showSessionModal" class="modal-overlay" @click.self="showSessionModal = false">
      <div class="modal-content session-modal">
        <div class="modal-header">
          <h3>Iniciar Sessão de Estudo</h3>
          <button class="modal-close" @click="showSessionModal = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="session-info">
            <div class="session-plan-info">
              <div class="plan-icon-large" :style="{ background: selectedPlanForAction?.color || '#667eea' }">
                <i :class="selectedPlanForAction?.icon || 'fas fa-book'"></i>
              </div>
              <div class="plan-details-modal">
                <h4>{{ selectedPlanForAction?.name }}</h4>
                <p>{{ selectedPlanForAction?.description }}</p>
              </div>
            </div>
            
            <div class="session-config">
              <div class="config-section">
                <label>
                  <i class="fas fa-clock"></i>
                  Duração da Sessão
                </label>
                <div class="duration-options">
                  <button 
                    class="duration-btn" 
                    :class="{ active: sessionDuration === 25 }"
                    @click="sessionDuration = 25"
                  >25 min</button>
                  <button 
                    class="duration-btn" 
                    :class="{ active: sessionDuration === 50 }"
                    @click="sessionDuration = 50"
                  >50 min</button>
                  <button 
                    class="duration-btn" 
                    :class="{ active: sessionDuration === 90 }"
                    @click="sessionDuration = 90"
                  >90 min</button>
                  <button 
                    class="duration-btn" 
                    :class="{ active: ![25, 50, 90].includes(sessionDuration) }"
                    @click="showCustomDuration = true"
                  >Personalizado</button>
                </div>
              </div>
              
              <div class="config-section">
                <label>
                  <i class="fas fa-bullseye"></i>
                  Foco da Sessão
                </label>
                <select class="session-focus-select" v-model="sessionFocus">
                  <option>Revisão Geral</option>
                  <option>Exercícios Práticos</option>
                  <option>Leitura e Anotações</option>
                  <option>Resolução de Questões</option>
                </select>
              </div>
              
              <div class="config-section">
                <label>
                  <i class="fas fa-cog"></i>
                  Ambiente de Estudo
                </label>
                <div class="environment-options">
                  <label class="checkbox-option">
                    <input type="checkbox" v-model="sessionSettings.breakReminders">
                    <span><i class="fas fa-bell"></i> Lembretes de pausa inteligentes</span>
                    <small>Notifica com base no seu ritmo de estudo</small>
                  </label>
                  <label class="checkbox-option">
                    <input type="checkbox" v-model="sessionSettings.progressMilestones">
                    <span><i class="fas fa-trophy"></i> Celebrar marcos de progresso</span>
                    <small>Animações ao completar 25%, 50%, 75%</small>
                  </label>
                  <label class="checkbox-option">
                    <input type="checkbox" v-model="sessionSettings.focusMode">
                    <span><i class="fas fa-eye-slash"></i> Modo foco total</span>
                    <small>Esconde outras abas e notificações</small>
                  </label>
                  <label class="checkbox-option">
                    <input type="checkbox" v-model="sessionSettings.studyStreak">
                    <span><i class="fas fa-fire"></i> Rastreador de sequência</span>
                    <small>Acompanha dias consecutivos de estudo</small>
                  </label>
                </div>
              </div>
              
              <div class="config-section">
                <label>
                  <i class="fas fa-palette"></i>
                  Tema do Timer
                </label>
                <div class="theme-options">
                  <button 
                    v-for="theme in timerThemes" 
                    :key="theme.id"
                    @click="sessionSettings.theme = theme.id"
                    class="theme-option"
                    :class="{ active: sessionSettings.theme === theme.id }"
                    :style="{ background: theme.gradient }"
                  >
                    <i :class="theme.icon"></i>
                    <span>{{ theme.name }}</span>
                  </button>
                </div>
              </div>
              
              <div class="config-section">
                <label>
                  <i class="fas fa-brain"></i>
                  Técnica de Estudo
                </label>
                <div class="technique-options">
                  <label class="radio-option" v-for="technique in studyTechniques" :key="technique.id">
                    <input 
                      type="radio" 
                      name="technique" 
                      :value="technique.id" 
                      v-model="sessionSettings.technique"
                    >
                    <div class="technique-card">
                      <i :class="technique.icon"></i>
                      <div>
                        <strong>{{ technique.name }}</strong>
                        <small>{{ technique.description }}</small>
                      </div>
                    </div>
                  </label>
                </div>
              </div>
            </div>
          </div>
          
          <div class="modal-actions">
            <button class="btn-cancel" @click="showSessionModal = false">
              Cancelar
            </button>
            <button class="btn-start-session" @click="confirmStartSession">
              <i class="fas fa-play"></i>
              Iniciar Agora
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Custom Duration Modal -->
    <transition name="modal-fade">
      <div v-if="showCustomDuration" class="modal-overlay" @click="showCustomDuration = false">
        <div class="modal-content custom-duration-modal" @click.stop>
          <div class="modal-header">
            <h3><i class="fas fa-clock"></i> Duração Personalizada</h3>
            <button class="close-btn" @click="showCustomDuration = false">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <div class="modal-body">
            <div class="custom-duration-input">
              <label>Defina a duração em minutos:</label>
              <div class="duration-input-group">
                <button @click="customDurationInput = Math.max(5, customDurationInput - 5)" class="duration-adjust">
                  <i class="fas fa-minus"></i>
                </button>
                <input 
                  type="number" 
                  v-model.number="customDurationInput" 
                  min="5" 
                  max="180"
                  class="duration-input"
                  @keyup.enter="applyCustomDuration"
                >
                <button @click="customDurationInput = Math.min(180, customDurationInput + 5)" class="duration-adjust">
                  <i class="fas fa-plus"></i>
                </button>
              </div>
              <span class="duration-hint">Entre 5 e 180 minutos</span>
              
              <div class="duration-presets">
                <span>Sugestões:</span>
                <button v-for="preset in [15, 30, 45, 60, 75, 120]" 
                        :key="preset"
                        @click="customDurationInput = preset"
                        class="preset-btn">
                  {{ preset }}min
                </button>
              </div>
            </div>
          </div>
          
          <div class="modal-actions">
            <button class="btn-cancel" @click="showCustomDuration = false">
              Cancelar
            </button>
            <button class="btn-apply" @click="applyCustomDuration">
              <i class="fas fa-check"></i>
              Aplicar
            </button>
          </div>
        </div>
      </div>
    </transition>
    
    <!-- Task Modal -->
    <transition name="modal-fade">
      <div v-if="showTaskModal" class="modal-overlay" @click="closeTaskModal">
        <div class="modal-content task-modal" @click.stop>
          <div class="modal-header">
            <h3>
              <i class="fas fa-tasks"></i> 
              {{ editingTask ? 'Editar Tarefa' : 'Nova Tarefa' }}
            </h3>
            <button class="close-btn" @click="closeTaskModal">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <div class="modal-body">
            <div class="task-form">
              <div class="form-group">
                <label>
                  <i class="fas fa-heading"></i>
                  Título da Tarefa
                </label>
                <input 
                  type="text" 
                  v-model="taskForm.title" 
                  placeholder="Ex: Revisar capítulo 3"
                  class="form-input"
                  @keyup.enter="saveTask"
                >
              </div>
              
              <div class="form-group">
                <label>
                  <i class="fas fa-align-left"></i>
                  Descrição
                </label>
                <textarea 
                  v-model="taskForm.description" 
                  placeholder="Detalhes sobre a tarefa..."
                  class="form-textarea"
                  rows="3"
                ></textarea>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label>
                    <i class="fas fa-tag"></i>
                    Categoria
                  </label>
                  <select v-model="taskForm.category" class="form-select">
                    <option>Leitura</option>
                    <option>Exercícios</option>
                    <option>Revisão</option>
                    <option>Pesquisa</option>
                    <option>Prática</option>
                  </select>
                </div>
                
                <div class="form-group">
                  <label>
                    <i class="fas fa-clock"></i>
                    Tempo Estimado
                  </label>
                  <div class="time-input-group">
                    <input 
                      type="number" 
                      v-model.number="taskForm.estimatedTime" 
                      min="0.5" 
                      step="0.5"
                      class="form-input"
                    >
                    <span class="time-unit">horas</span>
                  </div>
                </div>
              </div>
              
              <div class="form-group">
                <label>
                  <i class="fas fa-exclamation-circle"></i>
                  Prioridade
                </label>
                <div class="priority-options">
                  <label class="priority-option" v-for="priority in ['baixa', 'média', 'alta', 'urgente']" :key="priority">
                    <input 
                      type="radio" 
                      :value="priority" 
                      v-model="taskForm.priority"
                      name="priority"
                    >
                    <span :class="`priority-badge ${priority}`">
                      <i :class="getPriorityIcon(priority)"></i>
                      {{ priority }}
                    </span>
                  </label>
                </div>
              </div>
              
              <div class="form-group">
                <label>
                  <i class="fas fa-calendar"></i>
                  Prazo
                </label>
                <input 
                  type="date" 
                  v-model="taskForm.dueDate" 
                  class="form-input"
                  :min="today"
                >
              </div>
              
              <!-- Advanced Features -->
              <div class="advanced-section">
                <div class="section-divider">
                  <span>Recursos Avançados</span>
                </div>
                
                <!-- Subtasks -->
                <div class="form-group">
                  <label>
                    <i class="fas fa-list-check"></i>
                    Subtarefas
                  </label>
                  <div class="subtasks-container">
                    <div v-for="(subtask, index) in taskForm.subtasks" :key="index" class="subtask-item">
                      <input 
                        type="checkbox" 
                        v-model="subtask.completed"
                        class="subtask-checkbox"
                      >
                      <input 
                        type="text" 
                        v-model="subtask.title"
                        placeholder="Subtarefa..."
                        class="subtask-input"
                      >
                      <button @click="removeSubtask(index)" class="remove-btn">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                    <button @click="addSubtask" class="add-subtask-btn">
                      <i class="fas fa-plus"></i>
                      Adicionar Subtarefa
                    </button>
                  </div>
                </div>
                
                <!-- Tags -->
                <div class="form-group">
                  <label>
                    <i class="fas fa-tags"></i>
                    Tags
                  </label>
                  <div class="tags-container">
                    <div class="selected-tags">
                      <span v-for="(tag, index) in taskForm.tags" :key="index" class="tag-chip">
                        {{ tag }}
                        <button @click="removeTag(index)" class="tag-remove">
                          <i class="fas fa-times"></i>
                        </button>
                      </span>
                    </div>
                    <div class="tag-input-wrapper">
                      <input 
                        type="text" 
                        v-model="newTag"
                        @keyup.enter="addTag"
                        placeholder="Adicionar tag e pressionar Enter"
                        class="tag-input"
                      >
                      <div v-if="getFilteredSuggestions.length > 0" class="tag-suggestions">
                        <button 
                          v-for="tag in getFilteredSuggestions" 
                          :key="tag"
                          @click="selectTag(tag)"
                          class="tag-suggestion"
                        >
                          {{ tag }}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Recurrence -->
                <div class="form-group">
                  <label class="checkbox-label">
                    <input 
                      type="checkbox" 
                      v-model="taskForm.recurring"
                      class="form-checkbox"
                    >
                    <span>
                      <i class="fas fa-repeat"></i>
                      Tarefa Recorrente
                    </span>
                  </label>
                  <div v-if="taskForm.recurring" class="recurrence-options">
                    <select v-model="taskForm.recurrencePattern" class="form-select">
                      <option value="daily">Diariamente</option>
                      <option value="weekly">Semanalmente</option>
                      <option value="biweekly">Quinzenalmente</option>
                      <option value="monthly">Mensalmente</option>
                      <option value="custom">Personalizado</option>
                    </select>
                  </div>
                </div>
                
                <!-- Reminder -->
                <div class="form-group">
                  <label>
                    <i class="fas fa-bell"></i>
                    Lembrete
                  </label>
                  <div class="reminder-options">
                    <select v-model="taskForm.reminder" class="form-select">
                      <option :value="null">Sem lembrete</option>
                      <option value="15min">15 minutos antes</option>
                      <option value="30min">30 minutos antes</option>
                      <option value="1hour">1 hora antes</option>
                      <option value="1day">1 dia antes</option>
                      <option value="custom">Personalizado</option>
                    </select>
                  </div>
                </div>
                
                <!-- Progress Tracker -->
                <div class="form-group">
                  <label>
                    <i class="fas fa-chart-line"></i>
                    Progresso da Tarefa
                  </label>
                  <div class="progress-tracker">
                    <input 
                      type="range" 
                      v-model.number="taskForm.progress" 
                      min="0" 
                      max="100"
                      class="progress-slider"
                    >
                    <span class="progress-percent">{{ taskForm.progress }}%</span>
                  </div>
                  <div class="progress-preview">
                    <div class="progress-bar">
                      <div class="progress-fill" :style="{ width: taskForm.progress + '%' }"></div>
                    </div>
                  </div>
                </div>
                
                <!-- Notes -->
                <div class="form-group">
                  <label>
                    <i class="fas fa-sticky-note"></i>
                    Notas Adicionais
                  </label>
                  <textarea 
                    v-model="taskForm.notes"
                    placeholder="Observações, links úteis, referências..."
                    class="form-textarea"
                    rows="2"
                  ></textarea>
                </div>
              </div>
            </div>
          </div>
          
          <div class="modal-actions">
            <button class="btn-cancel" @click="closeTaskModal">
              Cancelar
            </button>
            <button class="btn-save-task" @click="saveTask" :disabled="!taskForm.title">
              <i class="fas fa-save"></i>
              {{ editingTask ? 'Atualizar' : 'Criar' }} Tarefa
            </button>
          </div>
        </div>
      </div>
    </transition>
    
    <!-- Session Timer -->
    <transition name="timer-slide">
      <div v-if="showSessionTimer" class="session-timer-container" :data-theme="sessionSettings.theme">
        <div class="timer-header">
          <button class="timer-minimize" @click.stop="showSessionTimer = false">
            <i class="fas fa-minus"></i>
          </button>
          <div class="timer-plan-info">
            <h3>{{ selectedPlanForAction?.name }}</h3>
            <p>{{ sessionFocus }}</p>
          </div>
          <button class="timer-close" @click.stop="endSession">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="timer-display">
          <div class="timer-circle">
            <svg class="timer-svg" viewBox="0 0 200 200">
              <circle 
                class="timer-background"
                cx="100" 
                cy="100" 
                r="90"
              />
              <circle 
                class="timer-progress"
                cx="100" 
                cy="100" 
                r="90"
                :stroke-dasharray="circleProgress"
                :stroke-dashoffset="circleOffset"
              />
            </svg>
            <div class="timer-text">
              <span class="timer-minutes">{{ formatTime(sessionTimeRemaining).minutes }}</span>
              <span class="timer-separator">:</span>
              <span class="timer-seconds">{{ formatTime(sessionTimeRemaining).seconds }}</span>
            </div>
          </div>
        </div>
        
        <div class="timer-controls">
          <button 
            v-if="!sessionPaused" 
            @click.stop="pauseTimer" 
            class="timer-btn pause"
          >
            <i class="fas fa-pause"></i>
            Pausar
          </button>
          <button 
            v-else 
            @click.stop="resumeTimer" 
            class="timer-btn play"
          >
            <i class="fas fa-play"></i>
            Continuar
          </button>
          <button @click.stop="skipBreak" class="timer-btn skip" v-if="isBreakTime">
            <i class="fas fa-forward"></i>
            Pular Pausa
          </button>
          <button @click.stop="endSession" class="timer-btn stop">
            <i class="fas fa-stop"></i>
            Encerrar
          </button>
        </div>
        
        <div class="session-stats">
          <div class="stat-mini">
            <i class="fas fa-graduation-cap"></i>
            <span>{{ currentPlanProgress }}% do plano concluído</span>
          </div>
          <div class="stat-mini">
            <i class="fas fa-chart-line"></i>
            <span>+{{ sessionProgress.toFixed(1) }}% nesta sessão</span>
          </div>
          <div class="stat-mini">
            <i class="fas fa-trophy"></i>
            <span>{{ remainingHours }}h restantes</span>
          </div>
        </div>
        
        <!-- Removed audio player -->
      </div>
    </transition>
    
    <!-- Minimized Timer Button -->
    <button 
      v-if="!showSessionTimer && sessionTimerInterval" 
      @click="showSessionTimer = true"
      class="timer-minimized-btn"
    >
      <i class="fas fa-clock"></i>
      {{ formatTime(sessionTimeRemaining).minutes }}:{{ formatTime(sessionTimeRemaining).seconds }}
    </button>
  </div>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import SharedCalendar from './SharedCalendar.vue'

export default {
  name: 'StudyPlanPage',
  emits: ['back', 'plan-created'],
  components: {
    SharedCalendar
  },
  setup(props, { emit }) {
    const router = useRouter()
    
    // State
    const selectedPlan = ref(null)
    const sessionView = ref('list')
    const quickNotes = ref('')
    const pomodoroTime = ref('25:00')
    const pomodoroActive = ref(false)
    const showNewPlanModal = ref(false)
    const formStep = ref(1)
    const today = new Date().toISOString().split('T')[0]
    const expandedPlans = ref([])
    const selectedTab = ref({})
    const planTasks = ref({})
    const showPlanFilters = ref(false)
    const activeFilters = ref({
      category: [],
      status: [],
      priority: []
    })
    const planCategories = ref(['Medicina', 'Biologia', 'Química', 'Física', 'Matemática'])
    const planStatuses = ref([
      { id: 'active', name: 'Em Andamento', icon: 'fas fa-play' },
      { id: 'paused', name: 'Pausado', icon: 'fas fa-pause' },
      { id: 'completed', name: 'Concluído', icon: 'fas fa-check' }
    ])
    
    // Task modal states
    const showTaskModal = ref(false)
    const editingTask = ref(null)
    const currentPlanId = ref(null)
    const taskForm = ref({
      title: '',
      description: '',
      category: 'Leitura',
      estimatedTime: 1,
      priority: 'média',
      dueDate: '',
      completed: false,
      subtasks: [],
      tags: [],
      attachments: [],
      reminder: null,
      recurring: false,
      recurrencePattern: 'daily',
      dependencies: [],
      notes: '',
      progress: 0
    })
    
    // New task-related states
    const newTag = ref('')
    const suggestedTags = ref(['Importante', 'Revisar', 'Prova', 'Exercício', 'Leitura', 'Pesquisa', 'Prática'])
    const availableDependencies = ref([])
    const taskFilter = ref('all') // all, pending, completed
    
    // Modal states
    const showShareModal = ref(false)
    const showSessionModal = ref(false)
    const shareLink = ref('')
    const selectedPlanForAction = ref(null)
    
    // Session timer states
    const showSessionTimer = ref(false)
    const sessionDuration = ref(25) // minutos
    const sessionTimeRemaining = ref(0) // segundos
    const sessionTimerInterval = ref(null)
    const sessionPaused = ref(false)
    const sessionFocus = ref('Revisão Geral')
    const sessionSettings = ref({
      breakReminders: true,
      progressMilestones: true,
      focusMode: false,
      studyStreak: true,
      theme: 'ocean',
      technique: 'pomodoro'
    })
    const isBreakTime = ref(false)
    const completedPomodoros = ref(0)
    const totalStudyTime = ref(0) // em segundos
    const focusLevel = ref(100) // percentual de foco
    const showCustomDuration = ref(false)
    const customDurationInput = ref(25) // valor padrão
    
    // Variáveis de progresso do plano
    const currentPlanProgress = ref(0)
    const sessionProgress = ref(0)
    const remainingHours = ref(0)
    const weekProgress = ref([
      { name: 'Seg', progress: 80, hours: 2 },
      { name: 'Ter', progress: 65, hours: 1.5 },
      { name: 'Qua', progress: 90, hours: 2.5 },
      { name: 'Qui', progress: 70, hours: 2 },
      { name: 'Sex', progress: 85, hours: 2.2 },
      { name: 'Sáb', progress: 40, hours: 1 },
      { name: 'Dom', progress: 60, hours: 1.5 }
    ])
    
    // Timer themes
    const timerThemes = ref([
      {
        id: 'ocean',
        name: 'Oceano',
        icon: 'fas fa-water',
        gradient: 'linear-gradient(135deg, #667eea, #764ba2)'
      },
      {
        id: 'sunset',
        name: 'Pôr do Sol',
        icon: 'fas fa-sun',
        gradient: 'linear-gradient(135deg, #f093fb, #f5576c)'
      },
      {
        id: 'forest',
        name: 'Floresta',
        icon: 'fas fa-tree',
        gradient: 'linear-gradient(135deg, #4facfe, #00f2fe)'
      },
      {
        id: 'night',
        name: 'Noturno',
        icon: 'fas fa-moon',
        gradient: 'linear-gradient(135deg, #30cfd0, #330867)'
      }
    ])
    
    // Study techniques
    const studyTechniques = ref([
      {
        id: 'pomodoro',
        name: 'Pomodoro Clássico',
        icon: 'fas fa-clock',
        description: '25min estudo + 5min pausa'
      },
      {
        id: 'flowtime',
        name: 'Flowtime',
        icon: 'fas fa-infinity',
        description: 'Pausas flexíveis quando necessário'
      },
      {
        id: 'timeboxing',
        name: 'Timeboxing',
        icon: 'fas fa-box',
        description: 'Blocos fixos sem interrupções'
      },
      {
        id: 'deepwork',
        name: 'Deep Work',
        icon: 'fas fa-brain',
        description: '90min de foco profundo'
      }
    ])
    
    // New Plan Form
    const newPlan = ref({
      name: '',
      description: '',
      category: '',
      startDate: '',
      deadline: '',
      estimatedTime: '',
      difficulty: 'Médio',
      color: '#667eea',
      icon: 'fas fa-book',
      objectives: [{ text: '' }],
      notifications: {
        enabled: true,
        daily: true,
        deadlineAlert: true
      }
    })
    
    const planColors = ref([
      '#667eea',
      '#f093fb',
      '#4facfe',
      '#fa709a',
      '#fee140',
      '#30cfd0',
      '#764ba2',
      '#f5576c'
    ])
    
    const planIcons = ref([
      'fas fa-book',
      'fas fa-user-md',
      'fas fa-brain',
      'fas fa-pills',
      'fas fa-microscope',
      'fas fa-dna',
      'fas fa-heartbeat',
      'fas fa-stethoscope',
      'fas fa-atom',
      'fas fa-flask',
      'fas fa-laptop-medical',
      'fas fa-notes-medical'
    ])
    
    // Mock data
    const totalSubjects = ref(8)
    const studyHours = ref(4.5)
    const efficiency = ref(87)
    const streakDays = ref(15)
    const completedGoals = ref(23)
    const retentionRate = ref(92)
    
    const activePlans = ref([
      {
        id: 1,
        name: 'Anatomia Sistêmica',
        description: 'Revisão completa dos sistemas corporais',
        icon: 'fas fa-user-md',
        color: 'linear-gradient(135deg, #667eea, #764ba2)',
        completedTasks: 45,
        totalTasks: 68,
        estimatedTime: 120,
        deadline: '15 Dez',
        progress: 66
      },
      {
        id: 2,
        name: 'Farmacologia Clínica',
        description: 'Medicamentos e interações',
        icon: 'fas fa-pills',
        color: 'linear-gradient(135deg, #f093fb, #f5576c)',
        completedTasks: 23,
        totalTasks: 50,
        estimatedTime: 80,
        deadline: '20 Dez',
        progress: 46
      },
      {
        id: 3,
        name: 'Neurociências',
        description: 'Sistema nervoso e funções',
        icon: 'fas fa-brain',
        color: 'linear-gradient(135deg, #4facfe, #00f2fe)',
        completedTasks: 60,
        totalTasks: 65,
        estimatedTime: 150,
        deadline: '10 Dez',
        progress: 92
      }
    ])
    
    const todaySessions = ref([
      {
        id: 1,
        startTime: '08:00',
        endTime: '09:30',
        subject: 'Anatomia',
        topic: 'Sistema Cardiovascular',
        tags: ['Revisão', 'Importante'],
        status: 'completed'
      },
      {
        id: 2,
        startTime: '10:00',
        endTime: '11:00',
        subject: 'Farmacologia',
        topic: 'Antibióticos Beta-lactâmicos',
        tags: ['Nova matéria'],
        status: 'in-progress'
      },
      {
        id: 3,
        startTime: '14:00',
        endTime: '15:30',
        subject: 'Neurociências',
        topic: 'Vias Sensoriais',
        tags: ['Prática'],
        status: 'pending'
      }
    ])
    
    const aiInsights = ref([
      {
        id: 1,
        type: 'suggestion',
        icon: 'fas fa-lightbulb',
        title: 'Otimização de Horário',
        message: 'Baseado no seu desempenho, sugiro estudar Anatomia pela manhã.',
        actionText: 'Aplicar',
        action: () => console.log('Apply suggestion')
      },
      {
        id: 2,
        type: 'warning',
        icon: 'fas fa-exclamation-triangle',
        title: 'Revisão Pendente',
        message: 'Você tem 5 tópicos de Farmacologia para revisar esta semana.',
        actionText: 'Ver tópicos',
        action: () => console.log('View topics')
      },
      {
        id: 3,
        type: 'achievement',
        icon: 'fas fa-trophy',
        title: 'Meta Alcançada',
        message: 'Parabéns! Você completou 80% do plano de Neurociências.',
        actionText: 'Ver progresso',
        action: () => console.log('View progress')
      }
    ])
    
    const resources = ref([
      { id: 1, name: 'Biblioteca Digital', icon: 'fas fa-book', url: '#' },
      { id: 2, name: 'Videoaulas', icon: 'fas fa-video', url: '#' },
      { id: 3, name: 'Flashcards', icon: 'fas fa-clone', url: '#' },
      { id: 4, name: 'Simulados', icon: 'fas fa-tasks', url: '#' }
    ])
    
    // Calendar data
    const selectedCalendarDate = ref(null)
    const calendarEvents = computed(() => {
      // Integrar eventos de todos os planos ativos
      const events = []
      
      activePlans.value.forEach(plan => {
        // Adicionar eventos de deadline
        if (plan.deadline) {
          events.push({
            id: `deadline-${plan.id}`,
            date: plan.deadline,
            title: `Prazo: ${plan.name}`,
            color: plan.color || '#667eea',
            type: 'deadline'
          })
        }
        
        // Adicionar sessões de estudo programadas
        if (plan.studySessions) {
          plan.studySessions.forEach(session => {
            events.push({
              id: `session-${session.id}`,
              date: session.date,
              start: session.start,
              end: session.end,
              title: `${plan.name}: ${session.topic}`,
              color: plan.color || '#667eea',
              type: 'study-session'
            })
          })
        }
      })
      
      // Adicionar eventos das sessões de hoje
      todaySessions.value.forEach(session => {
        events.push({
          id: `today-session-${session.id}`,
          date: new Date(),
          start: session.startTime,
          end: session.endTime,
          title: session.subject,
          color: '#4facfe',
          type: 'session'
        })
      })
      
      return events
    })
    
    // Methods
    const selectPlan = (plan) => {
      selectedPlan.value = plan
    }
    
    const createNewPlan = () => {
      showNewPlanModal.value = true
      formStep.value = 1
      // Initialize with today's date if not set
      if (!newPlan.value.startDate) {
        newPlan.value.startDate = today
      }
    }
    
    // Auto-show form on mount
    onMounted(() => {
      createNewPlan()
    })
    
    const submitNewPlan = () => {
      // Validate form before proceeding
      if (!isFormValid.value) {
        alert('Por favor, preencha todos os campos obrigatórios antes de criar o plano.')
        return
      }
      
      try {
        console.log('Starting plan creation...')
        console.log('Form data:', newPlan.value)
        
        // Filter valid objectives
        const validObjectives = newPlan.value.objectives.filter(obj => obj.text && obj.text.trim())
        console.log('Valid objectives:', validObjectives)
        
        // Test color adjustment
        console.log('Color:', newPlan.value.color)
        const adjustedColor = adjustColor(newPlan.value.color, -20)
        console.log('Adjusted color:', adjustedColor)
        
        // Test date formatting
        console.log('Deadline:', newPlan.value.deadline)
        const formattedDeadline = formatDateShort(newPlan.value.deadline)
        console.log('Formatted deadline:', formattedDeadline)
        
        // Create plan with all form data
        const newPlanData = {
          id: Date.now(),
          name: newPlan.value.name,
          description: newPlan.value.description,
          category: newPlan.value.category,
          icon: newPlan.value.icon,
          color: `linear-gradient(135deg, ${newPlan.value.color}, ${adjustedColor})`,
          completedTasks: 0,
          totalTasks: validObjectives.length > 0 ? validObjectives.length * 3 : 10,
          estimatedTime: parseInt(newPlan.value.estimatedTime),
          startDate: newPlan.value.startDate,
          deadline: formattedDeadline,
          difficulty: newPlan.value.difficulty,
          objectives: validObjectives,
          notifications: { ...newPlan.value.notifications },
          progress: 0
        }
        
        console.log('New plan data created:', newPlanData)
        
        // Add to active plans - Force Vue reactivity
        activePlans.value = [...activePlans.value, newPlanData]
        console.log('Plan added to active plans')
        
        // Close modal
        showNewPlanModal.value = false
        
        // Reset form
        resetNewPlanForm()
        
        // Select the new plan
        selectedPlan.value = newPlanData
        
        // Show success message
        alert('Plano criado com sucesso!')
        
        // Emit event to parent
        emit('plan-created')
        
      } catch (error) {
        console.error('❌ Error creating plan:', error)
        console.error('Error details:', error.message)
        console.error('Error stack:', error.stack)
        alert(`Erro ao criar o plano: ${error.message}`)
      }
    }
    
    const resetNewPlanForm = () => {
      newPlan.value = {
        name: '',
        description: '',
        category: '',
        startDate: '',
        deadline: '',
        estimatedTime: '',
        difficulty: 'Médio',
        color: '#667eea',
        icon: 'fas fa-book',
        objectives: [{ text: '' }],
        notifications: {
          enabled: true,
          daily: true,
          deadlineAlert: true
        }
      }
      formStep.value = 1
    }
    
    const nextStep = () => {
      if (canProceed.value) {
        formStep.value++
      }
    }
    
    const previousStep = () => {
      formStep.value--
    }
    
    const cancelPlanCreation = () => {
      resetNewPlanForm()
      showNewPlanModal.value = false
      // Emit event to parent
      emit('back')
    }
    
    const navigateBack = () => {
      emit('back')
    }
    
    const addObjective = () => {
      newPlan.value.objectives.push({ text: '' })
    }
    
    const removeObjective = (index) => {
      if (newPlan.value.objectives.length > 1) {
        newPlan.value.objectives.splice(index, 1)
      }
    }
    
    const updatePlanPreview = () => {
      // Trigger preview update
    }
    
    const getDifficultyIcon = (level) => {
      const icons = {
        'Fácil': 'fas fa-seedling',
        'Médio': 'fas fa-fire',
        'Difícil': 'fas fa-mountain'
      }
      return icons[level] || 'fas fa-fire'
    }
    
    const formatDate = (date) => {
      if (!date) return 'Sem prazo'
      return new Date(date).toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: 'long',
        year: 'numeric'
      })
    }
    
    const formatDateShort = (date) => {
      try {
        if (!date) return ''
        const d = new Date(date)
        if (isNaN(d.getTime())) {
          return '' // Return empty string for invalid dates
        }
        return `${d.getDate()} ${d.toLocaleDateString('pt-BR', { month: 'short' })}`
      } catch (error) {
        console.error('Error in formatDateShort:', error)
        return ''
      }
    }
    
    const adjustColor = (color, amount) => {
      try {
        let usePound = false
        
        if (!color || typeof color !== 'string') {
          return '#667eea' // Default color
        }
        
        if (color[0] === "#") {
          color = color.slice(1)
          usePound = true
        }
        
        const num = parseInt(color, 16)
        if (isNaN(num)) {
          return '#667eea' // Default color if parsing fails
        }
        
        let r = (num >> 16) + amount
        
        if (r > 255) r = 255
        else if (r < 0) r = 0
        
        let b = ((num >> 8) & 0x00FF) + amount
        
        if (b > 255) b = 255
        else if (b < 0) b = 0
        
        let g = (num & 0x0000FF) + amount
        
        if (g > 255) g = 255
        else if (g < 0) g = 0
        
        return (usePound ? "#" : "") + (g | (b << 8) | (r << 16)).toString(16).padStart(6, '0')
      } catch (error) {
        console.error('Error in adjustColor:', error)
        return '#667eea' // Default color on error
      }
    }
    
    // Computed
    const canProceed = computed(() => {
      if (formStep.value === 1) {
        return newPlan.value.name && newPlan.value.description && newPlan.value.category
      } else if (formStep.value === 2) {
        return newPlan.value.startDate && newPlan.value.deadline && newPlan.value.estimatedTime && newPlan.value.difficulty
      }
      return true
    })
    
    const isFormValid = computed(() => {
      return newPlan.value.name && 
             newPlan.value.description && 
             newPlan.value.category &&
             newPlan.value.startDate &&
             newPlan.value.deadline && 
             newPlan.value.estimatedTime &&
             newPlan.value.difficulty
    })
    
    // Computed properties for quick stats
    const totalHoursToday = computed(() => {
      const history = JSON.parse(localStorage.getItem('studyHistory') || '[]')
      const today = new Date().toDateString()
      return history
        .filter(s => new Date(s.date).toDateString() === today)
        .reduce((acc, s) => acc + (s.duration / 60), 0)
        .toFixed(1)
    })
    
    const tasksCompletedToday = computed(() => {
      const tasks = Object.values(planTasks.value).flat()
      const today = new Date().toDateString()
      return tasks.filter(t => t.completed && new Date(t.completedDate).toDateString() === today).length
    })
    
    const averageProgress = computed(() => {
      if (activePlans.value.length === 0) return 0
      const total = activePlans.value.reduce((acc, plan) => acc + (plan.progress || 0), 0)
      return Math.round(total / activePlans.value.length)
    })
    
    const filteredPlans = computed(() => {
      let plans = [...activePlans.value]
      
      // Filter by category
      if (activeFilters.value.category.length > 0) {
        plans = plans.filter(p => activeFilters.value.category.includes(p.category))
      }
      
      // Filter by status
      if (activeFilters.value.status.length > 0) {
        plans = plans.filter(p => {
          if (activeFilters.value.status.includes('completed')) {
            return p.progress === 100
          }
          if (activeFilters.value.status.includes('paused')) {
            return p.paused
          }
          if (activeFilters.value.status.includes('active')) {
            return p.progress < 100 && !p.paused
          }
          return true
        })
      }
      
      return plans
    })
    
    // Timer computed properties
    const circleProgress = computed(() => {
      const circumference = 2 * Math.PI * 90
      return `${circumference} ${circumference}`
    })
    
    const circleOffset = computed(() => {
      const circumference = 2 * Math.PI * 90
      const totalSeconds = sessionDuration.value * 60
      const progress = (totalSeconds - sessionTimeRemaining.value) / totalSeconds
      return circumference * (1 - progress)
    })
    
    const editPlan = (plan) => {
      // TODO: Implementar edição de plano
      // Por enquanto, vamos abrir o modal de criação com os dados do plano
      newPlan.value = { ...plan }
      formStep.value = 1
      showNewPlanModal.value = true
    }
    
    const startSession = (session) => {
      session.status = 'in-progress'
    }
    
    const pauseSession = (session) => {
      session.status = 'paused'
    }
    
    const refreshInsights = () => {
      console.log('Refresh insights')
    }
    
    const handleDateSelected = (date) => {
      selectedCalendarDate.value = date
      // Aqui você pode adicionar lógica adicional quando uma data é selecionada
      // Por exemplo, mostrar eventos do dia, criar nova sessão de estudo, etc.
    }
    
    const handleMonthChanged = (newMonth) => {
      // Aqui você pode adicionar lógica quando o mês é alterado
      // Por exemplo, carregar eventos do novo mês
    }
    
    const startPomodoro = () => {
      pomodoroActive.value = true
    }
    
    const pausePomodoro = () => {
      pomodoroActive.value = false
    }
    
    const resetPomodoro = () => {
      pomodoroTime.value = '25:00'
      pomodoroActive.value = false
    }
    
    const saveNotes = () => {
      console.log('Save notes:', quickNotes.value)
    }
    
    const toggleQuickMenu = () => {
      console.log('Toggle quick menu')
    }
    
    // New methods for plan details
    const togglePlanDetails = (planId) => {
      const index = expandedPlans.value.indexOf(planId)
      if (index > -1) {
        expandedPlans.value.splice(index, 1)
      } else {
        expandedPlans.value.push(planId)
        // Initialize tab if not set
        if (!selectedTab.value[planId]) {
          selectedTab.value[planId] = 'Visão Geral'
        }
      }
    }
    
    const selectTab = (planId, tab) => {
      selectedTab.value[planId] = tab
    }
    
    const getTabIcon = (tab) => {
      const icons = {
        'Visão Geral': 'fas fa-eye',
        'Tarefas': 'fas fa-tasks',
        'Estatísticas': 'fas fa-chart-bar',
        'Linha do Tempo': 'fas fa-stream'
      }
      return icons[tab] || 'fas fa-circle'
    }
    
    const getPlanTasks = (planId) => {
      return planTasks.value[planId] || []
    }
    
    const addNewTask = (planId) => {
      console.log('addNewTask called with planId:', planId)
      currentPlanId.value = planId
      editingTask.value = null
      
      // Reset form with all fields
      taskForm.value = {
        title: '',
        description: '',
        category: 'Leitura',
        estimatedTime: 1,
        priority: 'média',
        dueDate: '',
        completed: false,
        subtasks: [],
        tags: [],
        attachments: [],
        reminder: null,
        recurring: false,
        recurrencePattern: 'daily',
        dependencies: [],
        notes: '',
        progress: 0
      }
      
      showTaskModal.value = true
    }
    
    const editTask = (planId, task) => {
      currentPlanId.value = planId
      editingTask.value = task
      
      // Load task data into form with all fields
      taskForm.value = {
        title: task.title,
        description: task.description || '',
        category: task.category || 'Leitura',
        estimatedTime: task.estimatedTime || 1,
        priority: task.priority || 'média',
        dueDate: task.dueDate || '',
        completed: task.completed || false,
        subtasks: task.subtasks || [],
        tags: task.tags || [],
        attachments: task.attachments || [],
        reminder: task.reminder || null,
        recurring: task.recurring || false,
        recurrencePattern: task.recurrencePattern || 'daily',
        dependencies: task.dependencies || [],
        notes: task.notes || '',
        progress: task.progress || 0
      }
      
      showTaskModal.value = true
    }
    
    const saveTask = () => {
      console.log('saveTask called')
      console.log('currentPlanId:', currentPlanId.value)
      console.log('taskForm:', taskForm.value)
      
      if (!taskForm.value.title.trim()) {
        console.log('Title is empty, returning')
        return
      }
      
      if (!currentPlanId.value) {
        console.error('No plan ID selected!')
        if (window.$toast) {
          window.$toast.error('Erro: Nenhum plano selecionado')
        }
        return
      }
      
      // Initialize tasks array if not exists
      if (!planTasks.value[currentPlanId.value]) {
        planTasks.value[currentPlanId.value] = []
      }
      
      // Calculate subtask progress
      const subtaskProgress = taskForm.value.subtasks.length > 0
        ? (taskForm.value.subtasks.filter(s => s.completed).length / taskForm.value.subtasks.length) * 100
        : taskForm.value.progress
      
      const taskData = {
        ...taskForm.value,
        progress: subtaskProgress,
        completedSubtasks: taskForm.value.subtasks.filter(s => s.completed).length,
        totalSubtasks: taskForm.value.subtasks.length
      }
      
      if (editingTask.value) {
        // Update existing task
        const taskIndex = planTasks.value[currentPlanId.value].findIndex(t => t.id === editingTask.value.id)
        if (taskIndex !== -1) {
          planTasks.value[currentPlanId.value][taskIndex] = {
            ...editingTask.value,
            ...taskData,
            updatedAt: new Date().toISOString()
          }
        }
      } else {
        // Create new task
        const newTask = {
          id: Date.now(),
          ...taskData,
          createdAt: new Date().toISOString()
        }
        planTasks.value[currentPlanId.value].push(newTask)
        
        // If recurring, create future instances
        if (taskForm.value.recurring) {
          createRecurringTasks(newTask, currentPlanId.value)
        }
      }
      
      // Update plan progress based on tasks
      updatePlanProgress(currentPlanId.value)
      
      // Save to localStorage
      localStorage.setItem('planTasks', JSON.stringify(planTasks.value))
      
      // Set reminder if configured
      if (taskForm.value.reminder) {
        scheduleTaskReminder(taskData)
      }
      
      console.log('Task saved successfully!')
      console.log('Updated planTasks:', planTasks.value)
      console.log('Tasks for current plan:', planTasks.value[currentPlanId.value])
      
      // Force reactivity update
      planTasks.value = { ...planTasks.value }
      
      // Add notification to notification center
      if (!editingTask.value && store) {
        const plan = studyPlans.value.find(p => p.id === currentPlanId.value)
        store.dispatch('notifications/addTaskNotification', {
          taskName: taskData.title,
          planName: plan ? plan.title : 'Plano de Estudos'
        })
      }
      
      if (window.$toast) {
        window.$toast.success(editingTask.value ? 'Tarefa atualizada!' : 'Tarefa criada!')
      } else {
        alert(editingTask.value ? 'Tarefa atualizada com sucesso!' : 'Tarefa criada com sucesso!')
      }
      
      closeTaskModal()
    }
    
    const deleteTask = (planId, taskId) => {
      if (!confirm('Deseja excluir esta tarefa?')) return
      
      const tasks = planTasks.value[planId] || []
      planTasks.value[planId] = tasks.filter(t => t.id !== taskId)
      
      // Save to localStorage
      localStorage.setItem('planTasks', JSON.stringify(planTasks.value))
      
      if (window.$toast) {
        window.$toast.info('Tarefa excluída')
      }
    }
    
    const closeTaskModal = () => {
      showTaskModal.value = false
      editingTask.value = null
      currentPlanId.value = null
      // Reset form
      taskForm.value = {
        title: '',
        description: '',
        category: 'Leitura',
        estimatedTime: 1,
        priority: 'média',
        dueDate: '',
        completed: false,
        subtasks: [],
        tags: [],
        attachments: [],
        reminder: null,
        recurring: false,
        recurrencePattern: 'daily',
        dependencies: [],
        notes: '',
        progress: 0
      }
      newTag.value = ''
    }
    
    // Advanced task methods
    const addSubtask = () => {
      taskForm.value.subtasks.push({
        title: '',
        completed: false,
        id: Date.now()
      })
    }
    
    const removeSubtask = (index) => {
      taskForm.value.subtasks.splice(index, 1)
    }
    
    const addTag = () => {
      if (newTag.value.trim() && !taskForm.value.tags.includes(newTag.value.trim())) {
        taskForm.value.tags.push(newTag.value.trim())
        newTag.value = ''
      }
    }
    
    const removeTag = (index) => {
      taskForm.value.tags.splice(index, 1)
    }
    
    const selectTag = (tag) => {
      if (!taskForm.value.tags.includes(tag)) {
        taskForm.value.tags.push(tag)
      }
      newTag.value = ''
    }
    
    const getFilteredSuggestions = computed(() => {
      if (!newTag.value) return []
      return suggestedTags.value.filter(tag => 
        tag.toLowerCase().includes(newTag.value.toLowerCase()) &&
        !taskForm.value.tags.includes(tag)
      )
    })
    
    // Helper methods for advanced features
    const createRecurringTasks = (baseTask, planId) => {
      const pattern = baseTask.recurrencePattern
      const dates = []
      const today = new Date()
      
      // Generate dates based on pattern
      for (let i = 1; i <= 10; i++) { // Create next 10 occurrences
        const date = new Date(today)
        
        switch (pattern) {
          case 'daily':
            date.setDate(date.getDate() + i)
            break
          case 'weekly':
            date.setDate(date.getDate() + (i * 7))
            break
          case 'biweekly':
            date.setDate(date.getDate() + (i * 14))
            break
          case 'monthly':
            date.setMonth(date.getMonth() + i)
            break
        }
        
        dates.push(date)
      }
      
      // Create tasks for each date
      dates.forEach((date, index) => {
        const recurringTask = {
          ...baseTask,
          id: Date.now() + index + 1,
          title: `${baseTask.title} (${date.toLocaleDateString('pt-BR')})`,
          dueDate: date.toISOString().split('T')[0],
          completed: false,
          progress: 0,
          parentTaskId: baseTask.id,
          createdAt: new Date().toISOString()
        }
        
        planTasks.value[planId].push(recurringTask)
      })
    }
    
    const scheduleTaskReminder = (task) => {
      if (!task.dueDate || !task.reminder) return
      
      const dueDate = new Date(task.dueDate)
      const now = new Date()
      let reminderTime = new Date(dueDate)
      
      // Calculate reminder time
      switch (task.reminder) {
        case '15min':
          reminderTime.setMinutes(reminderTime.getMinutes() - 15)
          break
        case '30min':
          reminderTime.setMinutes(reminderTime.getMinutes() - 30)
          break
        case '1hour':
          reminderTime.setHours(reminderTime.getHours() - 1)
          break
        case '1day':
          reminderTime.setDate(reminderTime.getDate() - 1)
          break
      }
      
      const timeUntilReminder = reminderTime - now
      
      if (timeUntilReminder > 0) {
        setTimeout(() => {
          if (window.Notification && Notification.permission === 'granted') {
            new Notification('Lembrete de Tarefa', {
              body: `"${task.title}" está próxima do prazo!`,
              icon: '/favicon.ico',
              tag: `task-${task.id}`
            })
          }
          
          if (window.$toast) {
            window.$toast.warning(`Lembrete: "${task.title}" está próxima do prazo!`)
          }
        }, timeUntilReminder)
      }
    }
    
    const updatePlanProgress = (planId) => {
      const tasks = planTasks.value[planId] || []
      if (tasks.length === 0) return
      
      const totalProgress = tasks.reduce((sum, task) => sum + (task.progress || 0), 0)
      const averageProgress = Math.round(totalProgress / tasks.length)
      
      const plan = activePlans.value.find(p => p.id === planId)
      if (plan) {
        plan.progress = averageProgress
        plan.completedTasks = tasks.filter(t => t.completed || t.progress === 100).length
        plan.totalTasks = tasks.length
        
        // Save updated plans
        localStorage.setItem('activePlans', JSON.stringify(activePlans.value))
      }
    }
    
    const getPriorityIcon = (priority) => {
      const icons = {
        'baixa': 'fas fa-arrow-down',
        'média': 'fas fa-minus',
        'alta': 'fas fa-arrow-up',
        'urgente': 'fas fa-exclamation'
      }
      return icons[priority] || 'fas fa-minus'
    }
    
    const getFilteredTasks = (planId) => {
      const tasks = planTasks.value[planId] || []
      
      switch (taskFilter.value) {
        case 'pending':
          return tasks.filter(t => !t.completed)
        case 'completed':
          return tasks.filter(t => t.completed)
        default:
          return tasks
      }
    }
    
    const setTaskFilter = (filter) => {
      taskFilter.value = filter
    }
    
    const toggleTask = (planId, taskId) => {
      const tasks = planTasks.value[planId] || []
      const task = tasks.find(t => t.id === taskId)
      if (task) {
        task.completed = !task.completed
      }
    }
    
    const getStudyHours = (planId) => {
      // Mock data - in real app, calculate from actual study sessions
      return Math.floor(Math.random() * 40) + 10
    }
    
    const getEfficiency = (planId) => {
      // Mock data - in real app, calculate from actual performance
      return Math.floor(Math.random() * 30) + 70
    }
    
    const getStreak = (planId) => {
      // Mock data - in real app, calculate from actual study days
      return Math.floor(Math.random() * 15) + 1
    }
    
    const getPlanTimeline = (planId) => {
      const plan = activePlans.value.find(p => p.id === planId)
      if (!plan) return []
      
      const events = []
      
      // Evento de criação do plano
      if (plan.startDate) {
        events.push({
          id: `start-${planId}`,
          type: 'start',
          icon: 'fas fa-rocket',
          title: 'Plano Criado',
          description: `Plano "${plan.name}" foi criado e iniciado`,
          date: new Date(plan.startDate)
        })
      }
      
      // Buscar sessões de estudo do histórico
      const history = JSON.parse(localStorage.getItem('studyHistory') || '[]')
      const planSessions = history.filter(session => session.planId === planId)
      
      // Adicionar sessões de estudo na timeline
      planSessions.forEach((session, index) => {
        events.push({
          id: `session-${planId}-${index}`,
          type: 'study',
          icon: 'fas fa-book-reader',
          title: `Sessão de Estudo`,
          description: `${session.duration} minutos estudados • ${session.completedPomodoros || 0} pomodoros • +${(session.progressAdded || 0).toFixed(1)}% progresso`,
          date: new Date(session.date)
        })
      })
      
      // Marcos de progresso
      if (plan.progress >= 25 && plan.milestones?.milestone25Date) {
        events.push({
          id: `milestone-25-${planId}`,
          type: 'milestone',
          icon: 'fas fa-star',
          title: '25% Concluído',
          description: 'Primeiro quarto do plano completado!',
          date: new Date(plan.milestones.milestone25Date)
        })
      }
      
      if (plan.progress >= 50 && plan.milestones?.milestone50Date) {
        events.push({
          id: `milestone-50-${planId}`,
          type: 'milestone',
          icon: 'fas fa-star-half-alt',
          title: '50% Concluído',
          description: 'Metade do caminho percorrido!',
          date: new Date(plan.milestones.milestone50Date)
        })
      }
      
      if (plan.progress >= 75 && plan.milestones?.milestone75Date) {
        events.push({
          id: `milestone-75-${planId}`,
          type: 'milestone',
          icon: 'fas fa-medal',
          title: '75% Concluído',
          description: 'Quase lá! Último quarto do plano.',
          date: new Date(plan.milestones.milestone75Date)
        })
      }
      
      if (plan.progress === 100 && plan.milestones?.completionDate) {
        events.push({
          id: `complete-${planId}`,
          type: 'complete',
          icon: 'fas fa-trophy',
          title: 'Plano Concluído!',
          description: 'Parabéns! Você completou todo o plano de estudos.',
          date: new Date(plan.milestones.completionDate)
        })
      }
      
      // Adicionar tarefas completadas
      const tasks = planTasks.value[planId] || []
      tasks.filter(task => task.completed).forEach((task, index) => {
        events.push({
          id: `task-${planId}-${task.id}`,
          type: 'task',
          icon: 'fas fa-check-circle',
          title: 'Tarefa Concluída',
          description: task.title || task.description,
          date: task.completedDate || new Date() // Usar data real quando disponível
        })
      })
      
      // Adicionar objetivos alcançados
      if (plan.objectives) {
        plan.objectives.forEach((objective, index) => {
          if (objective.completed) {
            events.push({
              id: `objective-${planId}-${index}`,
              type: 'goal',
              icon: 'fas fa-bullseye',
              title: 'Objetivo Alcançado',
              description: objective.text,
              date: objective.completedDate || new Date()
            })
          }
        })
      }
      
      // Ordenar eventos por data (mais recentes primeiro)
      events.sort((a, b) => new Date(b.date) - new Date(a.date))
      
      return events
    }
    
    const exportPlan = (planId) => {
      const plan = activePlans.value.find(p => p.id === planId)
      if (!plan) return
      
      // Criar objeto com dados do plano para exportação
      const exportData = {
        plan: {
          name: plan.name,
          description: plan.description,
          objectives: plan.objectives,
          startDate: plan.startDate,
          deadline: plan.deadline,
          estimatedTime: plan.estimatedTime,
          difficulty: plan.difficulty,
          progress: plan.progress
        },
        tasks: planTasks.value[planId] || [],
        exportDate: new Date().toISOString(),
        version: '1.0'
      }
      
      // Converter para JSON
      const jsonData = JSON.stringify(exportData, null, 2)
      
      // Criar blob e download
      const blob = new Blob([jsonData], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `plano-estudo-${plan.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      // Feedback visual
      if (window.$toast) {
        window.$toast.success('Plano exportado com sucesso!')
      }
    }
    
    const sharePlan = (planId) => {
      const plan = activePlans.value.find(p => p.id === planId)
      if (!plan) return
      
      selectedPlanForAction.value = plan
      
      // Gerar link de compartilhamento (simulado)
      const baseUrl = window.location.origin
      const shareId = btoa(`${planId}-${Date.now()}`).substring(0, 8)
      shareLink.value = `${baseUrl}/shared/plan/${shareId}`
      
      showShareModal.value = true
    }
    
    const copyShareLink = () => {
      navigator.clipboard.writeText(shareLink.value).then(() => {
        if (window.$toast) {
          window.$toast.success('Link copiado para a área de transferência!')
        }
      }).catch(() => {
        if (window.$toast) {
          window.$toast.error('Erro ao copiar link')
        }
      })
    }
    
    const startStudySession = (planId) => {
      const plan = activePlans.value.find(p => p.id === planId)
      if (!plan) return
      
      selectedPlanForAction.value = plan
      showSessionModal.value = true
    }
    
    const confirmStartSession = () => {
      if (selectedPlanForAction.value) {
        // Inicializar progresso do plano
        const plan = selectedPlanForAction.value
        currentPlanProgress.value = plan.progress || 0
        sessionProgress.value = 0
        
        // Calcular horas restantes
        const totalHours = plan.estimatedTime || 0
        const hoursCompleted = Math.floor((totalHours * currentPlanProgress.value) / 100)
        remainingHours.value = Math.max(0, totalHours - hoursCompleted)
        
        // Salvar sessão no localStorage
        const session = {
          planId: selectedPlanForAction.value.id,
          planName: selectedPlanForAction.value.name,
          startTime: new Date().toISOString(),
          duration: sessionDuration.value,
          focus: sessionFocus.value,
          settings: sessionSettings.value,
          status: 'active',
          initialProgress: currentPlanProgress.value
        }
        
        localStorage.setItem('activeStudySession', JSON.stringify(session))
        
        // Iniciar timer
        sessionTimeRemaining.value = sessionDuration.value * 60
        totalStudyTime.value = 0
        completedPomodoros.value = 0
        isBreakTime.value = false
        showSessionModal.value = false
        showSessionTimer.value = true
        startSessionTimer()
        
        // Aplicar configurações da sessão
        if (sessionSettings.value.focusMode) {
          // Modo foco: pode adicionar lógica para esconder elementos distrativos
          document.body.classList.add('focus-mode')
        }
        
        if (window.$toast) {
          window.$toast.success(`Sessão iniciada: ${selectedPlanForAction.value.name}`)
        }
      }
    }
    
    // Timer methods
    const formatTime = (seconds) => {
      const mins = Math.floor(seconds / 60)
      const secs = seconds % 60
      return {
        minutes: mins.toString().padStart(2, '0'),
        seconds: secs.toString().padStart(2, '0')
      }
    }
    
    const startSessionTimer = () => {
      sessionPaused.value = false
      sessionTimerInterval.value = setInterval(() => {
        if (!sessionPaused.value && sessionTimeRemaining.value > 0) {
          sessionTimeRemaining.value--
          
          // Atualizar tempo total de estudo
          if (!isBreakTime.value) {
            totalStudyTime.value++
            
            // Calcular progresso da sessão em tempo real
            const plan = selectedPlanForAction.value
            if (plan && plan.estimatedTime) {
              const totalMinutes = plan.estimatedTime * 60 // converter horas para minutos
              const sessionMinutes = totalStudyTime.value / 60
              const progressIncrement = (sessionMinutes / totalMinutes) * 100
              sessionProgress.value = parseFloat(Math.min(progressIncrement, 100 - currentPlanProgress.value).toFixed(2))
              
              // Atualizar horas restantes
              const totalHoursCompleted = ((currentPlanProgress.value + sessionProgress.value) / 100) * plan.estimatedTime
              remainingHours.value = Math.max(0, (plan.estimatedTime - totalHoursCompleted).toFixed(1))
            }
          }
          
          // Verificar se chegou ao fim
          if (sessionTimeRemaining.value === 0) {
            handleTimerComplete()
          }
          
          // Notificações inteligentes de pausa
          if (!isBreakTime.value && sessionSettings.value.breakReminders) {
            checkSmartBreakReminder()
          }
          
          // Celebrar marcos de progresso
          if (sessionSettings.value.progressMilestones) {
            checkProgressMilestone()
          }
        }
      }, 1000)
    }
    
    const pauseTimer = () => {
      console.log('Pausando timer...')
      sessionPaused.value = true
    }
    
    const resumeTimer = () => {
      sessionPaused.value = false
    }
    
    const endSession = () => {
      console.log('Encerrando sessão...')
      if (sessionTimerInterval.value) {
        clearInterval(sessionTimerInterval.value)
        sessionTimerInterval.value = null
      }
      
      // Remover modo foco se estiver ativo
      if (sessionSettings.value.focusMode) {
        document.body.classList.remove('focus-mode')
      }
      
      // Calcular progresso final baseado no tempo real estudado
      if (selectedPlanForAction.value && totalStudyTime.value > 0) {
        const plan = activePlans.value.find(p => p.id === selectedPlanForAction.value.id)
        if (plan && plan.estimatedTime) {
          // Recalcular o progresso baseado no tempo total estudado
          const totalMinutes = plan.estimatedTime * 60
          const studiedMinutes = totalStudyTime.value / 60
          const actualProgress = (studiedMinutes / totalMinutes) * 100
          
          // Usar o progresso real calculado, não apenas o sessionProgress
          const progressToAdd = Math.min(actualProgress, 100 - currentPlanProgress.value)
          
          // Atualizar progresso do plano
          const previousProgress = plan.progress || 0
          plan.progress = Math.min(100, previousProgress + progressToAdd)
          
          // Salvar marcos de progresso
          if (!plan.milestones) plan.milestones = {}
          
          if (previousProgress < 25 && plan.progress >= 25) {
            plan.milestones.milestone25Date = new Date().toISOString()
          }
          if (previousProgress < 50 && plan.progress >= 50) {
            plan.milestones.milestone50Date = new Date().toISOString()
          }
          if (previousProgress < 75 && plan.progress >= 75) {
            plan.milestones.milestone75Date = new Date().toISOString()
          }
          if (previousProgress < 100 && plan.progress === 100) {
            plan.milestones.completionDate = new Date().toISOString()
          }
          
          // Atualizar tarefas completadas baseado no progresso
          const progressRatio = plan.progress / 100
          plan.completedTasks = Math.floor(plan.totalTasks * progressRatio)
          
          // Salvar no localStorage
          localStorage.setItem('activePlans', JSON.stringify(activePlans.value))
          
          // Usar o progresso real para as estatísticas
          sessionProgress.value = progressToAdd
        }
      }
      
      // Salvar estatísticas da sessão
      const sessionStats = {
        planId: selectedPlanForAction.value?.id,
        planName: selectedPlanForAction.value?.name,
        duration: Math.floor(totalStudyTime.value / 60),
        completedPomodoros: completedPomodoros.value,
        progressAdded: parseFloat(sessionProgress.value) || 0,
        finalProgress: currentPlanProgress.value + (parseFloat(sessionProgress.value) || 0),
        date: new Date().toISOString()
      }
      
      // Salvar no histórico
      const history = JSON.parse(localStorage.getItem('studyHistory') || '[]')
      history.push(sessionStats)
      localStorage.setItem('studyHistory', JSON.stringify(history))
      
      // Atualizar sequência de estudo se habilitado
      if (sessionSettings.value.studyStreak && sessionStats.duration >= 5) {
        updateStudyStreak()
      }
      
      // Limpar sessão ativa
      localStorage.removeItem('activeStudySession')
      
      // Reset states
      showSessionTimer.value = false
      sessionTimeRemaining.value = 0
      totalStudyTime.value = 0
      completedPomodoros.value = 0
      isBreakTime.value = false
      
      // Limpar marcos celebrados
      if (window.celebratedMilestones) {
        window.celebratedMilestones.clear()
      }
      
      if (window.$toast) {
        if (sessionStats.progressAdded > 0) {
          window.$toast.success(`Sessão encerrada! Progresso adicionado: +${sessionStats.progressAdded.toFixed(1)}%`)
        } else {
          window.$toast.info(`Sessão encerrada. Tempo de estudo: ${sessionStats.duration} minutos`)
        }
      }
      
      // Reset progresso após mostrar mensagem
      sessionProgress.value = 0
    }
    
    const handleTimerComplete = () => {
      if (!isBreakTime.value) {
        // Completou um pomodoro
        completedPomodoros.value++
        
        // Salvar progresso parcial
        if (selectedPlanForAction.value && sessionProgress.value > 0) {
          const plan = activePlans.value.find(p => p.id === selectedPlanForAction.value.id)
          if (plan) {
            // Atualizar progresso do plano incrementalmente
            plan.progress = Math.min(100, (plan.progress || 0) + (parseFloat(sessionProgress.value) * 0.25))
            localStorage.setItem('activePlans', JSON.stringify(activePlans.value))
          }
        }
        
        // Som de notificação
        playNotificationSound()
        
        // Iniciar pausa
        if (completedPomodoros.value % 4 === 0) {
          // Pausa longa após 4 pomodoros
          sessionTimeRemaining.value = 15 * 60 // 15 minutos
        } else {
          // Pausa curta
          sessionTimeRemaining.value = 5 * 60 // 5 minutos
        }
        isBreakTime.value = true
        
        if (window.$toast) {
          window.$toast.success(`Pomodoro ${completedPomodoros.value} completado! Hora da pausa.`)
        }
      } else {
        // Completou a pausa
        isBreakTime.value = false
        sessionTimeRemaining.value = sessionDuration.value * 60
        
        playNotificationSound()
        
        if (window.$toast) {
          window.$toast.info('Pausa finalizada! Vamos continuar estudando.')
        }
      }
    }
    
    const skipBreak = () => {
      isBreakTime.value = false
      sessionTimeRemaining.value = sessionDuration.value * 60
    }
    
    const applyCustomDuration = () => {
      // Validar o valor inserido
      const duration = Math.max(5, Math.min(180, customDurationInput.value))
      sessionDuration.value = duration
      showCustomDuration.value = false
      
      if (window.$toast) {
        window.$toast.success(`Duração definida: ${duration} minutos`)
      }
    }
    
    const showPauseNotification = () => {
      if (window.Notification && Notification.permission === 'granted') {
        new Notification('Hora de uma pausa!', {
          body: 'Você está estudando há 25 minutos. Que tal uma pausa?',
          icon: '/favicon.ico'
        })
      }
    }
    
    const playNotificationSound = () => {
      const audio = new Audio('/audio/notification.mp3')
      audio.volume = 0.5
      audio.play().catch(err => console.log('Erro ao tocar som:', err))
    }
    
    const checkSmartBreakReminder = () => {
      // Lembrete inteligente baseado no ritmo
      const studyMinutes = Math.floor(totalStudyTime.value / 60)
      
      // Pausas dinâmicas baseadas na técnica escolhida
      let breakInterval = 25 // padrão pomodoro
      
      switch (sessionSettings.value.technique) {
        case 'flowtime':
          // Pausa quando a produtividade cai (simulado)
          if (studyMinutes > 0 && studyMinutes % 30 === 0) {
            showPauseNotification()
          }
          break
        case 'deepwork':
          breakInterval = 90
          break
        case 'timeboxing':
          breakInterval = sessionDuration.value
          break
      }
      
      if (totalStudyTime.value > 0 && totalStudyTime.value % (breakInterval * 60) === 0) {
        showPauseNotification()
      }
    }
    
    const checkProgressMilestone = () => {
      if (!selectedPlanForAction.value) return
      
      const progress = parseFloat((currentPlanProgress.value + sessionProgress.value).toFixed(2))
      
      // Verificar se já celebramos este marco nesta sessão
      if (!window.celebratedMilestones) {
        window.celebratedMilestones = new Set()
      }
      
      // Verificar marcos importantes
      const milestones = [25, 50, 75]
      
      for (const milestone of milestones) {
        const milestoneKey = `${selectedPlanForAction.value.id}-${milestone}`
        
        if (progress >= milestone && 
            currentPlanProgress.value < milestone && 
            !window.celebratedMilestones.has(milestoneKey)) {
          window.celebratedMilestones.add(milestoneKey)
          celebrateMilestone(milestone)
          break
        }
      }
    }
    
    const celebrateMilestone = (percentage) => {
      // Tocar som especial
      const audio = new Audio('/audio/achievement.mp3')
      audio.volume = 0.6
      audio.play().catch(err => console.log('Erro ao tocar som:', err))
      
      // Mostrar notificação especial
      if (window.$toast) {
        const messages = {
          25: '🎯 25% Concluído! Continue assim!',
          50: '🏆 Metade do caminho! Você está indo muito bem!',
          75: '🚀 75% Concluído! A reta final está próxima!'
        }
        window.$toast.success(messages[percentage])
      }
      
      // Adicionar animação visual (implementar no CSS)
      const timer = document.querySelector('.session-timer-container')
      if (timer) {
        timer.classList.add('celebrate')
        setTimeout(() => timer.classList.remove('celebrate'), 3000)
      }
    }
    
    const updateStudyStreak = () => {
      const today = new Date().toDateString()
      const streakData = JSON.parse(localStorage.getItem('studyStreak') || '{}')
      
      if (!streakData.lastStudyDate) {
        // Primeira vez estudando
        streakData.currentStreak = 1
        streakData.lastStudyDate = today
        streakData.bestStreak = 1
      } else {
        const lastDate = new Date(streakData.lastStudyDate)
        const yesterday = new Date()
        yesterday.setDate(yesterday.getDate() - 1)
        
        if (lastDate.toDateString() === today) {
          // Já estudou hoje, não fazer nada
          return
        } else if (lastDate.toDateString() === yesterday.toDateString()) {
          // Estudou ontem, continuar sequência
          streakData.currentStreak++
          streakData.lastStudyDate = today
          
          if (streakData.currentStreak > (streakData.bestStreak || 0)) {
            streakData.bestStreak = streakData.currentStreak
          }
          
          // Notificar marcos de sequência
          if ([7, 14, 30, 60, 100].includes(streakData.currentStreak)) {
            if (window.$toast) {
              window.$toast.success(`🔥 ${streakData.currentStreak} dias seguidos estudando! Continue assim!`)
            }
          }
        } else {
          // Quebrou a sequência
          streakData.currentStreak = 1
          streakData.lastStudyDate = today
        }
      }
      
      localStorage.setItem('studyStreak', JSON.stringify(streakData))
      
      // Atualizar display se existir
      if (window.$toast && streakData.currentStreak > 1) {
        window.$toast.info(`🔥 Sequência: ${streakData.currentStreak} dias`)
      }
    }
    
    // New methods for filters and quick actions
    const toggleFilter = (type, value) => {
      const index = activeFilters.value[type].indexOf(value)
      if (index > -1) {
        activeFilters.value[type].splice(index, 1)
      } else {
        activeFilters.value[type].push(value)
      }
    }
    
    const isNearDeadline = (plan) => {
      if (!plan.deadline) return false
      const deadline = new Date(plan.deadline)
      const today = new Date()
      const diffTime = deadline - today
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      return diffDays <= 7 && diffDays > 0
    }
    
    const getDaysUntilDeadline = (plan) => {
      if (!plan.deadline) return null
      const deadline = new Date(plan.deadline)
      const today = new Date()
      const diffTime = deadline - today
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      return diffDays
    }
    
    const getRemainingTime = (plan) => {
      if (!plan.estimatedTime || !plan.progress) return plan.estimatedTime
      const totalHours = plan.estimatedTime
      const completedHours = (plan.progress / 100) * totalHours
      const remaining = totalHours - completedHours
      return remaining.toFixed(1)
    }
    
    const quickStartSession = (plan) => {
      selectedPlanForAction.value = plan
      confirmStartSession()
    }
    
    const quickEditPlan = (plan) => {
      // Implementar edição rápida
      editPlan(plan)
    }
    
    const archivePlan = (plan) => {
      if (confirm(`Deseja arquivar o plano "${plan.name}"?`)) {
        const index = activePlans.value.findIndex(p => p.id === plan.id)
        if (index > -1) {
          // Move to archived plans
          const archivedPlans = JSON.parse(localStorage.getItem('archivedPlans') || '[]')
          archivedPlans.push({ ...plan, archivedDate: new Date().toISOString() })
          localStorage.setItem('archivedPlans', JSON.stringify(archivedPlans))
          
          // Remove from active plans
          activePlans.value.splice(index, 1)
          localStorage.setItem('activePlans', JSON.stringify(activePlans.value))
          
          if (window.$toast) {
            window.$toast.success(`Plano "${plan.name}" arquivado com sucesso`)
          }
        }
      }
    }
    
    onMounted(() => {
      // Solicitar permissão para notificações
      if (window.Notification && Notification.permission === 'default') {
        Notification.requestPermission()
      }
      
      // Add demo notifications on first load
      if (store && !localStorage.getItem('integrated_notifications_demo')) {
        // Calendar Event
        store.dispatch('notifications/addCenterNotification', {
          type: 'reminder',
          priority: 'high',
          source: 'calendar',
          title: '⏰ Aula em 30 minutos',
          message: 'Anatomia Humana - Sala 203',
          category: 'Calendário',
          actions: [
            { label: 'Ver Detalhes', action: 'router/push', params: { path: '/calendar' } }
          ]
        })
        
        // Study Tasks
        setTimeout(() => {
          store.dispatch('notifications/addCenterNotification', {
            type: 'deadline',
            priority: 'high',
            source: 'studyPlans',
            title: '📋 3 tarefas para hoje',
            message: '• Resumo Cap. 5 - Fisiologia\n• Exercícios de Bioquímica\n• Revisão de Farmacologia',
            category: 'Planos de Estudo',
            persistent: true,
            actions: [
              { label: 'Ver Tarefas', action: 'router/push', params: { path: '/plano-estudo' } }
            ]
          })
        }, 1000)
        
        // Flashcards
        setTimeout(() => {
          store.dispatch('notifications/addCenterNotification', {
            type: 'reminder',
            priority: 'medium',
            source: 'flashcards',
            title: '🎴 45 flashcards para revisar',
            message: 'Anatomia (20) | Histologia (15) | Embriologia (10)',
            category: 'Flashcards',
            persistent: true,
            actions: [
              { label: 'Iniciar Sessão', action: 'router/push', params: { path: '/ai-tools/flashcards' } }
            ]
          })
        }, 2000)
        
        // Performance Alert
        setTimeout(() => {
          store.dispatch('notifications/addCenterNotification', {
            type: 'warning',
            priority: 'high',
            source: 'performance',
            title: '🔥 Streak em risco!',
            message: 'Estude hoje para manter seu streak de 15 dias',
            category: 'Desempenho',
            requiresAction: true,
            actions: [
              { label: 'Estudar Agora', action: 'router/push', params: { path: '/calendar' } }
            ]
          })
        }, 3000)
        
        // Upcoming Exam
        setTimeout(() => {
          store.dispatch('notifications/addCenterNotification', {
            type: 'deadline',
            priority: 'urgent',
            source: 'exams',
            title: '🚨 Prova AMANHÃ!',
            message: 'Anatomia Sistêmica - 14:00h\nSala de Provas B',
            category: 'Provas',
            persistent: true,
            requiresAction: true
          })
        }, 4000)
        
        // Daily Summary
        setTimeout(() => {
          store.dispatch('notifications/addCenterNotification', {
            type: 'insight',
            priority: 'medium',
            source: 'system',
            title: '☀️ Resumo do Dia',
            message: '📊 Seu dia hoje:\n\n📅 4 eventos no calendário\n📋 6 tarefas pendentes\n🎴 45 flashcards para revisar\n⏰ Melhor horário: 14h-17h',
            category: 'Sistema',
            actions: [
              { label: 'Ver Calendário', action: 'router/push', params: { path: '/calendar' } }
            ]
          })
        }, 5000)
        
        localStorage.setItem('integrated_notifications_demo', 'true')
      }
      
      // Verificar se há sessão ativa
      const activeSession = localStorage.getItem('activeStudySession')
      if (activeSession) {
        const session = JSON.parse(activeSession)
        // Aqui você pode restaurar a sessão se desejar
      }
      
      // Carregar tarefas do localStorage
      const savedTasks = localStorage.getItem('planTasks')
      if (savedTasks) {
        planTasks.value = JSON.parse(savedTasks)
      }
    })
    
    onBeforeUnmount(() => {
      // Limpar timer se estiver ativo
      if (sessionTimerInterval.value) {
        clearInterval(sessionTimerInterval.value)
      }
    })
    
    return {
      selectedPlan,
      sessionView,
      quickNotes,
      pomodoroTime,
      pomodoroActive,
      showNewPlanModal,
      formStep,
      today,
      newPlan,
      planColors,
      planIcons,
      totalSubjects,
      studyHours,
      efficiency,
      streakDays,
      completedGoals,
      retentionRate,
      activePlans,
      todaySessions,
      aiInsights,
      resources,
      selectedCalendarDate,
      calendarEvents,
      selectPlan,
      createNewPlan,
      submitNewPlan,
      editPlan,
      startSession,
      pauseSession,
      refreshInsights,
      handleDateSelected,
      handleMonthChanged,
      startPomodoro,
      pausePomodoro,
      resetPomodoro,
      saveNotes,
      toggleQuickMenu,
      nextStep,
      previousStep,
      cancelPlanCreation,
      navigateBack,
      addObjective,
      removeObjective,
      updatePlanPreview,
      getDifficultyIcon,
      formatDate,
      formatDateShort,
      adjustColor,
      canProceed,
      isFormValid,
      expandedPlans,
      selectedTab,
      planTasks,
      weekProgress,
      togglePlanDetails,
      selectTab,
      getTabIcon,
      getPlanTasks,
      addNewTask,
      editTask,
      saveTask,
      deleteTask,
      closeTaskModal,
      getPriorityIcon,
      getFilteredTasks,
      setTaskFilter,
      toggleTask,
      getStudyHours,
      getEfficiency,
      getStreak,
      getPlanTimeline,
      exportPlan,
      sharePlan,
      startStudySession,
      showShareModal,
      showSessionModal,
      shareLink,
      selectedPlanForAction,
      copyShareLink,
      confirmStartSession,
      showSessionTimer,
      sessionDuration,
      sessionTimeRemaining,
      sessionPaused,
      sessionFocus,
      sessionSettings,
      isBreakTime,
      completedPomodoros,
      totalStudyTime,
      focusLevel,
      currentPlanProgress,
      sessionProgress,
      remainingHours,
      circleProgress,
      circleOffset,
      formatTime,
      pauseTimer,
      resumeTimer,
      endSession,
      skipBreak,
      showCustomDuration,
      customDurationInput,
      applyCustomDuration,
      timerThemes,
      studyTechniques,
      showTaskModal,
      editingTask,
      taskForm,
      taskFilter,
      showPlanFilters,
      activeFilters,
      planCategories,
      planStatuses,
      totalHoursToday,
      tasksCompletedToday,
      averageProgress,
      filteredPlans,
      toggleFilter,
      isNearDeadline,
      getDaysUntilDeadline,
      getRemainingTime,
      quickStartSession,
      quickEditPlan,
      archivePlan,
      newTag,
      suggestedTags,
      availableDependencies,
      addSubtask,
      removeSubtask,
      addTag,
      removeTag,
      selectTag,
      getFilteredSuggestions,
      createRecurringTasks,
      scheduleTaskReminder,
      updatePlanProgress
    }
  }
}
</script>

<style scoped>
/* Ultra Modern Dark Theme */
.study-plan-page {
  min-height: 100vh;
  background: #0f0a1a;
  position: relative;
  overflow-x: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Background Effects */
.background-effects {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.5;
}

.orb-1 {
  width: 600px;
  height: 600px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  top: -200px;
  left: -200px;
  animation: float-1 20s ease-in-out infinite;
}

.orb-2 {
  width: 500px;
  height: 500px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  top: 50%;
  right: -150px;
  animation: float-2 25s ease-in-out infinite;
}

.orb-3 {
  width: 400px;
  height: 400px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  bottom: -150px;
  left: 30%;
  animation: float-3 30s ease-in-out infinite;
}

.orb-4 {
  width: 350px;
  height: 350px;
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  top: 60%;
  left: -100px;
  animation: float-4 22s ease-in-out infinite;
}

@keyframes float-1 {
  0%, 100% { transform: translate(0, 0) scale(1); }
  25% { transform: translate(100px, -50px) scale(1.1); }
  50% { transform: translate(-50px, 100px) scale(0.9); }
  75% { transform: translate(50px, 50px) scale(1.05); }
}

@keyframes float-2 {
  0%, 100% { transform: translate(0, 0) scale(1); }
  25% { transform: translate(-100px, 100px) scale(0.95); }
  50% { transform: translate(50px, -50px) scale(1.1); }
  75% { transform: translate(-50px, -100px) scale(1); }
}

@keyframes float-3 {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(150px, -100px) scale(1.1); }
  66% { transform: translate(-100px, 50px) scale(0.9); }
}

@keyframes float-4 {
  0%, 100% { transform: translate(0, 0) scale(1); }
  50% { transform: translate(100px, 100px) scale(1.15); }
}

.grid-overlay {
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.01) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.01) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 20s linear infinite;
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

.animated-lines {
  position: absolute;
  inset: 0;
  overflow: hidden;
}

.line {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
}

.line-1 {
  top: 20%;
  height: 1px;
  width: 100%;
  animation: scan 8s linear infinite;
}

.line-2 {
  top: 50%;
  height: 2px;
  width: 100%;
  animation: scan 6s linear infinite;
  animation-delay: 2s;
}

.line-3 {
  top: 80%;
  height: 1px;
  width: 100%;
  animation: scan 10s linear infinite;
  animation-delay: 4s;
}

@keyframes scan {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.floating-particles {
  position: absolute;
  inset: 0;
  background-image: radial-gradient(2px 2px at 20% 30%, rgba(255, 255, 255, 0.3), transparent),
                    radial-gradient(2px 2px at 60% 70%, rgba(255, 255, 255, 0.2), transparent),
                    radial-gradient(1px 1px at 90% 10%, rgba(255, 255, 255, 0.2), transparent);
  background-repeat: repeat;
  background-size: 300px 300px;
  animation: stars 120s linear infinite;
}

@keyframes stars {
  0% { transform: translateY(0); }
  100% { transform: translateY(-300px); }
}

.glow-effects {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.glow {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
}

.glow-1 {
  width: 300px;
  height: 300px;
  background: rgba(102, 126, 234, 0.4);
  top: 10%;
  right: 10%;
  animation: pulse-glow 4s ease-in-out infinite;
}

.glow-2 {
  width: 250px;
  height: 250px;
  background: rgba(240, 147, 251, 0.4);
  bottom: 20%;
  left: 15%;
  animation: pulse-glow 5s ease-in-out infinite;
  animation-delay: 2s;
}

@keyframes pulse-glow {
  0%, 100% { opacity: 0.4; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

/* Main Layout */
.study-plan-layout {
  position: relative;
  z-index: 1;
  display: flex;
  min-height: 100vh;
}

.scheduler-column {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Header */
.scheduler-header {
  position: relative;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 2rem;
}


.header-content {
  position: relative;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.back-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateX(-2px);
}

.back-button i {
  font-size: 1.1rem;
}

.header-icon {
  position: relative;
}

.icon-wrapper {
  position: relative;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-wrapper i {
  font-size: 1.5rem;
  color: #fff;
  position: relative;
  z-index: 2;
}

.icon-ring {
  position: absolute;
  inset: 0;
  border: 2px solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
}

.ring-1 {
  animation: ring-pulse 2s ease-in-out infinite;
}

.ring-2 {
  animation: ring-pulse 2s ease-in-out infinite;
  animation-delay: 1s;
}

@keyframes ring-pulse {
  0% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.2); opacity: 0; }
  100% { transform: scale(1.2); opacity: 0; }
}

.icon-pulse {
  position: absolute;
  inset: -10px;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.4) 0%, transparent 70%);
  animation: icon-glow 2s ease-in-out infinite;
}

@keyframes icon-glow {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 0.8; }
}

.header-background {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  opacity: 0.5;
}

.header-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.main-title {
  font-size: 2.25rem;
  font-weight: 700;
  margin: 0;
  display: flex;
  gap: 0.625rem;
  letter-spacing: -0.02em;
}

.title-gradient {
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-accent {
  color: rgba(255, 255, 255, 0.9);
}

.subtitle {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  letter-spacing: 0.01em;
}

.header-stats {
  display: flex;
  gap: 1.5rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.25rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.stat-icon {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: #fff;
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  letter-spacing: -0.01em;
}

.stat-label {
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.6);
  letter-spacing: 0.02em;
}

/* Main Container */
.main-container {
  flex: 1;
  padding: 0 2rem 2rem;
  overflow-y: auto;
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: 1fr 1.4fr;
  gap: 2.5rem;
  margin-top: 2rem;
  align-items: start;
}

.left-column,
.right-column {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
}

/* Modern Card */
.modern-card {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2rem;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 1px rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.modern-card:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Section Header */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.create-plan-btn {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.5rem 1rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 10px;
  color: #667eea;
  font-size: 0.813rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 36px;
}

.create-plan-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  color: #fff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.create-plan-btn:active {
  transform: translateY(0);
}

.create-plan-btn i {
  font-size: 0.75rem;
}

.create-plan-btn span {
  white-space: nowrap;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.section-heading {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: white;
  display: flex;
  gap: 0.4rem;
  align-items: baseline;
  letter-spacing: -0.01em;
}

.section-heading .title-gradient {
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-heading .title-accent {
  color: rgba(255, 255, 255, 0.9);
}

.title-line {
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2px;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scaleX(1); }
  50% { opacity: 0.7; transform: scaleX(0.8); }
}

.section-heading .title-gradient {
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.section-heading .title-accent {
  color: rgba(255, 255, 255, 0.9);
}

.title-line {
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2px;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scaleX(1); }
  50% { opacity: 0.7; transform: scaleX(0.8); }
}

/* Action Button */
.action-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #fff;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.action-button:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.action-button.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
}

.action-button.primary:hover {
  background: linear-gradient(135deg, #764ba2, #667eea);
}


/* Plans Grid */
.plans-grid {
  display: grid;
  gap: 1.25rem;
}

.plan-card {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: visible;
}

.plan-card::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, transparent 30%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.plan-card:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.plan-card:hover::after {
  opacity: 1;
}

.plan-card.is-active {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.5);
  box-shadow: 0 0 30px rgba(102, 126, 234, 0.3);
}

.plan-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.25rem;
}

.plan-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  transition: all 0.3s ease;
  position: relative;
}

.plan-card:hover .plan-icon {
  transform: scale(1.1) rotate(5deg);
}

.plan-info {
  flex: 1;
}

.plan-info h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  margin: 0 0 0.375rem;
  letter-spacing: -0.01em;
}

.plan-info p {
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.4);
  margin: 0;
}

.plan-actions {
  display: flex;
  gap: 0.5rem;
  position: relative;
  z-index: 5;
}

.icon-btn {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
  z-index: 10;
}

.icon-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  transform: scale(1.1);
}

.icon-btn:active {
  transform: scale(0.95);
}

.plan-stats {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.stat-item i {
  color: #667eea;
}

.plan-progress {
  margin-top: auto;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.813rem;
}

.progress-label span:first-child {
  color: rgba(255, 255, 255, 0.5);
}

.progress-label span:last-child {
  color: #667eea;
  font-weight: 600;
}

.progress-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
  position: relative;
}



/* Sessions List */
.sessions-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.session-card {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.25rem;
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: 1.25rem;
  align-items: center;
  transition: all 0.3s ease;
}

.session-card:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateX(4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.session-card.completed {
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
}

.session-card.current {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.5);
  box-shadow: 0 0 30px rgba(102, 126, 234, 0.2);
}

.session-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.875rem;
}

.session-content h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #fff;
  margin: 0 0 0.25rem;
  letter-spacing: -0.01em;
}

.session-content p {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 0.5rem;
}

.session-tags {
  display: flex;
  gap: 0.5rem;
}

.tag {
  padding: 0.25rem 0.75rem;
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid rgba(102, 126, 234, 0.3);
}

.completion-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #10b981;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Calendar Section */
.calendar-section {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2.5rem;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 1px rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  min-height: 500px;
}

.calendar-section:hover {
  background: rgba(255, 255, 255, 0.05);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.calendar-wrapper {
  padding: 1rem 0;
  min-height: 400px;
}


/* Insights Section */
.insights-section {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 1px rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.insights-section:hover {
  background: rgba(255, 255, 255, 0.05);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.insight-card {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.insight-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(4px);
}

.insight-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.insight-icon.suggestion {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.insight-icon.warning {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.insight-icon.achievement {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.insight-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #fff;
  margin: 0 0 0.25rem;
  letter-spacing: -0.01em;
}

.insight-content p {
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 0.5rem;
}

.insight-action {
  background: none;
  border: none;
  color: #667eea;
  font-size: 0.813rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0;
  transition: all 0.3s ease;
}

.insight-action:hover {
  gap: 0.625rem;
  color: #764ba2;
}

/* Stats Section */
.stats-section {
  background: var(--elegant-white);
  border: 1px solid var(--elegant-border);
  border-radius: calc(var(--elegant-radius) * 1.5);
  padding: calc(var(--space-unit) * 6);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: calc(var(--space-unit) * 4);
}

.stat-box {
  text-align: center;
}

.stat-icon-wrapper {
  width: 48px;
  height: 48px;
  background: var(--elegant-gradient);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto calc(var(--space-unit) * 3);
  color: white;
  font-size: 1.25rem;
}

.stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--elegant-dark);
  margin-bottom: calc(var(--space-unit) * 1);
}

.stat-desc {
  font-size: 0.75rem;
  color: var(--elegant-gray);
}

/* Tools Section */
.tools-section {
  margin-top: calc(var(--space-unit) * 8);
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: calc(var(--space-unit) * 6);
}

.tool-card {
  background: var(--elegant-white);
  border: 1px solid var(--elegant-border);
  border-radius: calc(var(--elegant-radius) * 1.5);
  padding: calc(var(--space-unit) * 6);
  transition: var(--transition-smooth);
}

.tool-card:hover {
  box-shadow: var(--elegant-shadow-md);
}

.tool-header {
  display: flex;
  align-items: center;
  gap: calc(var(--space-unit) * 3);
  margin-bottom: calc(var(--space-unit) * 5);
}

.tool-icon {
  width: 40px;
  height: 40px;
  background: var(--elegant-primary-soft);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--elegant-primary);
  font-size: 1.125rem;
}

.tool-header h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--elegant-dark);
  margin: 0;
}

/* Timer */
.timer-display {
  text-align: center;
  margin-bottom: calc(var(--space-unit) * 5);
}

.timer-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--elegant-dark);
  font-variant-numeric: tabular-nums;
}

.timer-controls {
  display: flex;
  justify-content: center;
  gap: calc(var(--space-unit) * 3);
}

.timer-btn {
  width: 40px;
  height: 40px;
  background: var(--elegant-primary-soft);
  border: none;
  border-radius: 10px;
  color: var(--elegant-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-smooth);
}

.timer-btn:hover {
  background: var(--elegant-gradient);
  color: white;
}

/* Notes */
.notes-input {
  width: 100%;
  min-height: 120px;
  padding: calc(var(--space-unit) * 3);
  background: var(--elegant-gradient-soft);
  border: 1px solid var(--elegant-border);
  border-radius: calc(var(--elegant-radius));
  resize: vertical;
  font-family: inherit;
  font-size: 0.875rem;
  margin-bottom: calc(var(--space-unit) * 3);
  transition: var(--transition-smooth);
}

.notes-input:focus {
  outline: none;
  border-color: var(--elegant-primary);
}

/* Resources */
.resources-list {
  display: flex;
  flex-direction: column;
  gap: calc(var(--space-unit) * 2);
}

.resource-item {
  display: flex;
  align-items: center;
  gap: calc(var(--space-unit) * 3);
  padding: calc(var(--space-unit) * 3);
  background: var(--elegant-gradient-soft);
  border-radius: calc(var(--elegant-radius));
  text-decoration: none;
  color: var(--elegant-dark);
  transition: var(--transition-smooth);
}

.resource-item:hover {
  background: var(--elegant-primary-soft);
  transform: translateX(4px);
}

.resource-item i {
  color: var(--elegant-primary);
}

/* View Toggle */
.view-toggle {
  display: flex;
  background: var(--elegant-border);
  border-radius: calc(var(--elegant-radius));
  padding: 2px;
}

.toggle-btn {
  padding: calc(var(--space-unit) * 2) calc(var(--space-unit) * 3);
  background: transparent;
  border: none;
  border-radius: calc(var(--elegant-radius) - 2px);
  color: var(--elegant-gray);
  cursor: pointer;
  transition: var(--transition-smooth);
}

.toggle-btn.active {
  background: var(--elegant-white);
  color: var(--elegant-primary);
  box-shadow: var(--elegant-shadow-sm);
}

/* Refresh Button */
.refresh-btn {
  width: 36px;
  height: 36px;
  background: transparent;
  border: 1px solid var(--elegant-border);
  border-radius: 8px;
  color: var(--elegant-gray);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-smooth);
}

.refresh-btn:hover {
  background: var(--elegant-primary-soft);
  border-color: var(--elegant-primary);
  color: var(--elegant-primary);
  transform: rotate(90deg);
}

/* All other sections styling - Dark Theme */
.stats-section,
.tool-card {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  transition: all 0.3s ease;
}

.stats-section:hover,
.tool-card:hover {
  background: rgba(255, 255, 255, 0.05);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Update all text colors for dark theme */
h3, h4 {
  color: #fff;
}

h2.section-heading {
  color: transparent;
}

p {
  color: rgba(255, 255, 255, 0.6);
}

/* Update all remaining components for dark theme */
.stat-number,
.timer-value {
  color: #fff;
}

.stat-desc,
.resource-item {
  color: rgba(255, 255, 255, 0.6);
}

.stat-icon-wrapper {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.tool-icon {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.3);
}

.tool-header h3 {
  color: #fff;
}

.notes-input {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #fff;
}

.notes-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.notes-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.resource-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
}

.resource-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.resource-item i {
  color: #667eea;
}

.timer-btn {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.3);
}

.timer-btn:hover {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: transparent;
}

.view-toggle {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.toggle-btn {
  color: rgba(255, 255, 255, 0.6);
}

.toggle-btn.active {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.6);
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: #fff;
}


/* Modal Styles */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background: rgba(15, 15, 20, 0.98);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 2rem;
  width: 90%;
  max-width: 720px;
  max-height: 85vh;
  overflow-y: auto;
  animation: slideIn 0.2s ease;
  position: relative;
}

@keyframes slideIn {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.modal-header h3 {
  font-size: 1.375rem;
  font-weight: 600;
  color: #fff;
  margin: 0;
  letter-spacing: -0.01em;
}

.modal-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.modal-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
  font-weight: 500;
}

.form-input {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.form-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.08);
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.form-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

textarea.form-input {
  resize: vertical;
  min-height: 80px;
}

.color-picker {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.color-option {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.selected {
  border-color: #fff;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
}

.btn-cancel {
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-cancel:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.btn-submit {
  padding: 0.625rem 1.25rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.813rem;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-submit:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.25);
}

/* Enhanced Form Styles */
.form-progress {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2.5rem;
  padding: 0 2rem;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.progress-step.active .step-number {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
  color: #667eea;
}

.progress-step.completed .step-number {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.step-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
}

.progress-step.active .step-label {
  color: rgba(255, 255, 255, 0.8);
}

.progress-line {
  width: 100px;
  height: 2px;
  background: rgba(255, 255, 255, 0.1);
  margin: 0 1rem;
  transition: all 0.3s ease;
  align-self: center;
  margin-bottom: 2rem;
}

.progress-line.active {
  background: #667eea;
}

.form-step {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-group label i {
  color: #667eea;
  font-size: 0.875rem;
}

.char-count {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.4);
  text-align: right;
  margin-top: 0.25rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.time-input-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.time-unit {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
}

.time-suggestions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.suggestion-chip {
  padding: 0.375rem 0.875rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-chip:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
  color: #667eea;
}

.difficulty-selector {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
}

.difficulty-option {
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.difficulty-option:hover {
  background: rgba(255, 255, 255, 0.08);
}

.difficulty-option.selected {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
  color: #667eea;
}

.difficulty-option i {
  font-size: 1.25rem;
}

.objectives-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.objective-item {
  display: flex;
  gap: 0.5rem;
}

.objective-item input {
  flex: 1;
}

.remove-btn {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  background: rgba(245, 87, 108, 0.1);
  border-color: rgba(245, 87, 108, 0.3);
  color: #f5576c;
}

.add-objective-btn {
  padding: 0.75rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 10px;
  color: #667eea;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.add-objective-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.3);
}

.color-picker-advanced {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.preset-colors {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 0.5rem;
}

.color-option {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.875rem;
}

.custom-color {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.custom-color label {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.813rem;
}

.color-input {
  width: 60px;
  height: 32px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  cursor: pointer;
  background: transparent;
}

.icon-picker {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 0.5rem;
}

.icon-option {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 1.25rem;
}

.icon-option:hover {
  background: rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.8);
}

.icon-option.selected {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
  color: #667eea;
}

.notification-settings {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.switch-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
}

.switch-input {
  display: none;
}

.switch-slider {
  width: 48px;
  height: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  position: relative;
  transition: all 0.3s ease;
}

.switch-slider::after {
  content: '';
  position: absolute;
  width: 18px;
  height: 18px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  top: 3px;
  left: 3px;
  transition: all 0.3s ease;
}

.switch-input:checked + .switch-slider {
  background: rgba(102, 126, 234, 0.3);
}

.switch-input:checked + .switch-slider::after {
  background: #667eea;
  transform: translateX(24px);
}

.switch-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
}

.notification-options {
  padding-left: 3.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.813rem;
  cursor: pointer;
}

.checkbox-label input {
  width: 16px;
  height: 16px;
  accent-color: #667eea;
}

.plan-preview {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.plan-preview h4 {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.5);
  margin: 0 0 1rem;
}

.preview-card {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-left: 3px solid;
}

.preview-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.preview-info h5 {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 0.25rem;
}

.preview-info p {
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.5);
  margin: 0 0 0.75rem;
}

.preview-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.4);
}

.preview-meta span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.btn-back,
.btn-next {
  padding: 0.75rem 1.25rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-back:hover,
.btn-next:hover {
  background: rgba(255, 255, 255, 0.08);
}

.btn-next {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.2);
  color: #667eea;
}

.btn-next:hover {
  background: rgba(102, 126, 234, 0.2);
}

.btn-next:disabled,
.btn-submit:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.modal-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Share Modal Styles */
.share-modal .modal-content {
  max-width: 500px;
}

.share-info {
  margin-bottom: 2rem;
  text-align: center;
}

.share-info h4 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #fff;
  margin: 0 0 0.5rem;
}

.share-info p {
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

.share-link-section {
  margin-bottom: 2rem;
}

.share-link-section label {
  display: block;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
}

.link-input-group {
  display: flex;
  gap: 0.5rem;
}

.share-link-input {
  flex: 1;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #fff;
  font-size: 0.875rem;
  font-family: monospace;
}

.copy-btn {
  padding: 0.75rem 1.25rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 8px;
  color: #667eea;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.copy-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-1px);
}

.share-methods h5 {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 1rem;
}

.share-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
}

.share-method-btn {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.share-method-btn i {
  font-size: 1.5rem;
}

.share-method-btn.email:hover {
  background: rgba(234, 67, 53, 0.1);
  border-color: rgba(234, 67, 53, 0.3);
  color: #ea4335;
}

.share-method-btn.whatsapp:hover {
  background: rgba(37, 211, 102, 0.1);
  border-color: rgba(37, 211, 102, 0.3);
  color: #25d366;
}

.share-method-btn.teams:hover {
  background: rgba(91, 105, 223, 0.1);
  border-color: rgba(91, 105, 223, 0.3);
  color: #5b69df;
}

/* Session Modal Styles */
.session-modal .modal-content {
  max-width: 600px;
}

.session-plan-info {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.plan-icon-large {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  flex-shrink: 0;
}

.plan-details-modal h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  margin: 0 0 0.5rem;
}

.plan-details-modal p {
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  font-size: 0.875rem;
}

.session-config {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.config-section {
  margin-bottom: 2rem;
}

.config-section > label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1rem;
}

.config-section > label i {
  color: #667eea;
  font-size: 1.1rem;
}

.duration-options {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.75rem;
}

.duration-btn {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 2px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.duration-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(102, 126, 234, 0.3);
  color: #fff;
  transform: translateY(-1px);
}

.duration-btn.active {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
  color: #fff;
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.2);
}

.session-focus-select {
  width: 100%;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 2px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%23667eea' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3E%3C/svg%3E");
  background-position: right 1rem center;
  background-repeat: no-repeat;
  background-size: 1.5rem;
  padding-right: 3rem;
}

.session-focus-select:focus {
  outline: none;
  border-color: #667eea;
  background-color: rgba(255, 255, 255, 0.05);
}

.session-focus-select:hover {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(102, 126, 234, 0.3);
}

.session-focus-select option {
  background: #1a1625;
  color: #fff;
  padding: 0.75rem;
}

.environment-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.checkbox-option {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 0.75rem;
  position: relative;
}

.checkbox-option:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(102, 126, 234, 0.3);
}

.checkbox-option input[type="checkbox"] {
  position: absolute;
  opacity: 0;
}

.checkbox-option input[type="checkbox"]:checked ~ span i {
  color: #4facfe;
}

.checkbox-option input[type="checkbox"]:checked ~ small {
  color: rgba(255, 255, 255, 0.7);
}

.checkbox-option span {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.checkbox-option span i {
  color: #667eea;
  font-size: 1.1rem;
}

.checkbox-option small {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  margin-left: 1.85rem;
}

/* Theme Options */
.theme-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-top: 1rem;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem 1rem;
  border: 2px solid transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.theme-option i {
  font-size: 1.5rem;
  color: white;
  z-index: 1;
}

.theme-option span {
  font-size: 0.875rem;
  color: white;
  font-weight: 600;
  z-index: 1;
}

.theme-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.theme-option.active {
  border-color: white;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
}

/* Technique Options */
.technique-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.radio-option {
  display: block;
  cursor: pointer;
}

.radio-option input[type="radio"] {
  position: absolute;
  opacity: 0;
}

.technique-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 2px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.technique-card i {
  font-size: 1.5rem;
  color: #667eea;
}

.technique-card div {
  flex: 1;
}

.technique-card strong {
  display: block;
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0.25rem;
}

.technique-card small {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

.radio-option input[type="radio"]:checked + .technique-card {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
}

.radio-option input[type="radio"]:checked + .technique-card i {
  color: #4facfe;
}

.radio-option:hover .technique-card {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(102, 126, 234, 0.3);
}

.btn-start-session {
  padding: 0.875rem 1.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 10px;
  color: white;
  font-size: 0.9375rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.btn-start-session:hover {
  background: linear-gradient(135deg, #764ba2, #667eea);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

/* Responsive */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .header-stats {
    display: none;
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
  }
  
  .tools-grid {
    grid-template-columns: 1fr;
  }
  
  .plan-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .session-card {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

/* Plan Details Expansion */
.expand-enter-active,
.expand-leave-active {
  transition: all 0.3s ease;
}

.expand-enter-from,
.expand-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.plan-details {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  animation: fadeInUp 0.3s ease;
  position: relative;
  z-index: 20;
}

.detail-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0;
}

.tab-btn {
  padding: 0.75rem 1.25rem;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  position: relative;
}

.tab-btn:hover {
  color: rgba(255, 255, 255, 0.8);
}

.tab-btn.active {
  color: #667eea;
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: #667eea;
}

.tab-content {
  min-height: 300px;
  animation: fadeIn 0.3s ease;
}

/* Overview Tab */
.overview-tab {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.overview-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 1.25rem;
  display: flex;
  gap: 1rem;
}

.overview-icon {
  width: 40px;
  height: 40px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  flex-shrink: 0;
}

.overview-info h4 {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 0.75rem;
}

.objectives-list-view {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.objectives-list-view li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.813rem;
}

.objectives-list-view i {
  color: rgba(255, 255, 255, 0.3);
}

.objectives-list-view i.completed {
  color: #4ade80;
}

.empty-state {
  color: rgba(255, 255, 255, 0.4);
  font-style: italic;
}

.mini-chart {
  display: flex;
  gap: 0.5rem;
  height: 80px;
  align-items: flex-end;
}

.chart-bar {
  flex: 1;
  background: #667eea;
  border-radius: 4px 4px 0 0;
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding-bottom: 0.5rem;
}

.chart-bar span {
  font-size: 0.75rem;
  color: white;
  font-weight: 600;
}

.performance-meter {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin: 0.5rem 0;
}

.meter-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ade80, #667eea);
  transition: width 0.5s ease;
}

.performance-text {
  font-size: 0.813rem;
  color: #4ade80;
  margin: 0;
}

.study-schedule {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  padding: 1.25rem;
}

.study-schedule h4 {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.schedule-items {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.schedule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
}

.schedule-time {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
}

.schedule-duration {
  color: #667eea;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Tasks Tab */
.tasks-tab {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.tasks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.add-task-btn {
  padding: 0.75rem 1.25rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 10px;
  color: #667eea;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  position: relative;
  z-index: 30;
  pointer-events: auto;
}

.add-task-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-2px);
}

.task-filters {
  display: flex;
  gap: 0.5rem;
}

.filter-btn {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.813rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  position: relative;
  z-index: 30;
  pointer-events: auto;
}

.filter-btn:hover {
  background: rgba(255, 255, 255, 0.08);
}

.filter-btn.active {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
  color: #667eea;
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.task-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  align-items: flex-start;
  transition: all 0.2s ease;
}

.task-item:hover {
  background: rgba(255, 255, 255, 0.04);
}

.task-checkbox {
  padding-top: 0.125rem;
}

.task-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #667eea;
  cursor: pointer;
}

.task-content {
  flex: 1;
}

.task-content h5 {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 0.25rem;
}

.task-content p {
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.5);
  margin: 0 0 0.5rem;
}

.task-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.4);
}

.task-meta span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.task-priority {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.task-priority.alta {
  background: rgba(245, 87, 108, 0.2);
  color: #f5576c;
}

.task-priority.média {
  background: rgba(250, 204, 21, 0.2);
  color: #facc15;
}

.task-priority.baixa {
  background: rgba(74, 222, 128, 0.2);
  color: #4ade80;
}

.empty-tasks {
  text-align: center;
  padding: 3rem;
  color: rgba(255, 255, 255, 0.4);
}

.empty-tasks i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-tasks p {
  margin-bottom: 1.5rem;
}

.add-first-task {
  padding: 0.75rem 1.5rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 10px;
  color: #667eea;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-first-task:hover {
  background: rgba(102, 126, 234, 0.2);
}

/* Statistics Tab */
.stats-tab {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.stat-box {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 1.25rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.2s ease;
}

.stat-box:hover {
  background: rgba(255, 255, 255, 0.04);
  transform: translateY(-2px);
}

.stat-box i {
  font-size: 1.5rem;
  color: #667eea;
  opacity: 0.8;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #fff;
}

.stat-label {
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.5);
}

.progress-chart {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  padding: 1.5rem;
}

.progress-chart h4 {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 1.5rem;
}

.week-chart {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 150px;
  gap: 0.75rem;
}

.day-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  height: 100%;
}

.bar-fill {
  width: 100%;
  background: linear-gradient(180deg, #667eea, #4facfe);
  border-radius: 4px 4px 0 0;
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 0.5rem;
  margin-top: auto;
}

.bar-value {
  font-size: 0.75rem;
  color: white;
  font-weight: 600;
}

.day-name {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  text-transform: uppercase;
}

.insights-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.insights-section h4 {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.insight-cards {
  display: grid;
  gap: 0.75rem;
}

.insight-card {
  background: rgba(102, 126, 234, 0.05);
  border: 1px solid rgba(102, 126, 234, 0.1);
  border-radius: 10px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.insight-card i {
  font-size: 1.25rem;
  color: #667eea;
}

.insight-card p {
  margin: 0;
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.7);
  flex: 1;
}

/* Timeline Tab */
.timeline-tab {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.timeline-container {
  position: relative;
  padding-left: 2rem;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.timeline-container::-webkit-scrollbar {
  width: 6px;
}

.timeline-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.timeline-container::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.3);
  border-radius: 3px;
}

.timeline-container::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.5);
}

.timeline-container::before {
  content: '';
  position: absolute;
  left: 0.75rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: rgba(255, 255, 255, 0.1);
}

.timeline-item {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.timeline-marker {
  position: absolute;
  left: -1.25rem;
  width: 24px;
  height: 24px;
  background: rgba(102, 126, 234, 0.2);
  border: 2px solid #667eea;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 0.75rem;
  transition: all 0.3s ease;
}

.timeline-marker.start {
  background: rgba(139, 92, 246, 0.2);
  border-color: #8b5cf6;
  color: #8b5cf6;
}

.timeline-marker.study {
  background: rgba(79, 172, 254, 0.2);
  border-color: #4facfe;
  color: #4facfe;
  animation: pulse 2s infinite;
}

.timeline-marker.milestone {
  background: rgba(250, 204, 21, 0.2);
  border-color: #facc15;
  color: #facc15;
}

.timeline-marker.task {
  background: rgba(74, 222, 128, 0.2);
  border-color: #4ade80;
  color: #4ade80;
}

.timeline-marker.goal {
  background: rgba(240, 147, 251, 0.2);
  border-color: #f093fb;
  color: #f093fb;
}

.timeline-marker.complete {
  background: rgba(250, 204, 21, 0.2);
  border-color: #facc15;
  color: #facc15;
  box-shadow: 0 0 20px rgba(250, 204, 21, 0.4);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(79, 172, 254, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(79, 172, 254, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(79, 172, 254, 0);
  }
}

.timeline-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  padding: 1rem;
}

.timeline-content h5 {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 0.5rem;
}

.timeline-content p {
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 0.5rem;
}

.timeline-date {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.4);
}

.milestones-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.milestones-section h4 {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.milestones-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.milestone-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 1.25rem;
  text-align: center;
  transition: all 0.2s ease;
}

.milestone-card.achieved {
  background: rgba(102, 126, 234, 0.05);
  border-color: rgba(102, 126, 234, 0.2);
}

.milestone-card i {
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.3);
  margin-bottom: 0.75rem;
}

.milestone-card.achieved i {
  color: #667eea;
}

.milestone-card h5 {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 0.5rem;
}

.milestone-card p {
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.5);
  margin: 0;
}

.milestone-card.achieved p {
  color: #667eea;
}

/* Detail Actions */
.detail-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.action-btn {
  padding: 0.75rem 1.25rem;
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  border: none;
  position: relative;
  z-index: 30;
  pointer-events: auto;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
}

.action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.08);
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.action-btn.primary:hover {
  background: linear-gradient(135deg, #764ba2, #667eea);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

/* Responsive adjustments for expanded cards */
@media (max-width: 768px) {
  .detail-tabs {
    flex-wrap: wrap;
  }
  
  .tab-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.813rem;
  }
  
  .overview-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .week-chart {
    height: 120px;
  }
  
  .detail-actions {
    flex-wrap: wrap;
  }
  
  .milestones-grid {
    grid-template-columns: 1fr;
  }
}

/* Plan Details Expansion Styles */
.plan-details {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Tab Navigation */
.detail-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  background: rgba(255, 255, 255, 0.02);
  padding: 0.25rem;
  border-radius: 12px;
}

.tab-btn {
  padding: 0.625rem 1rem;
  border: none;
  background: transparent;
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.tab-btn:hover {
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.05);
}

.tab-btn.active {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
}

.tab-btn i {
  font-size: 0.875rem;
}

/* Tab Content */
.tab-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Overview Tab */
.overview-tab {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.overview-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 1.25rem;
  display: flex;
  gap: 1rem;
}

.overview-icon {
  width: 40px;
  height: 40px;
  background: rgba(102, 126, 234, 0.2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  flex-shrink: 0;
}

.overview-info {
  flex: 1;
}

.overview-info h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 0.5rem 0;
}

.objectives-list-view {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.objectives-list-view li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.6);
}

.objectives-list-view i {
  color: rgba(255, 255, 255, 0.3);
  font-size: 0.75rem;
}

.objectives-list-view i.completed {
  color: #667eea;
}

.empty-state {
  font-style: italic;
  color: rgba(255, 255, 255, 0.3);
}

/* Mini Chart */
.mini-chart {
  display: flex;
  gap: 0.5rem;
  height: 60px;
  align-items: flex-end;
}

.chart-bar {
  flex: 1;
  border-radius: 4px;
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding-bottom: 0.25rem;
}

.chart-bar span {
  font-size: 0.75rem;
  color: white;
  font-weight: 600;
}

/* Performance Meter */
.performance-meter {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin: 0.5rem 0;
}

.meter-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: width 0.5s ease;
}

.performance-text {
  font-size: 0.813rem;
  color: #667eea;
  margin: 0;
}

/* Study Schedule */
.study-schedule {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 1.25rem;
}

.study-schedule h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.schedule-items {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.schedule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
}

.schedule-time {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
}

.schedule-duration {
  font-size: 0.875rem;
  font-weight: 600;
  color: #667eea;
}

/* Tasks Tab */
.tasks-tab {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.tasks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.add-task-btn {
  padding: 0.625rem 1rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.add-task-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.task-filters {
  display: flex;
  gap: 0.5rem;
}

.filter-btn {
  padding: 0.5rem 0.875rem;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  border-radius: 6px;
  font-size: 0.813rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
}

.filter-btn.active {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  color: #667eea;
}

/* Tasks List */
.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.task-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  transition: all 0.2s ease;
}

.task-item:hover {
  background: rgba(255, 255, 255, 0.03);
  border-color: rgba(255, 255, 255, 0.1);
}

.task-checkbox input {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.task-content {
  flex: 1;
}

.task-content h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 0.25rem 0;
}

.task-content p {
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.5);
  margin: 0 0 0.5rem 0;
}

.task-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.4);
}

.task-priority {
  padding: 0.25rem 0.625rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.task-priority.high {
  background: rgba(245, 87, 108, 0.2);
  color: #f5576c;
}

.task-priority.medium {
  background: rgba(250, 112, 154, 0.2);
  color: #fa709a;
}

.task-priority.low {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
}

/* Empty Tasks */
.empty-tasks {
  text-align: center;
  padding: 3rem;
  color: rgba(255, 255, 255, 0.3);
}

.empty-tasks i {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-tasks p {
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.add-first-task {
  padding: 0.625rem 1.25rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid #667eea;
  color: #667eea;
  border-radius: 8px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-first-task:hover {
  background: rgba(102, 126, 234, 0.2);
}

/* Stats Tab */
.stats-tab {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.stat-box {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 1.25rem;
  display: flex;
  gap: 1rem;
  align-items: center;
}

.stat-box i {
  font-size: 1.5rem;
  color: #667eea;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
}

.stat-label {
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.5);
}

/* Week Progress Chart */
.week-progress-chart {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 1.25rem;
}

.week-progress-chart h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 1rem 0;
}

.week-chart {
  display: flex;
  align-items: flex-end;
  gap: 0.75rem;
  height: 150px;
}

.day-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.bar {
  width: 100%;
  background: linear-gradient(to top, #667eea, #764ba2);
  border-radius: 4px 4px 0 0;
  min-height: 10px;
  transition: height 0.3s ease;
}

.day-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

/* Timeline Tab */
.timeline-tab {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.timeline-header h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.timeline-filter {
  display: flex;
  gap: 0.5rem;
}

.timeline-events {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  position: relative;
  padding-left: 2rem;
}

.timeline-events::before {
  content: '';
  position: absolute;
  left: 0.625rem;
  top: 0.5rem;
  bottom: 0.5rem;
  width: 2px;
  background: rgba(255, 255, 255, 0.1);
}

.timeline-event {
  display: flex;
  gap: 1rem;
  position: relative;
}

.event-dot {
  position: absolute;
  left: -1.625rem;
  top: 0.375rem;
  width: 12px;
  height: 12px;
  background: #667eea;
  border-radius: 50%;
  border: 2px solid #1a1530;
}

.event-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 1rem;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.event-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.event-date {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.4);
}

.event-description {
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

/* Milestones */
.milestones-section {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 1.25rem;
}

.milestones-section h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.milestones-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.milestone-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.milestone-card:hover {
  background: rgba(255, 255, 255, 0.03);
  border-color: rgba(255, 255, 255, 0.1);
}

.milestone-card.achieved {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
}

.milestone-icon {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.5);
}

.milestone-card.achieved .milestone-icon {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
}

.milestone-info {
  flex: 1;
}

.milestone-info h5 {
  font-size: 0.813rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 0.125rem 0;
}

.milestone-card.achieved h5 {
  color: #667eea;
}

.milestone-info p {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.4);
  margin: 0;
}

/* Expand Animation */
.expand-enter-active, .expand-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.expand-enter-from, .expand-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Session Timer Styles */
.session-timer-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(15, 10, 26, 0.98);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
  min-width: 400px;
  box-shadow: 
    0 24px 48px rgba(0, 0, 0, 0.4),
    inset 0 1px 1px rgba(255, 255, 255, 0.1);
  z-index: 3000;
  transition: all 0.5s ease;
}

/* Theme variations */
.session-timer-container[data-theme="ocean"] {
  background: linear-gradient(135deg, rgba(26, 22, 37, 0.95) 0%, rgba(102, 126, 234, 0.15) 100%);
  border-color: rgba(102, 126, 234, 0.3);
}

.session-timer-container[data-theme="ocean"] .timer-display {
  color: #667eea;
}

.session-timer-container[data-theme="sunset"] {
  background: linear-gradient(135deg, rgba(26, 22, 37, 0.95) 0%, rgba(240, 147, 251, 0.15) 100%);
  border-color: rgba(240, 147, 251, 0.3);
}

.session-timer-container[data-theme="sunset"] .timer-display {
  color: #f093fb;
}

.session-timer-container[data-theme="forest"] {
  background: linear-gradient(135deg, rgba(26, 22, 37, 0.95) 0%, rgba(79, 172, 254, 0.15) 100%);
  border-color: rgba(79, 172, 254, 0.3);
}

.session-timer-container[data-theme="forest"] .timer-display {
  color: #4facfe;
}

.session-timer-container[data-theme="night"] {
  background: linear-gradient(135deg, rgba(26, 22, 37, 0.95) 0%, rgba(48, 207, 208, 0.15) 100%);
  border-color: rgba(48, 207, 208, 0.3);
}

.session-timer-container[data-theme="night"] .timer-display {
  color: #30cfd0;
}

.timer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.timer-minimize,
.timer-close {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative;
  z-index: 10;
  pointer-events: auto;
}

.timer-minimize:hover,
.timer-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.timer-plan-info {
  text-align: center;
  flex: 1;
}

.timer-plan-info h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  margin: 0 0 0.25rem;
}

.timer-plan-info p {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

.timer-display {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

.timer-circle {
  position: relative;
  width: 200px;
  height: 200px;
}

.timer-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.timer-background {
  fill: none;
  stroke: rgba(255, 255, 255, 0.1);
  stroke-width: 8;
}

.timer-progress {
  fill: none;
  stroke: url(#timer-gradient);
  stroke-width: 8;
  stroke-linecap: round;
  transition: stroke-dashoffset 1s linear;
}

.timer-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  font-size: 3rem;
  font-weight: 300;
  color: #fff;
  font-family: 'SF Mono', Monaco, monospace;
}

.timer-separator {
  margin: 0 0.25rem;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 49% { opacity: 1; }
  50%, 100% { opacity: 0; }
}

.timer-controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
  padding: 0 1rem;
}

.timer-btn {
  padding: 1.125rem 2rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 14px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.0625rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 10;
  pointer-events: auto;
  outline: none;
  -webkit-tap-highlight-color: transparent;
  backdrop-filter: blur(10px);
  letter-spacing: 0.03em;
  min-width: 140px;
  justify-content: center;
}

.timer-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.timer-btn:active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.15);
}

.timer-btn i {
  font-size: 1.25rem;
  transition: transform 0.2s ease;
}

.timer-btn:hover i {
  transform: scale(1.1);
}

.timer-btn.play {
  background: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.3);
  color: #4caf50;
}

.timer-btn.play:hover {
  background: rgba(76, 175, 80, 0.2);
}

.timer-btn.pause {
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.15), rgba(255, 193, 7, 0.15));
  border: 1px solid rgba(255, 152, 0, 0.3);
  color: #ffa726;
  box-shadow: 
    0 4px 15px rgba(255, 152, 0, 0.2),
    inset 0 1px 1px rgba(255, 255, 255, 0.1);
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.timer-btn.pause::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.2), 
    transparent
  );
  transition: left 0.5s ease;
}

.timer-btn.pause:hover::before {
  left: 100%;
}

.timer-btn.pause:hover {
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.25), rgba(255, 193, 7, 0.25));
  border-color: rgba(255, 152, 0, 0.5);
  box-shadow: 0 6px 20px rgba(255, 152, 0, 0.3);
  transform: translateY(-3px);
}

.timer-btn.pause:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.2);
}

.timer-btn.pause i {
  font-size: 1.25rem;
}

.timer-btn.stop {
  background: linear-gradient(135deg, rgba(244, 67, 54, 0.15), rgba(229, 57, 53, 0.15));
  border: 1px solid rgba(244, 67, 54, 0.3);
  color: #ef5350;
  box-shadow: 
    0 4px 15px rgba(244, 67, 54, 0.2),
    inset 0 1px 1px rgba(255, 255, 255, 0.1);
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.timer-btn.stop::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.2), 
    transparent
  );
  transition: left 0.5s ease;
}

.timer-btn.stop:hover::before {
  left: 100%;
}

.timer-btn.stop:hover {
  background: linear-gradient(135deg, rgba(244, 67, 54, 0.25), rgba(229, 57, 53, 0.25));
  border-color: rgba(244, 67, 54, 0.5);
  box-shadow: 0 6px 20px rgba(244, 67, 54, 0.3);
  transform: translateY(-3px);
}

.timer-btn.stop:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(244, 67, 54, 0.2);
}

.timer-btn.stop i {
  font-size: 1.25rem;
}

.session-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 1.5rem;
  margin-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 40px;
}

.stat-mini {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.stat-mini:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-1px);
}

.stat-mini i {
  font-size: 1.125rem;
  margin-right: 0.25rem;
}

.stat-mini:nth-child(1) i {
  color: #667eea;
  filter: drop-shadow(0 0 8px rgba(102, 126, 234, 0.5));
}

.stat-mini:nth-child(2) i {
  color: #4facfe;
  filter: drop-shadow(0 0 8px rgba(79, 172, 254, 0.5));
}

.stat-mini:nth-child(3) i {
  color: #f093fb;
  filter: drop-shadow(0 0 8px rgba(240, 147, 251, 0.5));
}

.stat-mini span {
  white-space: nowrap;
  font-size: 0.9rem;
}

/* Custom Duration Modal */
.custom-duration-modal {
  max-width: 450px;
}

.custom-duration-input {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.custom-duration-input label {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
}

.duration-input-group {
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-content: center;
}

.duration-adjust {
  width: 40px;
  height: 40px;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  color: #667eea;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.duration-adjust:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(1.1);
}

.duration-input {
  width: 120px;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #fff;
  font-size: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.duration-input:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.08);
}

.duration-input::-webkit-inner-spin-button,
.duration-input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.duration-hint {
  text-align: center;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.5);
  margin-top: -1rem;
}

.duration-presets {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.duration-presets span {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0.25rem;
}

.duration-presets {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.preset-btn {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.preset-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.5);
  color: #fff;
  transform: translateY(-1px);
}

.btn-apply {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.btn-apply:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

/* Celebration animation */
@keyframes celebrate {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.05);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.session-timer-container.celebrate {
  animation: celebrate 0.6s ease-in-out 5;
}

.session-timer-container.celebrate::after {
  content: '';
  position: absolute;
  inset: -20px;
  background: radial-gradient(circle, rgba(250, 204, 21, 0.3) 0%, transparent 70%);
  animation: pulse 1s ease-out;
  pointer-events: none;
}

/* Task Modal Styles */
.task-modal {
  max-width: 600px;
}

.task-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0.5rem;
}

.form-group label i {
  color: #667eea;
  font-size: 1rem;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #fff;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.08);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.time-input-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.time-unit {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.priority-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.priority-option {
  display: block;
  cursor: pointer;
}

.priority-option input[type="radio"] {
  position: absolute;
  opacity: 0;
}

.priority-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  font-size: 0.813rem;
  font-weight: 500;
  text-transform: capitalize;
  transition: all 0.3s ease;
}

.priority-option input[type="radio"]:checked + .priority-badge {
  border-color: currentColor;
  background: rgba(255, 255, 255, 0.1);
}

.priority-badge.baixa {
  color: #4ade80;
}

.priority-badge.média {
  color: #facc15;
}

.priority-badge.alta {
  color: #fb923c;
}

.priority-badge.urgente {
  color: #ef4444;
}

.btn-save-task {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 10px;
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.btn-save-task:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.btn-save-task:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Task List Styles */
.task-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
}

.task-item:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(102, 126, 234, 0.2);
}

.task-item.completed {
  opacity: 0.6;
}

.task-item.completed .task-content h5 {
  text-decoration: line-through;
  color: rgba(255, 255, 255, 0.5);
}

.task-checkbox input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #667eea;
  cursor: pointer;
}

.task-content {
  flex: 1;
  cursor: pointer;
}

.task-content h5 {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 0.25rem;
  font-weight: 600;
}

.task-description {
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0.25rem 0 0.5rem;
  line-height: 1.4;
}

.task-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

.task-meta span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.task-meta i {
  font-size: 0.75rem;
  color: rgba(102, 126, 234, 0.6);
}

.task-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.task-delete {
  width: 32px;
  height: 32px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 8px;
  color: #ef4444;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  opacity: 0;
}

.task-item:hover .task-delete {
  opacity: 1;
}

.task-delete:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.1);
}

/* Timer minimized button */
.timer-minimized-btn {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  padding: 0.75rem 1.25rem;
  background: rgba(102, 126, 234, 0.9);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: 24px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
  transition: all 0.2s ease;
  z-index: 2500;
}

.timer-minimized-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
}

/* Timer slide animation */
.timer-slide-enter-active,
.timer-slide-leave-active {
  transition: all 0.3s ease;
}

.timer-slide-enter-from {
  opacity: 0;
  transform: translate(-50%, -40%);
}

.timer-slide-leave-to {
  opacity: 0;
  transform: translate(-50%, -60%);
}

/* Timer gradient */
.timer-svg defs {
  display: none;
}

.timer-progress {
  stroke: #667eea;
}

.timer-circle.break-time .timer-progress {
  stroke: #4caf50;
}

/* New styles for optimized page */
.active-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 1rem;
}

.pulse-dot {
  width: 8px;
  height: 8px;
  background: #4caf50;
  border-radius: 50%;
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

.active-count {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.813rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.filter-btn.active {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
  color: #667eea;
}

.create-plan-btn {
  position: relative;
  overflow: hidden;
}

.btn-glow {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at center, rgba(102, 126, 234, 0.4) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.create-plan-btn:hover .btn-glow {
  opacity: 1;
}

/* Filters Panel */
.filters-panel {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.filter-group {
  margin-bottom: 1.25rem;
}

.filter-group:last-child {
  margin-bottom: 0;
}

.filter-group label {
  display: block;
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.filter-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-chip {
  padding: 0.375rem 0.875rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.813rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-chip:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.filter-chip.active {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.5);
  color: #667eea;
}

/* Quick Stats Grid */
.quick-stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.quick-stat-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.quick-stat-card:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.stat-icon-wrapper {
  width: 40px;
  height: 40px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-number {
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  line-height: 1;
}

.stat-text {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 0.25rem;
}

/* Enhanced Plan Card */
.plan-card.high-priority {
  border-color: rgba(240, 147, 251, 0.3);
}

.plan-card.near-deadline {
  border-color: rgba(239, 68, 68, 0.3);
}

.icon-pulse {
  position: absolute;
  inset: -8px;
  background: radial-gradient(circle, currentColor 0%, transparent 70%);
  opacity: 0;
  animation: icon-pulse 2s infinite;
}

@keyframes icon-pulse {
  0%, 100% { 
    opacity: 0;
    transform: scale(0.8);
  }
  50% { 
    opacity: 0.3;
    transform: scale(1.2);
  }
}

.plan-tags {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.priority-tag,
.deadline-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.625rem;
  border-radius: 12px;
  font-size: 0.688rem;
  font-weight: 500;
}

.priority-tag.high {
  background: rgba(240, 147, 251, 0.1);
  color: #f093fb;
  border: 1px solid rgba(240, 147, 251, 0.2);
}

.deadline-tag {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Quick Actions */
.icon-btn.start {
  background: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.icon-btn.start:hover {
  background: rgba(76, 175, 80, 0.2);
}

.icon-btn.archive {
  background: rgba(255, 193, 7, 0.1);
  border-color: rgba(255, 193, 7, 0.2);
  color: #ffc107;
}

.icon-btn.archive:hover {
  background: rgba(255, 193, 7, 0.2);
}

/* Progress Bar Enhancements */
.progress-value {
  font-weight: 600;
  font-size: 0.875rem;
}

.progress-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progress-glow 2s infinite;
}

@keyframes progress-glow {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.time-remaining {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.5);
}

.time-remaining i {
  color: rgba(102, 126, 234, 0.7);
}

/* Transitions */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Advanced Task Modal Styles */
.task-modal {
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
}

.task-modal .modal-body {
  padding: 2rem;
}

.advanced-section {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.section-divider {
  text-align: center;
  margin-bottom: 1.5rem;
  position: relative;
}

.section-divider span {
  background: #1a1625;
  padding: 0 1rem;
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
  z-index: 1;
}

.section-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

/* Subtasks */
.subtasks-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.subtask-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.subtask-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.subtask-checkbox {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.subtask-input {
  flex: 1;
  background: transparent;
  border: none;
  color: #fff;
  font-size: 0.875rem;
  padding: 0.5rem;
  outline: none;
}

.subtask-input:focus {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
}

.remove-btn {
  width: 32px;
  height: 32px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 6px;
  color: #ef4444;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.remove-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.1);
}

.add-subtask-btn {
  padding: 0.75rem 1rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 10px;
  color: #667eea;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.add-subtask-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-2px);
}

/* Tags */
.tags-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  min-height: 32px;
}

.tag-chip {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 20px;
  color: #667eea;
  font-size: 0.813rem;
  font-weight: 500;
}

.tag-remove {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.tag-remove:hover {
  color: #ef4444;
  transform: scale(1.2);
}

.tag-input-wrapper {
  position: relative;
}

.tag-input {
  width: 100%;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #fff;
  font-size: 0.875rem;
}

.tag-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 0.5rem;
  background: #1a1625;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 0.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  z-index: 10;
}

.tag-suggestion {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.813rem;
  text-align: left;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.tag-suggestion:hover {
  background: rgba(102, 126, 234, 0.2);
  color: #fff;
}

/* Recurrence Options */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
}

.form-checkbox {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.recurrence-options {
  margin-top: 0.75rem;
  padding-left: 2rem;
}

/* Progress Tracker */
.progress-tracker {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.progress-slider {
  flex: 1;
  height: 6px;
  -webkit-appearance: none;
  appearance: none;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  outline: none;
}

.progress-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  background: #667eea;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.progress-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.5);
}

.progress-percent {
  font-size: 0.875rem;
  font-weight: 600;
  color: #667eea;
  min-width: 50px;
  text-align: right;
}

.progress-preview {
  margin-top: 0.5rem;
}

.progress-preview .progress-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-preview .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

/* Form Elements */
.form-select {
  width: 100%;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #fff;
  font-size: 0.875rem;
  cursor: pointer;
}

.form-textarea {
  width: 100%;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #fff;
  font-size: 0.875rem;
  resize: vertical;
  font-family: inherit;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-cancel,
.btn-primary {
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  border: none;
}

.btn-cancel {
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-cancel:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

/* Task Modal Button */
.btn-save-task {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 10px;
  color: #fff;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.btn-save-task:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.btn-save-task:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn-save-task:disabled:hover {
  transform: none;
  box-shadow: none;
}
</style>