<template>
  <div class="home-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="grid-pattern"></div>
        <div class="floating-orbs">
          <span class="orb orb-1"></span>
          <span class="orb orb-2"></span>
          <span class="orb orb-3"></span>
        </div>
      </div>
      
      <div class="hero-content">
        <div class="welcome-section">
          <div class="welcome-avatar">
            <div class="avatar-glow"></div>
            <div class="avatar-core">
              <span>{{ userInitials }}</span>
            </div>
            <div class="avatar-rings">
              <span class="ring ring-1"></span>
              <span class="ring ring-2"></span>
            </div>
          </div>
          
          <div class="welcome-text">
            <h1 class="welcome-title">
              <span class="greeting">Bem-vindo(a),</span>
              <span class="user-name">{{ userName }}!</span>
            </h1>
            <p class="subtitle">
              <font-awesome-icon icon="fa-brain" />
              Seu espaço de estudo personalizado com IA.
            </p>
            <div class="last-login" v-if="lastLogin">
              <font-awesome-icon icon="fa-clock" />
              <span>Último acesso: {{ formatDate(lastLogin) }}</span>
            </div>
          </div>
        </div>
        
        <div class="hero-metrics">
          <MetricCard
            v-for="metric in heroMetrics"
            :key="metric.id"
            :icon="metric.icon"
            :value="metric.value"
            :label="metric.label"
            :color="metric.color"
          />
        </div>
      </div>
    </section>

    <!-- Quick Actions -->
    <section class="quick-actions">
      <SectionHeader
        icon="fa-rocket"
        title="Ações Rápidas"
        subtitle="Acesse rapidamente suas ferramentas favoritas"
      />
      
      <div class="actions-grid">
        <QuickActionCard
          v-for="action in quickActions"
          :key="action.id"
          :icon="action.icon"
          :title="action.title"
          :description="action.description"
          :link="action.link"
          :color="action.color"
        />
      </div>
    </section>

    <!-- Progress Overview -->
    <section class="progress-overview">
      <SectionHeader
        icon="fa-chart-line"
        title="Visão Geral do Progresso"
        subtitle="Acompanhe seu desempenho e evolução"
      />
      
      <div class="progress-content">
        <ProgressStats :stats="progressStats" />
        <WeeklyChart :data="weeklyProgress" />
        <PerformanceDetails :metrics="performanceMetrics" />
      </div>
    </section>

    <!-- Achievements -->
    <section class="achievements">
      <SectionHeader
        icon="fa-medal"
        title="Conquistas"
        subtitle="Desbloqueie recompensas conforme avança nos estudos"
      />
      
      <AchievementsGrid :achievements="achievements" />
    </section>

    <!-- Recent Activities -->
    <section class="recent-activities">
      <SectionHeader
        icon="fa-clock-rotate-left"
        title="Atividades Recentes"
        subtitle="Veja o que você fez recentemente"
      />
      
      <ActivityTimeline :activities="recentActivities" />
    </section>

    <!-- AI Assistant -->
    <section class="ai-assistant">
      <AIAssistantWidget
        :message="currentAIMessage"
        :suggestions="aiSuggestions"
        :loading="aiMessageLoading"
        @send-message="sendAIMessage"
        @suggestion-click="handleAISuggestion"
      />
    </section>

    <!-- Study Resources -->
    <section class="study-resources">
      <SectionHeader
        icon="fa-book"
        title="Recursos Recomendados"
        subtitle="Conteúdo selecionado especialmente para você"
      />
      
      <ResourcesGrid :resources="recommendedResources" />
    </section>

    <!-- Getting Started Guide -->
    <section class="getting-started" v-if="showGettingStarted">
      <SectionHeader
        icon="fa-road"
        title="Guia de Início"
        subtitle="Complete estas etapas para começar"
      />
      
      <GettingStartedSteps
        :steps="gettingStartedSteps"
        @skip="showGettingStarted = false"
      />
    </section>
  </div>
</template>

<script>
import { computed, ref, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

// Component imports
import SectionHeader from './common/SectionHeader.vue';
import MetricCard from './common/MetricCard.vue';
import QuickActionCard from './common/QuickActionCard.vue';
import ProgressStats from './home/<USER>';
import WeeklyChart from './home/<USER>';
import PerformanceDetails from './home/<USER>';
import AchievementsGrid from './home/<USER>';
import ActivityTimeline from './home/<USER>';
import AIAssistantWidget from './home/<USER>';
import ResourcesGrid from './home/<USER>';
import GettingStartedSteps from './home/<USER>';

export default {
  name: 'HomePage',
  components: {
    SectionHeader,
    MetricCard,
    QuickActionCard,
    ProgressStats,
    WeeklyChart,
    PerformanceDetails,
    AchievementsGrid,
    ActivityTimeline,
    AIAssistantWidget,
    ResourcesGrid,
    GettingStartedSteps
  },
  setup() {
    const store = useStore();
    const router = useRouter();

    // User data
    const user = computed(() => store.getters['auth/user']);
    const userName = computed(() => user.value?.name || 'Estudante');
    const userInitials = computed(() => {
      if (user.value?.name) {
        return user.value.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
      }
      return 'ME';
    });

    // Data refs
    const lastLogin = ref(new Date());
    const showGettingStarted = ref(true);
    const aiMessageLoading = ref(false);
    const currentAIMessage = ref('Olá! Como posso ajudar você hoje?');

    // Hero metrics
    const heroMetrics = ref([
      {
        id: 'study-days',
        icon: 'fa-calendar-days',
        value: 42,
        label: 'Dias de estudo',
        color: 'primary'
      },
      {
        id: 'streak',
        icon: 'fa-fire',
        value: 7,
        label: 'Sequência atual',
        color: 'orange'
      }
    ]);

    // Quick actions
    const quickActions = ref([
      {
        id: 'second-brain',
        icon: 'fa-brain',
        title: 'Second Brain',
        description: 'Converse com a IA sobre qualquer tópico médico',
        link: '/ia/second-brain',
        color: 'purple'
      },
      {
        id: 'flashcards',
        icon: 'fa-clone',
        title: 'Flashcards',
        description: 'Revise conceitos com cartões inteligentes',
        link: '/ia/flashcards',
        color: 'blue'
      },
      {
        id: 'study-plan',
        icon: 'fa-calendar-check',
        title: 'Plano de Estudos',
        description: 'Organize sua rotina de estudos',
        link: '/plano-estudo',
        color: 'green'
      },
      {
        id: 'questions',
        icon: 'fa-question-circle',
        title: 'Questões',
        description: 'Pratique com questões comentadas',
        link: '/ia/questions',
        color: 'orange'
      },
      {
        id: 'calendar',
        icon: 'fa-calendar',
        title: 'Calendário',
        description: 'Visualize seus compromissos e revisões',
        link: '/calendar',
        color: 'teal'
      },
      {
        id: 'resources',
        icon: 'fa-book',
        title: 'Recursos',
        description: 'Acesse materiais de estudo',
        link: '/recursos',
        color: 'indigo'
      }
    ]);

    // Progress stats
    const progressStats = ref([
      {
        id: 'hours',
        value: 125,
        suffix: 'h',
        title: 'Horas Estudadas',
        description: 'Total de tempo dedicado',
        icon: 'fa-clock',
        trend: '+12%',
        trendType: 'positive'
      },
      {
        id: 'tasks',
        value: 42,
        title: 'Tarefas Concluídas',
        description: 'Atividades finalizadas',
        icon: 'fa-list-check',
        trend: '+7',
        trendType: 'positive'
      },
      {
        id: 'score',
        value: 87,
        suffix: '%',
        title: 'Pontuação Média',
        description: 'Média de acertos',
        icon: 'fa-star',
        trend: '+5%',
        trendType: 'positive'
      },
      {
        id: 'completion',
        value: 78,
        suffix: '%',
        title: 'Taxa de Conclusão',
        description: 'Tarefas vs. planejadas',
        icon: 'fa-check-double',
        trend: '-3%',
        trendType: 'negative'
      }
    ]);

    // Weekly progress data
    const weeklyProgress = ref([
      { day: 'Seg', hours: 2, percentage: 40 },
      { day: 'Ter', hours: 3.5, percentage: 70 },
      { day: 'Qua', hours: 1.5, percentage: 30 },
      { day: 'Qui', hours: 4, percentage: 80 },
      { day: 'Sex', hours: 2.5, percentage: 50, isToday: true },
      { day: 'Sáb', hours: 0, percentage: 0 },
      { day: 'Dom', hours: 0, percentage: 0 }
    ]);

    // Performance metrics
    const performanceMetrics = ref([
      { label: 'Taxa de Retenção', value: '87%', icon: 'fa-brain', color: 'green' },
      { label: 'Foco Médio', value: '42 min', icon: 'fa-bullseye', color: 'orange' },
      { label: 'Produtividade', value: 'Alta', icon: 'fa-rocket', color: 'purple' }
    ]);

    // Achievements
    const achievements = ref([
      {
        id: 1,
        title: 'Iniciante Empolgado',
        description: 'Complete 5 dias seguidos de estudo',
        progress: 100,
        icon: 'fa-fire',
        completed: true
      },
      {
        id: 2,
        title: 'Maratonista de Estudos',
        description: 'Estude por 50 horas acumuladas',
        progress: 80,
        icon: 'fa-trophy',
        completed: false
      },
      {
        id: 3,
        title: 'Memória de Elefante',
        description: 'Complete 100 sessões de estudo',
        progress: 40,
        icon: 'fa-brain',
        completed: false
      }
    ]);

    // Recent activities
    const recentActivities = ref([
      {
        id: 1,
        type: 'flashcard',
        icon: 'fa-clone',
        title: 'Revisão de Flashcards',
        description: 'Completou 30 cartões de Cardiologia',
        time: '30 minutos atrás',
        link: '/ia/flashcards'
      },
      {
        id: 2,
        type: 'ai',
        icon: 'fa-robot',
        title: 'Consulta à IA',
        description: 'Perguntou sobre arritmias cardíacas',
        time: '2 horas atrás',
        link: '/ia/second-brain'
      },
      {
        id: 3,
        type: 'quiz',
        icon: 'fa-clipboard-check',
        title: 'Simulado Concluído',
        description: 'Neurologia - 78% de acertos',
        time: '5 horas atrás',
        link: '/provas-simulados'
      }
    ]);

    // AI suggestions
    const aiSuggestions = ref([
      { text: 'Como organizar meus estudos?', action: 'studyPlan' },
      { text: 'Revisar Cardiologia', action: 'cardiology' },
      { text: 'Ver meu progresso', action: 'progress' },
      { text: 'Próximas tarefas', action: 'tasks' }
    ]);

    // Recommended resources
    const recommendedResources = ref([
      {
        id: 1,
        title: 'Avanços em Cardiologia',
        description: 'Artigo atualizado sobre insuficiência cardíaca',
        icon: 'fa-file-medical',
        tags: ['Cardiologia', 'Pesquisa'],
        type: 'article',
        duration: '15 min'
      },
      {
        id: 2,
        title: 'Neuroanatomia Aplicada',
        description: 'Vídeo aula sobre sistema nervoso central',
        icon: 'fa-video',
        tags: ['Neurologia', 'Vídeo'],
        type: 'video',
        duration: '30 min'
      },
      {
        id: 3,
        title: 'Questões de Endocrinologia',
        description: 'Banco de questões comentadas',
        icon: 'fa-clipboard-question',
        tags: ['Endocrinologia', 'Prática'],
        type: 'questions',
        duration: '45 min'
      }
    ]);

    // Getting started steps
    const gettingStartedSteps = ref([
      {
        id: 1,
        title: 'Configure seu perfil',
        description: 'Defina suas metas e preferências',
        completed: true,
        link: '/profile',
        icon: 'fa-user-cog'
      },
      {
        id: 2,
        title: 'Crie um plano de estudos',
        description: 'Organize sua rotina de estudos',
        completed: false,
        link: '/plano-estudo',
        icon: 'fa-list-check'
      },
      {
        id: 3,
        title: 'Adicione eventos ao calendário',
        description: 'Programe suas sessões de estudo',
        completed: false,
        link: '/calendar',
        icon: 'fa-calendar-days'
      },
      {
        id: 4,
        title: 'Explore o Second Brain',
        description: 'Converse com nossa IA especializada',
        completed: false,
        link: '/ia/second-brain',
        icon: 'fa-brain'
      }
    ]);

    // Methods
    const formatDate = (date) => {
      return format(new Date(date), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });
    };

    const sendAIMessage = (message) => {
      aiMessageLoading.value = true;
      // Simulate AI response
      setTimeout(() => {
        currentAIMessage.value = `Processando sua pergunta: "${message}"...`;
        aiMessageLoading.value = false;
      }, 1500);
    };

    const handleAISuggestion = (action) => {
      switch (action) {
        case 'studyPlan':
          router.push('/plano-estudo');
          break;
        case 'cardiology':
          router.push('/ia/flashcards?topic=cardiologia');
          break;
        case 'progress':
          router.push('/progress-dashboard');
          break;
        case 'tasks':
          router.push('/calendar');
          break;
      }
    };

    // Lifecycle
    onMounted(() => {
      // Load user data if not already loaded
      if (!user.value) {
        store.dispatch('auth/fetchUser').catch(console.error);
      }
      
      // Check if user is new
      const isNewUser = localStorage.getItem('isNewUser') !== 'false';
      showGettingStarted.value = isNewUser;
    });

    return {
      // User data
      userName,
      userInitials,
      lastLogin,
      
      // UI state
      showGettingStarted,
      aiMessageLoading,
      currentAIMessage,
      
      // Data
      heroMetrics,
      quickActions,
      progressStats,
      weeklyProgress,
      performanceMetrics,
      achievements,
      recentActivities,
      aiSuggestions,
      recommendedResources,
      gettingStartedSteps,
      
      // Methods
      formatDate,
      sendAIMessage,
      handleAISuggestion
    };
  }
};
</script>

<style scoped>
.home-page {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

/* Hero Section */
.hero-section {
  position: relative;
  background: var(--color-bg-secondary);
  border-radius: 16px;
  padding: 3rem 2rem;
  margin-bottom: 3rem;
  overflow: hidden;
  border: 1px solid var(--color-border);
}

.hero-background {
  position: absolute;
  inset: 0;
  opacity: 0.1;
  pointer-events: none;
}

.grid-pattern {
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(66, 185, 131, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(66, 185, 131, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  from { transform: translate(0, 0); }
  to { transform: translate(50px, 50px); }
}

.floating-orbs {
  position: absolute;
  inset: 0;
}

.orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.3;
}

.orb-1 {
  width: 300px;
  height: 300px;
  background: rgba(66, 185, 131, 0.3);
  top: -100px;
  left: -100px;
  animation: orbFloat1 15s ease-in-out infinite;
}

.orb-2 {
  width: 200px;
  height: 200px;
  background: rgba(139, 92, 246, 0.3);
  bottom: -50px;
  right: -50px;
  animation: orbFloat2 20s ease-in-out infinite;
}

.orb-3 {
  width: 150px;
  height: 150px;
  background: rgba(59, 130, 246, 0.3);
  top: 50%;
  left: 50%;
  animation: orbFloat3 18s ease-in-out infinite;
}

@keyframes orbFloat1 {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(30px, -30px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.95); }
}

@keyframes orbFloat2 {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(-40px, -20px) scale(1.05); }
  66% { transform: translate(30px, 30px) scale(0.9); }
}

@keyframes orbFloat3 {
  0%, 100% { transform: translate(-50%, -50%) scale(1); }
  33% { transform: translate(-40%, -60%) scale(1.15); }
  66% { transform: translate(-60%, -40%) scale(0.85); }
}

.hero-content {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 3rem;
}

.welcome-section {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex: 1;
}

/* Welcome Avatar */
.welcome-avatar {
  position: relative;
  width: 80px;
  height: 80px;
  flex-shrink: 0;
}

.avatar-glow {
  position: absolute;
  inset: -10px;
  background: radial-gradient(circle, rgba(66, 185, 131, 0.4) 0%, transparent 70%);
  border-radius: 50%;
  animation: avatarGlow 3s ease-in-out infinite;
}

@keyframes avatarGlow {
  0%, 100% { transform: scale(0.9); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.6; }
}

.avatar-core {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  z-index: 3;
  box-shadow: 0 0 20px rgba(66, 185, 131, 0.5);
}

.avatar-rings {
  position: absolute;
  inset: 0;
  z-index: 2;
}

.ring {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  border: 1px solid rgba(66, 185, 131, 0.3);
}

.ring-1 {
  animation: ringExpand 3s ease-out infinite;
}

.ring-2 {
  animation: ringExpand 3s ease-out infinite 1.5s;
}

@keyframes ringExpand {
  0% { transform: scale(1); opacity: 1; }
  100% { transform: scale(1.4); opacity: 0; }
}

/* Welcome Text */
.welcome-text {
  flex: 1;
}

.welcome-title {
  margin: 0 0 0.5rem 0;
  font-size: 2.2rem;
  line-height: 1.2;
}

.greeting {
  display: block;
  font-size: 1.2rem;
  color: var(--color-text-secondary);
  font-weight: 400;
  margin-bottom: 0.2rem;
}

.user-name {
  display: block;
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--color-text);
}

.subtitle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  color: var(--color-text-secondary);
  margin-bottom: 0.8rem;
}

.last-login {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  opacity: 0.8;
}

/* Hero Metrics */
.hero-metrics {
  display: flex;
  gap: 1.5rem;
}

/* Sections */
section {
  margin-bottom: 3rem;
}

/* Grids */
.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.progress-content {
  display: grid;
  gap: 2rem;
}

/* Responsive */
@media (max-width: 1024px) {
  .hero-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .hero-metrics {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .home-page {
    padding: 1rem;
  }
  
  .hero-section {
    padding: 2rem 1rem;
  }
  
  .welcome-section {
    flex-direction: column;
    text-align: center;
  }
  
  .welcome-avatar {
    margin: 0 auto;
  }
  
  .welcome-title {
    font-size: 1.8rem;
  }
  
  .user-name {
    font-size: 1.8rem;
  }
  
  .hero-metrics {
    flex-direction: column;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
}
</style> 