<template>
  <div class="home-page">
    <!-- Ultra Modern Background Effects -->
    <div class="ultra-background">
      <div class="neural-network">
        <div class="neural-node" v-for="i in 12" :key="i" :style="{ '--delay': i * 0.5 + 's' }"></div>
      </div>
      <div class="floating-particles">
        <div class="particle" v-for="i in 20" :key="i" :style="{ '--delay': i * 0.3 + 's' }"></div>
      </div>
      <div class="gradient-mesh">
        <div class="mesh-gradient mesh-1"></div>
        <div class="mesh-gradient mesh-2"></div>
        <div class="mesh-gradient mesh-3"></div>
        <div class="mesh-gradient mesh-4"></div>
      </div>
    </div>

    <!-- Ultra Hero Section -->
    <section class="ultra-hero-section">
      <div class="hero-glass-container">
        <div class="hero-content-wrapper">
          <!-- Welcome Avatar with Neural Animation -->
          <div class="ultra-welcome-section">
            <div class="neural-avatar">
              <div class="avatar-neural-ring ring-outer">
                <div class="neural-pulse"></div>
              </div>
              <div class="avatar-neural-ring ring-middle">
                <div class="neural-pulse"></div>
              </div>
              <div class="avatar-neural-ring ring-inner">
                <div class="neural-pulse"></div>
              </div>
              <div class="avatar-core">
                <div class="avatar-glow"></div>
                <div class="avatar-image">
                  <span class="avatar-initials">{{ userInitials }}</span>
                  <div class="avatar-status-indicator"></div>
                </div>
              </div>
              <div class="neural-connections">
                <div class="connection" v-for="i in 8" :key="i" :style="{ '--angle': i * 45 + 'deg' }"></div>
              </div>
            </div>

            <div class="ultra-welcome-text">
              <div class="welcome-badge">
                <i class="fas fa-sparkles"></i>
                <span>Mentor AI</span>
              </div>
              <h1 class="ultra-welcome-title">
                <span class="greeting-line">
                  <span class="greeting-text">Bem-vindo de volta,</span>
                </span>
                <span class="name-line">
                  <span class="user-name-gradient">{{ userName }}!</span>
                  <div class="name-underline"></div>
                </span>
              </h1>
              <p class="ultra-subtitle">
                <i class="fas fa-brain neural-icon"></i>
                <span>Seu assistente de estudos inteligente está pronto para acelerar seu aprendizado</span>
              </p>
              <div class="status-indicators">
                <div class="status-item">
                  <div class="status-dot online"></div>
                  <span>IA Online</span>
                </div>
                <div class="status-item" v-if="lastLogin">
                  <i class="fas fa-clock"></i>
                  <span>{{ formatDate(lastLogin) }}</span>
                </div>
                <div class="status-item">
                  <div class="status-dot synced"></div>
                  <span>Sincronizado</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Ultra Metrics Dashboard -->
          <div class="ultra-metrics-dashboard">
            <div class="metrics-header">
              <h3>
                <i class="fas fa-chart-line"></i>
                Painel de Performance
              </h3>
              <div class="metrics-time-selector">
                <button class="time-btn active">Hoje</button>
                <button class="time-btn">Semana</button>
                <button class="time-btn">Mês</button>
              </div>
            </div>
            <div class="ultra-metrics-grid">
              <div
                v-for="metric in heroMetrics"
                :key="metric.id"
                class="ultra-metric-card"
                :class="metric.trend"
              >
                <div class="metric-icon-container">
                  <div class="metric-icon-bg" :style="{ background: metric.color }"></div>
                  <i :class="metric.icon" class="metric-icon"></i>
                  <div class="metric-pulse"></div>
                </div>
                <div class="metric-content">
                  <div class="metric-value">
                    <span class="value-number">{{ metric.value }}</span>
                    <span class="value-unit">{{ metric.unit }}</span>
                  </div>
                  <div class="metric-label">{{ metric.label }}</div>
                  <div class="metric-trend-indicator">
                    <i :class="metric.trendIcon" class="trend-icon"></i>
                    <span class="trend-value">{{ metric.trendValue }}</span>
                  </div>
                </div>
                <div class="metric-sparkline">
                  <svg viewBox="0 0 100 20" class="sparkline-svg">
                    <polyline
                      :points="metric.sparklineData"
                      fill="none"
                      :stroke="metric.color"
                      stroke-width="2"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Ultra Quick Actions Hub -->
    <section class="ultra-quick-actions">
      <div class="section-glass-container">
        <div class="ultra-section-header">
          <div class="header-content">
            <div class="header-icon-wrapper">
              <div class="header-icon-bg"></div>
              <i class="fas fa-rocket header-icon"></i>
              <div class="header-icon-pulse"></div>
            </div>
            <div class="header-text">
              <h2 class="section-title">Central de Comando</h2>
              <p class="section-subtitle">Acesse suas ferramentas favoritas com um clique</p>
            </div>
          </div>
          <div class="header-actions">
            <button class="customize-btn">
              <i class="fas fa-cog"></i>
              <span>Personalizar</span>
            </button>
          </div>
        </div>

        <div class="ultra-actions-grid">
          <div
            v-for="action in quickActions"
            :key="action.id"
            class="ultra-action-card"
            :class="action.featured ? 'featured' : ''"
            @click="navigateToAction(action)"
          >
            <div class="action-card-bg">
              <div class="card-gradient" :style="{ background: action.gradient }"></div>
              <div class="card-pattern"></div>
            </div>

            <div class="action-card-content">
              <div class="action-icon-container">
                <div class="action-icon-bg" :style="{ background: action.color }"></div>
                <i :class="action.icon" class="action-icon"></i>
                <div class="action-icon-glow"></div>
              </div>

              <div class="action-text">
                <h3 class="action-title">{{ action.title }}</h3>
                <p class="action-description">{{ action.description }}</p>
                <div class="action-stats" v-if="action.stats">
                  <span class="stat-item">
                    <i :class="action.stats.icon"></i>
                    {{ action.stats.value }}
                  </span>
                </div>
              </div>

              <div class="action-arrow">
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>

            <div class="action-card-hover-effect"></div>
            <div class="action-card-border"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- Ultra Progress Analytics -->
    <section class="ultra-progress-analytics">
      <div class="section-glass-container">
        <div class="ultra-section-header">
          <div class="header-content">
            <div class="header-icon-wrapper">
              <div class="header-icon-bg analytics-bg"></div>
              <i class="fas fa-chart-line header-icon"></i>
              <div class="header-icon-pulse analytics-pulse"></div>
            </div>
            <div class="header-text">
              <h2 class="section-title">Analytics Avançado</h2>
              <p class="section-subtitle">Insights detalhados sobre seu progresso e performance</p>
            </div>
          </div>
          <div class="header-actions">
            <div class="time-range-selector">
              <button class="range-btn active">7D</button>
              <button class="range-btn">30D</button>
              <button class="range-btn">90D</button>
            </div>
          </div>
        </div>

        <div class="analytics-dashboard">
          <!-- Main Progress Chart -->
          <div class="main-chart-container">
            <div class="chart-header">
              <h3>Evolução do Aprendizado</h3>
              <div class="chart-legend">
                <div class="legend-item">
                  <div class="legend-color study-time"></div>
                  <span>Tempo de Estudo</span>
                </div>
                <div class="legend-item">
                  <div class="legend-color performance"></div>
                  <span>Performance</span>
                </div>
                <div class="legend-item">
                  <div class="legend-color retention"></div>
                  <span>Retenção</span>
                </div>
              </div>
            </div>
            <div class="chart-canvas">
              <WeeklyChart :data="weeklyProgress" class="ultra-chart" />
            </div>
          </div>

          <!-- Progress Stats Grid -->
          <div class="progress-stats-grid">
            <ProgressStats :stats="progressStats" class="ultra-stats" />
          </div>

          <!-- Performance Insights -->
          <div class="performance-insights">
            <div class="insights-header">
              <h3>
                <i class="fas fa-lightbulb"></i>
                Insights Inteligentes
              </h3>
            </div>
            <PerformanceDetails :metrics="performanceMetrics" class="ultra-performance" />
          </div>
        </div>
      </div>
    </section>

    <!-- Achievements -->
    <section class="achievements">
      <SectionHeader
        icon="fa-medal"
        title="Conquistas"
        subtitle="Desbloqueie recompensas conforme avança nos estudos"
      />
      
      <AchievementsGrid :achievements="achievements" />
    </section>

    <!-- Recent Activities -->
    <section class="recent-activities">
      <SectionHeader
        icon="fa-clock-rotate-left"
        title="Atividades Recentes"
        subtitle="Veja o que você fez recentemente"
      />
      
      <ActivityTimeline :activities="recentActivities" />
    </section>

    <!-- AI Assistant -->
    <section class="ai-assistant">
      <AIAssistantWidget
        :message="currentAIMessage"
        :suggestions="aiSuggestions"
        :loading="aiMessageLoading"
        @send-message="sendAIMessage"
        @suggestion-click="handleAISuggestion"
      />
    </section>

    <!-- Study Resources -->
    <section class="study-resources">
      <SectionHeader
        icon="fa-book"
        title="Recursos Recomendados"
        subtitle="Conteúdo selecionado especialmente para você"
      />
      
      <ResourcesGrid :resources="recommendedResources" />
    </section>

    <!-- Getting Started Guide -->
    <section class="getting-started" v-if="showGettingStarted">
      <SectionHeader
        icon="fa-road"
        title="Guia de Início"
        subtitle="Complete estas etapas para começar"
      />
      
      <GettingStartedSteps
        :steps="gettingStartedSteps"
        @skip="showGettingStarted = false"
      />
    </section>
  </div>
</template>

<script>
import { computed, ref, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

// Component imports
import SectionHeader from './common/SectionHeader.vue';
import MetricCard from './common/MetricCard.vue';
import QuickActionCard from './common/QuickActionCard.vue';
import ProgressStats from './home/<USER>';
import WeeklyChart from './home/<USER>';
import PerformanceDetails from './home/<USER>';
import AchievementsGrid from './home/<USER>';
import ActivityTimeline from './home/<USER>';
import AIAssistantWidget from './home/<USER>';
import ResourcesGrid from './home/<USER>';
import GettingStartedSteps from './home/<USER>';

export default {
  name: 'HomePage',
  components: {
    SectionHeader,
    MetricCard,
    QuickActionCard,
    ProgressStats,
    WeeklyChart,
    PerformanceDetails,
    AchievementsGrid,
    ActivityTimeline,
    AIAssistantWidget,
    ResourcesGrid,
    GettingStartedSteps
  },
  setup() {
    const store = useStore();
    const router = useRouter();

    // User data
    const user = computed(() => store.getters['auth/user']);
    const userName = computed(() => user.value?.name || 'Estudante');
    const userInitials = computed(() => {
      if (user.value?.name) {
        return user.value.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
      }
      return 'ME';
    });

    // Data refs
    const lastLogin = ref(new Date());
    const showGettingStarted = ref(true);
    const aiMessageLoading = ref(false);
    const currentAIMessage = ref('Olá! Como posso ajudar você hoje?');

    // Enhanced Hero metrics with trends and sparklines
    const heroMetrics = ref([
      {
        id: 1,
        icon: 'fas fa-clock',
        value: '2.5',
        unit: 'h',
        label: 'Estudado hoje',
        color: 'linear-gradient(135deg, #667eea, #764ba2)',
        trend: 'up',
        trendIcon: 'fas fa-arrow-up',
        trendValue: '+15%',
        sparklineData: '0,5 10,3 20,7 30,4 40,8 50,6 60,9 70,5 80,7 90,8 100,6'
      },
      {
        id: 2,
        icon: 'fas fa-fire',
        value: '7',
        unit: 'dias',
        label: 'Sequência',
        color: 'linear-gradient(135deg, #f093fb, #f5576c)',
        trend: 'up',
        trendIcon: 'fas fa-arrow-up',
        trendValue: '+2',
        sparklineData: '0,2 10,3 20,4 30,5 40,6 50,7 60,7 70,7 80,7 90,7 100,7'
      },
      {
        id: 3,
        icon: 'fas fa-chart-line',
        value: '85',
        unit: '%',
        label: 'Performance',
        color: 'linear-gradient(135deg, #4facfe, #00f2fe)',
        trend: 'up',
        trendIcon: 'fas fa-arrow-up',
        trendValue: '+5%',
        sparklineData: '0,6 10,7 20,8 30,7 40,8 50,8 60,8 70,9 80,8 90,8 100,9'
      },
      {
        id: 4,
        icon: 'fas fa-trophy',
        value: '12',
        unit: '',
        label: 'Conquistas',
        color: 'linear-gradient(135deg, #fa709a, #fee140)',
        trend: 'up',
        trendIcon: 'fas fa-arrow-up',
        trendValue: '+3',
        sparklineData: '0,4 10,5 20,6 30,7 40,8 50,9 60,10 70,11 80,11 90,12 100,12'
      },
      {
        id: 5,
        icon: 'fas fa-brain',
        value: '94',
        unit: '%',
        label: 'Retenção',
        color: 'linear-gradient(135deg, #a8edea, #fed6e3)',
        trend: 'stable',
        trendIcon: 'fas fa-minus',
        trendValue: '0%',
        sparklineData: '0,9 10,9 20,9 30,9 40,9 50,9 60,9 70,9 80,9 90,9 100,9'
      },
      {
        id: 6,
        icon: 'fas fa-target',
        value: '3',
        unit: '/5',
        label: 'Metas Atingidas',
        color: 'linear-gradient(135deg, #ffecd2, #fcb69f)',
        trend: 'up',
        trendIcon: 'fas fa-arrow-up',
        trendValue: '+1',
        sparklineData: '0,1 10,1 20,2 30,2 40,2 50,3 60,3 70,3 80,3 90,3 100,3'
      }
    ]);

    // Enhanced Quick actions with stats and gradients
    const quickActions = ref([
      {
        id: 'second-brain',
        icon: 'fas fa-brain',
        title: 'Second Brain',
        description: 'Converse com a IA sobre qualquer tópico médico',
        link: '/ia/second-brain',
        color: 'linear-gradient(135deg, #667eea, #764ba2)',
        gradient: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1))',
        featured: true,
        stats: { icon: 'fas fa-comments', value: '24 conversas' }
      },
      {
        id: 'flashcards',
        icon: 'fas fa-layer-group',
        title: 'Flashcards',
        description: 'Revise conceitos com cartões inteligentes',
        link: '/ia/flashcards',
        color: 'linear-gradient(135deg, #4facfe, #00f2fe)',
        gradient: 'linear-gradient(135deg, rgba(79, 172, 254, 0.1), rgba(0, 242, 254, 0.1))',
        stats: { icon: 'fas fa-cards-blank', value: '156 cards' }
      },
      {
        id: 'study-plan',
        icon: 'fas fa-calendar-check',
        title: 'Plano de Estudos',
        description: 'Organize sua rotina de estudos',
        link: '/plano-estudo',
        color: 'linear-gradient(135deg, #43e97b, #38f9d7)',
        gradient: 'linear-gradient(135deg, rgba(67, 233, 123, 0.1), rgba(56, 249, 215, 0.1))',
        stats: { icon: 'fas fa-tasks', value: '3 planos ativos' }
      },
      {
        id: 'questions',
        icon: 'fas fa-question-circle',
        title: 'Questões IA',
        description: 'Pratique com questões comentadas',
        link: '/ia/questions',
        color: 'linear-gradient(135deg, #fa709a, #fee140)',
        gradient: 'linear-gradient(135deg, rgba(250, 112, 154, 0.1), rgba(254, 225, 64, 0.1))',
        stats: { icon: 'fas fa-check-circle', value: '92% acertos' }
      },
      {
        id: 'calendar',
        icon: 'fas fa-calendar-alt',
        title: 'Calendário',
        description: 'Visualize seus compromissos e revisões',
        link: '/calendar',
        color: 'linear-gradient(135deg, #a8edea, #fed6e3)',
        gradient: 'linear-gradient(135deg, rgba(168, 237, 234, 0.1), rgba(254, 214, 227, 0.1))',
        stats: { icon: 'fas fa-calendar-day', value: '5 eventos hoje' }
      },
      {
        id: 'resources',
        icon: 'fas fa-book-open',
        title: 'Recursos',
        description: 'Acesse materiais de estudo',
        link: '/recursos',
        color: 'linear-gradient(135deg, #ffecd2, #fcb69f)',
        gradient: 'linear-gradient(135deg, rgba(255, 236, 210, 0.1), rgba(252, 182, 159, 0.1))',
        stats: { icon: 'fas fa-file-alt', value: '89 documentos' }
      },
      {
        id: 'analytics',
        icon: 'fas fa-chart-bar',
        title: 'Analytics',
        description: 'Acompanhe seu progresso detalhado',
        link: '/analytics',
        color: 'linear-gradient(135deg, #ff9a9e, #fecfef)',
        gradient: 'linear-gradient(135deg, rgba(255, 154, 158, 0.1), rgba(254, 207, 239, 0.1))',
        featured: true,
        stats: { icon: 'fas fa-trending-up', value: '85% performance' }
      },
      {
        id: 'revision',
        icon: 'fas fa-repeat',
        title: 'Revisão Espaçada',
        description: 'Sistema de revisão inteligente',
        link: '/revisao',
        color: 'linear-gradient(135deg, #667eea, #764ba2)',
        gradient: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1))',
        stats: { icon: 'fas fa-clock', value: '12 pendentes' }
      }
    ]);

    // Progress stats
    const progressStats = ref([
      {
        id: 'hours',
        value: 125,
        suffix: 'h',
        title: 'Horas Estudadas',
        description: 'Total de tempo dedicado',
        icon: 'fa-clock',
        trend: '+12%',
        trendType: 'positive'
      },
      {
        id: 'tasks',
        value: 42,
        title: 'Tarefas Concluídas',
        description: 'Atividades finalizadas',
        icon: 'fa-list-check',
        trend: '+7',
        trendType: 'positive'
      },
      {
        id: 'score',
        value: 87,
        suffix: '%',
        title: 'Pontuação Média',
        description: 'Média de acertos',
        icon: 'fa-star',
        trend: '+5%',
        trendType: 'positive'
      },
      {
        id: 'completion',
        value: 78,
        suffix: '%',
        title: 'Taxa de Conclusão',
        description: 'Tarefas vs. planejadas',
        icon: 'fa-check-double',
        trend: '-3%',
        trendType: 'negative'
      }
    ]);

    // Weekly progress data
    const weeklyProgress = ref([
      { day: 'Seg', hours: 2, percentage: 40 },
      { day: 'Ter', hours: 3.5, percentage: 70 },
      { day: 'Qua', hours: 1.5, percentage: 30 },
      { day: 'Qui', hours: 4, percentage: 80 },
      { day: 'Sex', hours: 2.5, percentage: 50, isToday: true },
      { day: 'Sáb', hours: 0, percentage: 0 },
      { day: 'Dom', hours: 0, percentage: 0 }
    ]);

    // Performance metrics
    const performanceMetrics = ref([
      { label: 'Taxa de Retenção', value: '87%', icon: 'fa-brain', color: 'green' },
      { label: 'Foco Médio', value: '42 min', icon: 'fa-bullseye', color: 'orange' },
      { label: 'Produtividade', value: 'Alta', icon: 'fa-rocket', color: 'purple' }
    ]);

    // Achievements
    const achievements = ref([
      {
        id: 1,
        title: 'Iniciante Empolgado',
        description: 'Complete 5 dias seguidos de estudo',
        progress: 100,
        icon: 'fa-fire',
        completed: true
      },
      {
        id: 2,
        title: 'Maratonista de Estudos',
        description: 'Estude por 50 horas acumuladas',
        progress: 80,
        icon: 'fa-trophy',
        completed: false
      },
      {
        id: 3,
        title: 'Memória de Elefante',
        description: 'Complete 100 sessões de estudo',
        progress: 40,
        icon: 'fa-brain',
        completed: false
      }
    ]);

    // Recent activities
    const recentActivities = ref([
      {
        id: 1,
        type: 'flashcard',
        icon: 'fa-clone',
        title: 'Revisão de Flashcards',
        description: 'Completou 30 cartões de Cardiologia',
        time: '30 minutos atrás',
        link: '/ia/flashcards'
      },
      {
        id: 2,
        type: 'ai',
        icon: 'fa-robot',
        title: 'Consulta à IA',
        description: 'Perguntou sobre arritmias cardíacas',
        time: '2 horas atrás',
        link: '/ia/second-brain'
      },
      {
        id: 3,
        type: 'quiz',
        icon: 'fa-clipboard-check',
        title: 'Simulado Concluído',
        description: 'Neurologia - 78% de acertos',
        time: '5 horas atrás',
        link: '/provas-simulados'
      }
    ]);

    // AI suggestions
    const aiSuggestions = ref([
      { text: 'Como organizar meus estudos?', action: 'studyPlan' },
      { text: 'Revisar Cardiologia', action: 'cardiology' },
      { text: 'Ver meu progresso', action: 'progress' },
      { text: 'Próximas tarefas', action: 'tasks' }
    ]);

    // Recommended resources
    const recommendedResources = ref([
      {
        id: 1,
        title: 'Avanços em Cardiologia',
        description: 'Artigo atualizado sobre insuficiência cardíaca',
        icon: 'fa-file-medical',
        tags: ['Cardiologia', 'Pesquisa'],
        type: 'article',
        duration: '15 min'
      },
      {
        id: 2,
        title: 'Neuroanatomia Aplicada',
        description: 'Vídeo aula sobre sistema nervoso central',
        icon: 'fa-video',
        tags: ['Neurologia', 'Vídeo'],
        type: 'video',
        duration: '30 min'
      },
      {
        id: 3,
        title: 'Questões de Endocrinologia',
        description: 'Banco de questões comentadas',
        icon: 'fa-clipboard-question',
        tags: ['Endocrinologia', 'Prática'],
        type: 'questions',
        duration: '45 min'
      }
    ]);

    // Getting started steps
    const gettingStartedSteps = ref([
      {
        id: 1,
        title: 'Configure seu perfil',
        description: 'Defina suas metas e preferências',
        completed: true,
        link: '/profile',
        icon: 'fa-user-cog'
      },
      {
        id: 2,
        title: 'Crie um plano de estudos',
        description: 'Organize sua rotina de estudos',
        completed: false,
        link: '/plano-estudo',
        icon: 'fa-list-check'
      },
      {
        id: 3,
        title: 'Adicione eventos ao calendário',
        description: 'Programe suas sessões de estudo',
        completed: false,
        link: '/calendar',
        icon: 'fa-calendar-days'
      },
      {
        id: 4,
        title: 'Explore o Second Brain',
        description: 'Converse com nossa IA especializada',
        completed: false,
        link: '/ia/second-brain',
        icon: 'fa-brain'
      }
    ]);

    // Methods
    const formatDate = (date) => {
      return format(new Date(date), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });
    };

    const sendAIMessage = (message) => {
      aiMessageLoading.value = true;
      // Simulate AI response
      setTimeout(() => {
        currentAIMessage.value = `Processando sua pergunta: "${message}"...`;
        aiMessageLoading.value = false;
      }, 1500);
    };

    const handleAISuggestion = (action) => {
      switch (action) {
        case 'studyPlan':
          router.push('/plano-estudo');
          break;
        case 'cardiology':
          router.push('/ia/flashcards?topic=cardiologia');
          break;
        case 'progress':
          router.push('/progress-dashboard');
          break;
        case 'tasks':
          router.push('/calendar');
          break;
      }
    };

    const navigateToAction = (action) => {
      router.push(action.link);
    };

    // Lifecycle
    onMounted(() => {
      // Load user data if not already loaded
      if (!user.value) {
        store.dispatch('auth/fetchUser').catch(console.error);
      }
      
      // Check if user is new
      const isNewUser = localStorage.getItem('isNewUser') !== 'false';
      showGettingStarted.value = isNewUser;
    });

    return {
      // User data
      userName,
      userInitials,
      lastLogin,
      
      // UI state
      showGettingStarted,
      aiMessageLoading,
      currentAIMessage,
      
      // Data
      heroMetrics,
      quickActions,
      progressStats,
      weeklyProgress,
      performanceMetrics,
      achievements,
      recentActivities,
      aiSuggestions,
      recommendedResources,
      gettingStartedSteps,
      
      // Methods
      formatDate,
      sendAIMessage,
      handleAISuggestion
    };
  }
};
</script>

<style scoped>
.home-page {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

/* Hero Section */
.hero-section {
  position: relative;
  background: var(--color-bg-secondary);
  border-radius: 16px;
  padding: 3rem 2rem;
  margin-bottom: 3rem;
  overflow: hidden;
  border: 1px solid var(--color-border);
}

.hero-background {
  position: absolute;
  inset: 0;
  opacity: 0.1;
  pointer-events: none;
}

.grid-pattern {
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(66, 185, 131, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(66, 185, 131, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  from { transform: translate(0, 0); }
  to { transform: translate(50px, 50px); }
}

.floating-orbs {
  position: absolute;
  inset: 0;
}

.orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.3;
}

.orb-1 {
  width: 300px;
  height: 300px;
  background: rgba(66, 185, 131, 0.3);
  top: -100px;
  left: -100px;
  animation: orbFloat1 15s ease-in-out infinite;
}

.orb-2 {
  width: 200px;
  height: 200px;
  background: rgba(139, 92, 246, 0.3);
  bottom: -50px;
  right: -50px;
  animation: orbFloat2 20s ease-in-out infinite;
}

.orb-3 {
  width: 150px;
  height: 150px;
  background: rgba(59, 130, 246, 0.3);
  top: 50%;
  left: 50%;
  animation: orbFloat3 18s ease-in-out infinite;
}

@keyframes orbFloat1 {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(30px, -30px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.95); }
}

@keyframes orbFloat2 {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(-40px, -20px) scale(1.05); }
  66% { transform: translate(30px, 30px) scale(0.9); }
}

@keyframes orbFloat3 {
  0%, 100% { transform: translate(-50%, -50%) scale(1); }
  33% { transform: translate(-40%, -60%) scale(1.15); }
  66% { transform: translate(-60%, -40%) scale(0.85); }
}

.hero-content {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 3rem;
}

.welcome-section {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex: 1;
}

/* Welcome Avatar */
.welcome-avatar {
  position: relative;
  width: 80px;
  height: 80px;
  flex-shrink: 0;
}

.avatar-glow {
  position: absolute;
  inset: -10px;
  background: radial-gradient(circle, rgba(66, 185, 131, 0.4) 0%, transparent 70%);
  border-radius: 50%;
  animation: avatarGlow 3s ease-in-out infinite;
}

@keyframes avatarGlow {
  0%, 100% { transform: scale(0.9); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.6; }
}

.avatar-core {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  z-index: 3;
  box-shadow: 0 0 20px rgba(66, 185, 131, 0.5);
}

.avatar-rings {
  position: absolute;
  inset: 0;
  z-index: 2;
}

.ring {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  border: 1px solid rgba(66, 185, 131, 0.3);
}

.ring-1 {
  animation: ringExpand 3s ease-out infinite;
}

.ring-2 {
  animation: ringExpand 3s ease-out infinite 1.5s;
}

@keyframes ringExpand {
  0% { transform: scale(1); opacity: 1; }
  100% { transform: scale(1.4); opacity: 0; }
}

/* Welcome Text */
.welcome-text {
  flex: 1;
}

.welcome-title {
  margin: 0 0 0.5rem 0;
  font-size: 2.2rem;
  line-height: 1.2;
}

.greeting {
  display: block;
  font-size: 1.2rem;
  color: var(--color-text-secondary);
  font-weight: 400;
  margin-bottom: 0.2rem;
}

.user-name {
  display: block;
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--color-text);
}

.subtitle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  color: var(--color-text-secondary);
  margin-bottom: 0.8rem;
}

.last-login {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  opacity: 0.8;
}

/* Hero Metrics */
.hero-metrics {
  display: flex;
  gap: 1.5rem;
}

/* Sections */
section {
  margin-bottom: 3rem;
}

/* Grids */
.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.progress-content {
  display: grid;
  gap: 2rem;
}

/* Responsive */
@media (max-width: 1024px) {
  .hero-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .hero-metrics {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .home-page {
    padding: 1rem;
  }
  
  .hero-section {
    padding: 2rem 1rem;
  }
  
  .welcome-section {
    flex-direction: column;
    text-align: center;
  }
  
  .welcome-avatar {
    margin: 0 auto;
  }
  
  .welcome-title {
    font-size: 1.8rem;
  }
  
  .user-name {
    font-size: 1.8rem;
  }
  
  .hero-metrics {
    flex-direction: column;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
}
</style> 