<template>
  <div class="shared-calendar">
    <div class="calendar-controls">
      <button @click="previousMonth" class="calendar-nav">
        <i class="fas fa-chevron-left"></i>
      </button>
      <h3 class="calendar-month">{{ formattedMonth }}</h3>
      <button @click="nextMonth" class="calendar-nav">
        <i class="fas fa-chevron-right"></i>
      </button>
    </div>
    
    <div class="calendar-grid">
      <div v-for="day in weekDays" :key="day" class="calendar-weekday">
        {{ day }}
      </div>
      <div 
        v-for="(day, index) in calendarDays" 
        :key="`day-${index}`"
        class="calendar-date"
        :class="{ 
          'other-month': !day.isCurrentMonth,
          'today': day.isToday,
          'has-events': day.events?.length > 0,
          'selected': day.isSelected
        }"
        @click="selectDate(day)"
      >
        <span class="date-number">{{ day.day }}</span>
        <div v-if="day.events?.length" class="date-indicators">
          <span 
            v-for="(event, idx) in day.events.slice(0, 3)" 
            :key="idx"
            class="event-dot"
            :style="{ background: event.color || '#667eea' }"
          ></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameMonth, isSameDay, addMonths, subMonths } from 'date-fns'
import { ptBR } from 'date-fns/locale'

export default {
  name: 'SharedCalendar',
  props: {
    events: {
      type: Array,
      default: () => []
    },
    selectedDate: {
      type: Date,
      default: null
    },
    useVuexStore: {
      type: Boolean,
      default: true
    }
  },
  emits: ['date-selected', 'month-changed'],
  setup(props, { emit }) {
    const store = useStore()
    const currentDate = ref(new Date())
    const weekDays = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
    
    const formattedMonth = computed(() => {
      return format(currentDate.value, 'MMMM yyyy', { locale: ptBR })
    })
    
    // Eventos do Vuex ou props
    const calendarEvents = computed(() => {
      if (props.useVuexStore && store.state.calendar) {
        return store.state.calendar.events || []
      }
      return props.events
    })
    
    const calendarDays = computed(() => {
      const start = startOfMonth(currentDate.value)
      const end = endOfMonth(currentDate.value)
      const startDate = startOfWeek(start)
      const endDate = endOfWeek(end)
      
      const days = []
      let date = startDate
      
      while (date <= endDate) {
        const dayEvents = calendarEvents.value.filter(event => {
          const eventDate = new Date(event.date || event.start)
          return isSameDay(eventDate, date)
        })
        
        days.push({
          date: new Date(date),
          day: format(date, 'd'),
          isCurrentMonth: isSameMonth(date, currentDate.value),
          isToday: isSameDay(date, new Date()),
          isSelected: props.selectedDate && isSameDay(date, props.selectedDate),
          events: dayEvents
        })
        date = addDays(date, 1)
      }
      
      return days
    })
    
    const previousMonth = () => {
      currentDate.value = subMonths(currentDate.value, 1)
      emit('month-changed', currentDate.value)
    }
    
    const nextMonth = () => {
      currentDate.value = addMonths(currentDate.value, 1)
      emit('month-changed', currentDate.value)
    }
    
    const selectDate = (day) => {
      if (day.isCurrentMonth) {
        emit('date-selected', day.date)
        if (props.useVuexStore && store) {
          store.commit('calendar/SET_SELECTED_DAY', day.date)
        }
      }
    }
    
    // Carregar eventos quando o mês mudar
    const loadEvents = async () => {
      if (props.useVuexStore && store) {
        const start = startOfMonth(currentDate.value)
        const end = endOfMonth(currentDate.value)
        try {
          await store.dispatch('calendar/fetchEvents', {
            start: start.toISOString(),
            end: end.toISOString()
          })
        } catch (error) {
          console.error('Erro ao carregar eventos:', error)
        }
      }
    }
    
    // Carregar eventos iniciais e quando o mês mudar
    onMounted(() => {
      loadEvents()
    })
    
    watch(currentDate, () => {
      loadEvents()
    })
    
    return {
      currentDate,
      weekDays,
      formattedMonth,
      calendarDays,
      previousMonth,
      nextMonth,
      selectDate
    }
  }
}
</script>

<style scoped>
.shared-calendar {
  width: 100%;
}

.calendar-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding: 0 0.5rem;
}

.calendar-month {
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  margin: 0;
  text-transform: capitalize;
}

.calendar-nav {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.calendar-nav:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  transform: scale(1.05);
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.75rem;
}

.calendar-weekday {
  text-align: center;
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.5);
  padding: 0.75rem 0;
}

.calendar-date {
  aspect-ratio: 1;
  min-height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid transparent;
  padding: 0.5rem;
}

.calendar-date:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

.calendar-date.other-month {
  opacity: 0.3;
  cursor: default;
}

.calendar-date.other-month:hover {
  transform: none;
  background: rgba(255, 255, 255, 0.02);
}

.calendar-date.today {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.calendar-date.selected {
  border: 2px solid #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.calendar-date.has-events {
  font-weight: 600;
}

.date-number {
  font-size: 1.125rem;
  font-weight: 500;
}

.date-indicators {
  position: absolute;
  bottom: 6px;
  display: flex;
  gap: 3px;
}

.event-dot {
  width: 5px;
  height: 5px;
  border-radius: 50%;
}
</style>