<template>
  <div class="revision-scheduler">
    <!-- Background Effects -->
    <div class="background-effects">
      <div class="gradient-bg"></div>
      <div class="floating-particles">
        <div v-for="i in 20" :key="`particle-${i}`" class="particle"></div>
      </div>
    </div>

    <!-- Main Container -->
    <div class="container">
      <!-- Header -->
      <header class="header">
        <div class="header-content">
          <div class="title-section">
            <h1>
              <font-awesome-icon icon="brain" class="brain-icon" />
              Sistema de Revisões Espaçadas
            </h1>
            <p class="subtitle">Metodologia científica para maximizar retenção de conhecimento</p>
          </div>
          <div class="stats-summary">
            <div class="stat-item">
              <span class="stat-value">{{ totalRevisoes }}</span>
              <span class="stat-label">Revisões Agendadas</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ taxaRetencao }}%</span>
              <span class="stat-label">Taxa de Retenção</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ diasEstudados }}</span>
              <span class="stat-label">Dias de Estudo</span>
            </div>
          </div>
        </div>
      </header>

      <!-- Main Content Grid -->
      <div class="content-grid">
        <!-- Estudo Teórico Section -->
        <section class="study-section teorico-section">
          <div class="section-header">
            <h2>
              <font-awesome-icon icon="book" />
              Registro de Estudo Teórico
            </h2>
            <p>Registre seu primeiro contato com o conteúdo</p>
          </div>

          <form @submit.prevent="registrarEstudoTeorico" class="teorico-form">
            <div class="form-group">
              <label>
                <font-awesome-icon icon="bookmark" />
                Matéria / Tópico
              </label>
              <input 
                v-model="estudoTeorico.materia" 
                type="text" 
                placeholder="Ex: Anatomia - Sistema Cardiovascular"
                required
              />
            </div>

            <div class="form-group">
              <label>
                <font-awesome-icon icon="calendar" />
                Data do Estudo Teórico
              </label>
              <input 
                v-model="estudoTeorico.data" 
                type="date" 
                required
              />
            </div>

            <div class="form-group">
              <label>
                <font-awesome-icon icon="signal" />
                Grau de Dificuldade
              </label>
              <div class="difficulty-selector">
                <button 
                  type="button"
                  :class="['diff-option', { active: estudoTeorico.dificuldade === 'Fácil' }]"
                  @click="estudoTeorico.dificuldade = 'Fácil'"
                >
                  <font-awesome-icon icon="smile" />
                  <span>Fácil</span>
                  <small>Primeiro contato em 2 dias</small>
                </button>
                <button 
                  type="button"
                  :class="['diff-option', { active: estudoTeorico.dificuldade === 'Difícil' }]"
                  @click="estudoTeorico.dificuldade = 'Difícil'"
                >
                  <font-awesome-icon icon="frown" />
                  <span>Difícil</span>
                  <small>Primeiro contato em 1 dia</small>
                </button>
              </div>
            </div>

            <div class="form-preview" v-if="estudoTeorico.dificuldade">
              <div class="preview-card">
                <font-awesome-icon icon="calendar-check" class="preview-icon" />
                <div>
                  <strong>Primeiro Contato Agendado:</strong>
                  <p>{{ calcularPrimeiroContato() }}</p>
                </div>
              </div>
            </div>

            <button type="submit" class="submit-btn">
              <font-awesome-icon icon="plus-circle" />
              Registrar Estudo Teórico
            </button>
          </form>
        </section>

        <!-- Revisão Prática Section -->
        <section class="study-section pratica-section">
          <div class="section-header">
            <h2>
              <font-awesome-icon icon="tasks" />
              Registro de Revisão Prática
            </h2>
            <p>Registre seu desempenho em questões</p>
          </div>

          <form @submit.prevent="registrarRevisaoPratica" class="pratica-form">
            <div class="form-group">
              <label>
                <font-awesome-icon icon="bookmark" />
                Matéria / Tópico
              </label>
              <select v-model="revisaoPratica.materiaId" required>
                <option value="">Selecione a matéria</option>
                <option v-for="estudo in estudosTeoricosPendentes" :key="estudo.id" :value="estudo.id">
                  {{ estudo.materia }} - {{ formatDate(estudo.primeiroContato) }}
                </option>
              </select>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>
                  <font-awesome-icon icon="clipboard-list" />
                  Total de Questões
                </label>
                <input 
                  v-model.number="revisaoPratica.totalQuestoes" 
                  type="number" 
                  min="20"
                  max="30"
                  placeholder="20-30"
                  required
                />
              </div>

              <div class="form-group">
                <label>
                  <font-awesome-icon icon="check-circle" />
                  Questões Corretas
                </label>
                <input 
                  v-model.number="revisaoPratica.acertos" 
                  type="number" 
                  min="0"
                  :max="revisaoPratica.totalQuestoes"
                  required
                />
              </div>
            </div>

            <div class="performance-preview" v-if="percentualCalculado">
              <div class="performance-meter">
                <div class="meter-fill" :style="{ width: percentualCalculado + '%' }"></div>
                <span class="meter-label">{{ percentualCalculado }}%</span>
              </div>
              
              <div class="next-revision-preview">
                <font-awesome-icon icon="clock" />
                <div>
                  <strong>Próxima Revisão:</strong>
                  <p>{{ proximaRevisaoTexto }}</p>
                </div>
              </div>
            </div>

            <button type="submit" class="submit-btn" :disabled="!percentualCalculado">
              <font-awesome-icon icon="chart-line" />
              Registrar Desempenho
            </button>
          </form>
        </section>

        <!-- Grades de Revisão -->
        <section class="revision-grid-section">
          <div class="section-header">
            <h2>
              <font-awesome-icon icon="table" />
              Grades de Revisão
            </h2>
            <div class="view-controls">
              <button 
                :class="['view-btn', { active: viewMode === 'teorico' }]"
                @click="viewMode = 'teorico'"
              >
                <font-awesome-icon icon="book" />
                Teórico
              </button>
              <button 
                :class="['view-btn', { active: viewMode === 'pratico' }]"
                @click="viewMode = 'pratico'"
              >
                <font-awesome-icon icon="tasks" />
                Prático
              </button>
              <button 
                :class="['view-btn', { active: viewMode === 'calendario' }]"
                @click="viewMode = 'calendario'"
              >
                <font-awesome-icon icon="calendar" />
                Calendário
              </button>
            </div>
          </div>

          <!-- Grade Teórico -->
          <div v-if="viewMode === 'teorico'" class="revision-table">
            <table>
              <thead>
                <tr>
                  <th>Matéria</th>
                  <th>Data Estudo</th>
                  <th>Dificuldade</th>
                  <th>Primeiro Contato</th>
                  <th>Status</th>
                  <th>Ações</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="estudo in estudosTeoricos" :key="estudo.id">
                  <td class="subject-cell">{{ estudo.materia }}</td>
                  <td>{{ formatDate(estudo.data) }}</td>
                  <td>
                    <span :class="['badge', 'badge-' + estudo.dificuldade.toLowerCase()]">
                      {{ estudo.dificuldade }}
                    </span>
                  </td>
                  <td class="date-cell">{{ formatDate(estudo.primeiroContato) }}</td>
                  <td>
                    <span :class="['status', getStatus(estudo.primeiroContato)]">
                      {{ getStatusText(estudo.primeiroContato) }}
                    </span>
                  </td>
                  <td>
                    <button @click="marcarComoConcluido(estudo.id)" class="action-btn">
                      <font-awesome-icon icon="check" />
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Grade Prático -->
          <div v-if="viewMode === 'pratico'" class="revision-table">
            <table>
              <thead>
                <tr>
                  <th>Matéria</th>
                  <th>Data Revisão</th>
                  <th>Desempenho</th>
                  <th>Intervalo</th>
                  <th>Próxima Revisão</th>
                  <th>Ações</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="revisao in revisoesPraticas" :key="revisao.id">
                  <td class="subject-cell">{{ revisao.materia }}</td>
                  <td>{{ formatDate(revisao.data) }}</td>
                  <td>
                    <div class="performance-cell">
                      <div class="mini-meter">
                        <div class="mini-fill" :style="{ width: revisao.percentual + '%' }"></div>
                      </div>
                      <span>{{ revisao.percentual }}%</span>
                    </div>
                  </td>
                  <td>{{ revisao.intervalo }} dias</td>
                  <td class="date-cell">{{ formatDate(revisao.proximaRevisao) }}</td>
                  <td>
                    <button @click="marcarRevisaoConcluida(revisao.id)" class="action-btn">
                      <font-awesome-icon icon="check" />
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Calendário -->
          <div v-if="viewMode === 'calendario'" class="calendar-view">
            <div class="calendar-header">
              <button @click="previousMonth" class="nav-btn">
                <font-awesome-icon icon="chevron-left" />
              </button>
              <h3>{{ currentMonthYear }}</h3>
              <button @click="nextMonth" class="nav-btn">
                <font-awesome-icon icon="chevron-right" />
              </button>
            </div>
            <div class="calendar-grid">
              <div v-for="day in weekDays" :key="day" class="weekday">{{ day }}</div>
              <div 
                v-for="(day, index) in calendarDays" 
                :key="index"
                :class="['calendar-day', { 
                  'other-month': !day.currentMonth,
                  'today': day.isToday,
                  'has-revision': day.revisions.length > 0
                }]"
              >
                <span class="day-number">{{ day.date }}</span>
                <div v-if="day.revisions.length > 0" class="day-revisions">
                  <div 
                    v-for="rev in day.revisions" 
                    :key="rev.id"
                    :class="['revision-dot', rev.type]"
                    :title="rev.materia"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RevisionSchedulerUpdated',
  
  data() {
    return {
      // Estado do formulário teórico
      estudoTeorico: {
        materia: '',
        data: '',
        dificuldade: ''
      },
      
      // Estado do formulário prático
      revisaoPratica: {
        materiaId: '',
        totalQuestoes: 30,
        acertos: 0
      },
      
      // Dados armazenados
      estudosTeoricos: [],
      revisoesPraticas: [],
      
      // Controles de visualização
      viewMode: 'teorico',
      currentMonth: new Date(),
      
      // Estatísticas
      totalRevisoes: 0,
      taxaRetencao: 85,
      diasEstudados: 0
    }
  },
  
  computed: {
    percentualCalculado() {
      if (this.revisaoPratica.totalQuestoes > 0) {
        return Math.round((this.revisaoPratica.acertos / this.revisaoPratica.totalQuestoes) * 100);
      }
      return 0;
    },
    
    proximaRevisaoTexto() {
      const dias = this.calcularIntervaloRevisao(this.percentualCalculado);
      return `Em ${dias} dias (${this.addDays(new Date(), dias).toLocaleDateString('pt-BR')})`;
    },
    
    estudosTeoricosPendentes() {
      return this.estudosTeoricos.filter(e => !e.concluido);
    },
    
    currentMonthYear() {
      return this.currentMonth.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' });
    },
    
    weekDays() {
      return ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
    },
    
    calendarDays() {
      const year = this.currentMonth.getFullYear();
      const month = this.currentMonth.getMonth();
      const firstDay = new Date(year, month, 1);
      const lastDay = new Date(year, month + 1, 0);
      const startCalendar = new Date(firstDay);
      startCalendar.setDate(startCalendar.getDate() - firstDay.getDay());
      
      const days = [];
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      for (let i = 0; i < 42; i++) {
        const current = new Date(startCalendar);
        current.setDate(startCalendar.getDate() + i);
        
        const dayRevisions = this.getRevisionsForDate(current);
        
        days.push({
          date: current.getDate(),
          currentMonth: current.getMonth() === month,
          isToday: current.getTime() === today.getTime(),
          revisions: dayRevisions
        });
      }
      
      return days;
    }
  },
  
  methods: {
    // Cálculo do primeiro contato baseado na dificuldade
    calcularPrimeiroContato() {
      if (!this.estudoTeorico.data || !this.estudoTeorico.dificuldade) return '';
      
      const dataEstudo = new Date(this.estudoTeorico.data);
      const dias = this.estudoTeorico.dificuldade === 'Fácil' ? 2 : 1;
      
      return this.addDays(dataEstudo, dias).toLocaleDateString('pt-BR');
    },
    
    // Cálculo do intervalo de revisão baseado no desempenho
    calcularIntervaloRevisao(percentual) {
      if (percentual <= 50) return 2;
      if (percentual <= 55) return 7;
      if (percentual <= 60) return 14;
      if (percentual <= 65) return 18;
      if (percentual <= 75) return 24;
      if (percentual <= 80) return 30;
      return 35;
    },
    
    // Registrar estudo teórico
    registrarEstudoTeorico() {
      const dataEstudo = new Date(this.estudoTeorico.data);
      const diasAte = this.estudoTeorico.dificuldade === 'Fácil' ? 2 : 1;
      const primeiroContato = this.addDays(dataEstudo, diasAte);
      
      const novoEstudo = {
        id: Date.now(),
        ...this.estudoTeorico,
        primeiroContato: primeiroContato,
        concluido: false
      };
      
      this.estudosTeoricos.push(novoEstudo);
      this.salvarDados();
      this.atualizarEstatisticas();
      
      // Limpar formulário
      this.estudoTeorico = {
        materia: '',
        data: '',
        dificuldade: ''
      };
      
      this.$toast.success('Estudo teórico registrado com sucesso!');
    },
    
    // Registrar revisão prática
    registrarRevisaoPratica() {
      const estudo = this.estudosTeoricos.find(e => e.id === parseInt(this.revisaoPratica.materiaId));
      if (!estudo) return;
      
      const intervalo = this.calcularIntervaloRevisao(this.percentualCalculado);
      const proximaRevisao = this.addDays(new Date(), intervalo);
      
      const novaRevisao = {
        id: Date.now(),
        materiaId: this.revisaoPratica.materiaId,
        materia: estudo.materia,
        data: new Date(),
        totalQuestoes: this.revisaoPratica.totalQuestoes,
        acertos: this.revisaoPratica.acertos,
        percentual: this.percentualCalculado,
        intervalo: intervalo,
        proximaRevisao: proximaRevisao,
        concluida: false
      };
      
      this.revisoesPraticas.push(novaRevisao);
      estudo.concluido = true;
      this.salvarDados();
      this.atualizarEstatisticas();
      
      // Limpar formulário
      this.revisaoPratica = {
        materiaId: '',
        totalQuestoes: 30,
        acertos: 0
      };
      
      this.$toast.success('Revisão prática registrada! Próxima revisão agendada.');
    },
    
    // Utilidades
    addDays(date, days) {
      const result = new Date(date);
      result.setDate(result.getDate() + days);
      return result;
    },
    
    formatDate(date) {
      if (!date) return '';
      return new Date(date).toLocaleDateString('pt-BR');
    },
    
    getStatus(date) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const compareDate = new Date(date);
      compareDate.setHours(0, 0, 0, 0);
      
      if (compareDate < today) return 'overdue';
      if (compareDate.getTime() === today.getTime()) return 'today';
      return 'upcoming';
    },
    
    getStatusText(date) {
      const status = this.getStatus(date);
      if (status === 'overdue') return 'Atrasado';
      if (status === 'today') return 'Hoje';
      return 'Agendado';
    },
    
    getRevisionsForDate(date) {
      const revisions = [];
      const dateStr = date.toDateString();
      
      // Verificar estudos teóricos
      this.estudosTeoricos.forEach(estudo => {
        if (new Date(estudo.primeiroContato).toDateString() === dateStr) {
          revisions.push({
            id: estudo.id,
            type: 'teorico',
            materia: estudo.materia
          });
        }
      });
      
      // Verificar revisões práticas
      this.revisoesPraticas.forEach(revisao => {
        if (new Date(revisao.proximaRevisao).toDateString() === dateStr) {
          revisions.push({
            id: revisao.id,
            type: 'pratico',
            materia: revisao.materia
          });
        }
      });
      
      return revisions;
    },
    
    // Navegação do calendário
    previousMonth() {
      this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() - 1);
    },
    
    nextMonth() {
      this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() + 1);
    },
    
    // Ações
    marcarComoConcluido(id) {
      const estudo = this.estudosTeoricos.find(e => e.id === id);
      if (estudo) {
        estudo.concluido = true;
        this.salvarDados();
      }
    },
    
    marcarRevisaoConcluida(id) {
      const revisao = this.revisoesPraticas.find(r => r.id === id);
      if (revisao) {
        revisao.concluida = true;
        this.salvarDados();
      }
    },
    
    // Persistência de dados
    salvarDados() {
      localStorage.setItem('estudosTeoricos', JSON.stringify(this.estudosTeoricos));
      localStorage.setItem('revisoesPraticas', JSON.stringify(this.revisoesPraticas));
    },
    
    carregarDados() {
      const estudos = localStorage.getItem('estudosTeoricos');
      const revisoes = localStorage.getItem('revisoesPraticas');
      
      if (estudos) this.estudosTeoricos = JSON.parse(estudos);
      if (revisoes) this.revisoesPraticas = JSON.parse(revisoes);
      
      this.atualizarEstatisticas();
    },
    
    atualizarEstatisticas() {
      this.totalRevisoes = this.estudosTeoricos.length + this.revisoesPraticas.length;
      
      const diasUnicos = new Set();
      this.estudosTeoricos.forEach(e => diasUnicos.add(new Date(e.data).toDateString()));
      this.revisoesPraticas.forEach(r => diasUnicos.add(new Date(r.data).toDateString()));
      this.diasEstudados = diasUnicos.size;
      
      // Calcular taxa de retenção média
      if (this.revisoesPraticas.length > 0) {
        const somaPercentuais = this.revisoesPraticas.reduce((sum, r) => sum + r.percentual, 0);
        this.taxaRetencao = Math.round(somaPercentuais / this.revisoesPraticas.length);
      }
    }
  },
  
  mounted() {
    this.carregarDados();
  }
}
</script>

<style scoped>
/* Reset and Variables */
:root {
  --primary: #6366f1;
  --secondary: #8b5cf6;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --dark: #1e293b;
  --darker: #0f172a;
  --light: #f8fafc;
  --text: #94a3b8;
  --border: #334155;
}

* {
  box-sizing: border-box;
}

/* Main Container */
.revision-scheduler {
  min-height: 100vh;
  background: var(--darker);
  color: var(--light);
  position: relative;
  overflow-x: hidden;
}

/* Background Effects */
.background-effects {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.gradient-bg {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at 20% 50%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
}

.floating-particles {
  position: absolute;
  inset: 0;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--primary);
  border-radius: 50%;
  opacity: 0.3;
  animation: float 20s infinite ease-in-out;
}

.particle:nth-child(odd) {
  animation-duration: 25s;
  background: var(--secondary);
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  33% {
    transform: translateY(-100px) translateX(50px);
  }
  66% {
    transform: translateY(-50px) translateX(-50px);
  }
}

/* Container */
.container {
  position: relative;
  z-index: 1;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

/* Header */
.header {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border);
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.title-section h1 {
  font-size: 2rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.brain-icon {
  color: var(--primary);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.subtitle {
  color: var(--text);
  margin: 0.5rem 0 0;
}

.stats-summary {
  display: flex;
  gap: 3rem;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  color: var(--primary);
}

.stat-label {
  display: block;
  font-size: 0.875rem;
  color: var(--text);
  margin-top: 0.25rem;
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

/* Study Sections */
.study-section {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border);
  border-radius: 1rem;
  padding: 2rem;
}

.section-header {
  margin-bottom: 2rem;
}

.section-header h2 {
  font-size: 1.5rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-header p {
  color: var(--text);
  margin: 0.5rem 0 0;
}

/* Forms */
.teorico-form,
.pratica-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-group input,
.form-group select {
  background: var(--dark);
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  padding: 0.75rem;
  color: var(--light);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* Difficulty Selector */
.difficulty-selector {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.diff-option {
  background: var(--dark);
  border: 2px solid var(--border);
  border-radius: 0.75rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.diff-option:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
}

.diff-option.active {
  border-color: var(--primary);
  background: rgba(99, 102, 241, 0.1);
}

.diff-option span {
  font-weight: 600;
}

.diff-option small {
  font-size: 0.75rem;
  color: var(--text);
}

/* Preview Cards */
.form-preview,
.performance-preview {
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid var(--primary);
  border-radius: 0.75rem;
  padding: 1rem;
}

.preview-card {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.preview-icon {
  font-size: 2rem;
  color: var(--primary);
}

/* Performance Meter */
.performance-meter {
  position: relative;
  height: 2rem;
  background: var(--dark);
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1rem;
}

.meter-fill {
  position: absolute;
  height: 100%;
  background: linear-gradient(90deg, var(--danger), var(--warning), var(--success));
  transition: width 0.5s ease;
}

.meter-label {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.875rem;
}

.next-revision-preview {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
}

/* Submit Button */
.submit-btn {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  border: none;
  border-radius: 0.75rem;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
}

.submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Revision Grid Section */
.revision-grid-section {
  grid-column: 1 / -1;
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border);
  border-radius: 1rem;
  padding: 2rem;
}

.view-controls {
  display: flex;
  gap: 0.5rem;
}

.view-btn {
  background: var(--dark);
  border: 1px solid var(--border);
  color: var(--text);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.view-btn:hover {
  border-color: var(--primary);
  color: var(--light);
}

.view-btn.active {
  background: var(--primary);
  border-color: var(--primary);
  color: white;
}

/* Tables */
.revision-table {
  overflow-x: auto;
  margin-top: 2rem;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th {
  background: var(--dark);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  border-bottom: 2px solid var(--border);
}

td {
  padding: 1rem;
  border-bottom: 1px solid var(--border);
}

tr:hover {
  background: rgba(99, 102, 241, 0.05);
}

/* Badges */
.badge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 600;
}

.badge-fácil {
  background: rgba(16, 185, 129, 0.2);
  color: var(--success);
}

.badge-difícil {
  background: rgba(239, 68, 68, 0.2);
  color: var(--danger);
}

/* Status */
.status {
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.status.overdue {
  background: rgba(239, 68, 68, 0.2);
  color: var(--danger);
}

.status.today {
  background: rgba(245, 158, 11, 0.2);
  color: var(--warning);
}

.status.upcoming {
  background: rgba(99, 102, 241, 0.2);
  color: var(--primary);
}

/* Performance Cell */
.performance-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.mini-meter {
  flex: 1;
  height: 0.5rem;
  background: var(--dark);
  border-radius: 0.25rem;
  overflow: hidden;
}

.mini-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--danger), var(--warning), var(--success));
}

/* Action Button */
.action-btn {
  background: var(--success);
  color: white;
  border: none;
  padding: 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #059669;
  transform: scale(1.1);
}

/* Calendar View */
.calendar-view {
  margin-top: 2rem;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.calendar-header h3 {
  margin: 0;
  text-transform: capitalize;
}

.nav-btn {
  background: var(--dark);
  border: 1px solid var(--border);
  color: var(--light);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  border-color: var(--primary);
  color: var(--primary);
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: var(--border);
  border: 1px solid var(--border);
  border-radius: 0.75rem;
  overflow: hidden;
}

.weekday {
  background: var(--dark);
  padding: 1rem;
  text-align: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.calendar-day {
  background: rgba(30, 41, 59, 0.5);
  min-height: 80px;
  padding: 0.5rem;
  position: relative;
  transition: all 0.3s ease;
}

.calendar-day:hover {
  background: rgba(99, 102, 241, 0.1);
}

.calendar-day.other-month {
  opacity: 0.3;
}

.calendar-day.today {
  background: rgba(99, 102, 241, 0.2);
}

.calendar-day.has-revision {
  border: 2px solid var(--primary);
}

.day-number {
  font-weight: 600;
  font-size: 0.875rem;
}

.day-revisions {
  display: flex;
  gap: 0.25rem;
  margin-top: 0.5rem;
  flex-wrap: wrap;
}

.revision-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.revision-dot.teorico {
  background: var(--primary);
}

.revision-dot.pratico {
  background: var(--secondary);
}

/* Responsive */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stats-summary {
    justify-content: center;
  }
}

@media (max-width: 640px) {
  .container {
    padding: 1rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .difficulty-selector {
    grid-template-columns: 1fr;
  }
  
  .view-controls {
    flex-wrap: wrap;
  }
  
  .calendar-grid {
    font-size: 0.75rem;
  }
  
  .calendar-day {
    min-height: 60px;
  }
}

/* Toast Animations */
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.toast-leave-to {
  transform: translateX(100%);
  opacity: 0;
}
</style>