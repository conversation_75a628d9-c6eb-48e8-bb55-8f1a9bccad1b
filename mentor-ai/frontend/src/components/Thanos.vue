<template>
  <div class="thanos-container">
    <!-- Premium Hero Header -->
    <header class="thanos-hero">
      <!-- Animated Background -->
      <div class="hero-background">
        <div class="floating-elements">
          <div class="float-element element-1"></div>
          <div class="float-element element-2"></div>
          <div class="float-element element-3"></div>
          <div class="float-element element-4"></div>
        </div>
        <div class="grid-overlay"></div>
      </div>
      
      <!-- Hero Content -->
      <div class="hero-content">
        <div class="hero-wrapper">
          <!-- Main Title Section -->
          <div class="hero-title-section">
            <div class="title-content">
              <h1 class="hero-title">
                <span class="title-main">Thanos</span>
                <span class="title-ai">AI</span>
              </h1>
              <p class="hero-subtitle">
                Seu assistente de estudos com inteligência artificial avançada
              </p>
            </div>
          </div>
          
          <!-- Enhanced Search -->
          <div class="hero-search">
            <div class="search-wrapper">
              <div class="search-icon-container">
                <font-awesome-icon icon="fa-solid fa-brain" />
              </div>
              <textarea 
                v-model="userInput"
                placeholder="Digite sua pergunta, tópico ou comando..."
                class="search-input"
                rows="1"
                @keydown.enter.prevent="sendMessage"
                @input="autoResize"
              ></textarea>
              <transition name="fade">
                <button v-if="userInput.trim()" @click="clearInput" class="search-clear">
                  <font-awesome-icon icon="fa-solid fa-times" />
                </button>
              </transition>
            </div>
          </div>
          
          <!-- Stats Cards -->
          <div class="stats-showcase">
            <div class="stat-card">
              <div class="stat-icon">
                <font-awesome-icon icon="fa-solid fa-comments" />
              </div>
              <div class="stat-content">
                <span class="stat-number">{{ chatSessions.length }}</span>
                <span class="stat-label">Conversas</span>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">
                <font-awesome-icon icon="fa-solid fa-brain" />
              </div>
              <div class="stat-content">
                <span class="stat-number">{{ isInitialized ? 'ON' : 'OFF' }}</span>
                <span class="stat-label">IA Inicializada</span>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">
                <font-awesome-icon icon="fa-solid fa-file-medical" />
              </div>
              <div class="stat-content">
                <span class="stat-number">{{ documentSummary ? '1' : '0' }}</span>
                <span class="stat-label">Documentos</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Filter Pills for Chat Management -->
    <section class="filter-section">
      <div class="filter-container">
        <button 
          v-for="option in chatOptions" 
          :key="option.id"
          @click="activeOption = option.id"
          :class="['filter-pill', { 'active': activeOption === option.id }]"
        >
          <font-awesome-icon :icon="option.icon" class="pill-icon" />
          <span>{{ option.name }}</span>
        </button>
      </div>
    </section>

    <!-- Main Content -->
    <main class="main-content">
      <!-- Chat Section with Enhanced Title -->
      <section class="chat-section">
        <div class="section-header">
          <div class="section-title">
            <div class="title-content">
              <font-awesome-icon icon="fa-solid fa-comments" class="section-icon" />
              <span class="title-text">Conversa <span class="highlight">Ativa</span></span>
            </div>
          </div>
          <p class="section-description">Interaja com o Thanos AI de forma inteligente e contextual</p>
        </div>

        <!-- Chat Interface -->
        <div class="chat-interface">
          <!-- Session Title -->
          <div v-if="currentSession" class="chat-header">
            <input
              v-model="currentSession.title"
              type="text"
              class="session-input-title"
              placeholder="Dê um título a esta conversa..."
              @blur="updateSessionTitle"
            />
          </div>

          <!-- Document Summary -->
          <div v-if="isInitialized && showSummary && documentSummary" class="document-summary">
            <div class="summary-header">
              <font-awesome-icon icon="fa-solid fa-file-medical-alt" />
              <span>Resumo do Documento</span>
            </div>
            <div class="summary-content">
              {{ documentSummary }}
            </div>
          </div>

          <!-- Chat History -->
          <div class="chat-history" ref="chatContainer">
            <div v-if="!isInitialized && currentSession?.messages?.length === 0" class="welcome-message">
              <div class="welcome-icon">
                <font-awesome-icon icon="fa-solid fa-brain" />
              </div>
              <h3>Bem-vindo ao Thanos AI</h3>
              <p>Para começar, você pode:</p>
              <ul>
                <li>Inicializar o assistente com um documento</li>
                <li>Fazer uma pergunta diretamente para usar o modo conversacional</li>
                <li>Explorar as funcionalidades nas abas laterais</li>
              </ul>
            </div>

            <div
              v-for="(msg, index) in currentSession?.messages || []"
              :key="index"
              :class="['message', msg.sender]"
            >
              <div class="message-avatar">
                <font-awesome-icon 
                  :icon="msg.sender === 'user' ? 'fa-solid fa-user' : 'fa-solid fa-brain'" 
                />
              </div>
              <div class="message-content">
                <div class="message-text" v-html="msg.text.replace(/\n/g, '<br>')"></div>
                <small class="message-meta">{{ formatTimestamp(msg.timestamp) }}</small>
              </div>
            </div>

            <div v-if="isLoading && currentSession?.messages?.length > 0" class="typing-indicator">
              <div class="typing-avatar">
                <font-awesome-icon icon="fa-solid fa-brain" />
              </div>
              <div class="typing-content">
                <div class="typing-dots">
                  <div class="typing-dot"></div>
                  <div class="typing-dot"></div>
                  <div class="typing-dot"></div>
                </div>
                <span class="typing-text">Thanos está pensando...</span>
              </div>
            </div>
          </div>

          <!-- Chat Input -->
          <div class="chat-input-area">
            <div class="input-wrapper">
              <button 
                v-if="!isInitialized"
                @click="openInitModal"
                class="init-trigger-button"
              >
                <font-awesome-icon icon="fa-solid fa-rocket" />
                <span>Inicializar IA</span>
              </button>
              
              <button @click="sendMessage" :disabled="isLoading || !userInput.trim()" class="send-button">
                <font-awesome-icon :icon="isLoading ? 'fa-spinner fa-spin' : 'fa-paper-plane'" />
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- Sidebar Content in Cards -->
      <aside class="sidebar-content">
        <!-- Chat Sessions -->
        <div class="sidebar-card">
          <div class="card-header">
            <font-awesome-icon icon="fa-solid fa-history" />
            <h3>Histórico</h3>
          </div>
          <div class="card-content">
            <div class="sessions-list">
              <div
                v-for="(session, idx) in chatSessions"
                :key="idx"
                :class="['session-item', { 'active': idx === selectedSessionIndex }]"
                @click="selectSession(idx)"
              >
                <div class="session-title">
                  {{ session.title || 'Conversa sem Título' }}
                </div>
                <small class="session-meta">
                  {{ formatTimestamp(session.updatedAt) }}
                </small>
              </div>
            </div>
            <div class="session-actions">
              <button @click="createNewSession" class="action-button primary">
                <font-awesome-icon icon="fa-solid fa-plus" />
                <span>Nova Conversa</span>
              </button>
              <button @click="clearHistory" class="action-button secondary">
                <font-awesome-icon icon="fa-solid fa-trash" />
                <span>Limpar</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Enhanced Analytics -->
        <div class="sidebar-card">
          <div class="card-header">
            <font-awesome-icon icon="fa-solid fa-chart-line" />
            <h3>Estatísticas</h3>
          </div>
          <div class="card-content">
            <div class="stats-grid">
              <div class="stat-item">
                <span class="stat-value">{{ $store.getters['thanos/totalTokensUsed'] }}</span>
                <span class="stat-label">Tokens Usados</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ Math.round($store.getters['thanos/averageResponseTime']) }}ms</span>
                <span class="stat-label">Tempo Médio</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ isInitialized ? selectedProvider : 'N/A' }}</span>
                <span class="stat-label">Provedor IA</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ isInitialized ? selectedModel : 'N/A' }}</span>
                <span class="stat-label">Modelo</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Notes -->
        <div class="sidebar-card">
          <div class="card-header">
            <font-awesome-icon icon="fa-solid fa-sticky-note" />
            <h3>Anotações</h3>
          </div>
          <div class="card-content">
            <textarea
              v-model="quickNotes"
              placeholder="Digite suas anotações rápidas..."
              class="notes-textarea"
              rows="4"
            ></textarea>
            <button @click="saveQuickNotes" class="action-button primary">
              <font-awesome-icon icon="fa-solid fa-save" />
              <span>Salvar</span>
            </button>
          </div>
        </div>

        <!-- Quick Prompts -->
        <div class="sidebar-card">
          <div class="card-header">
            <font-awesome-icon icon="fa-solid fa-lightbulb" />
            <h3>Prompts Rápidos</h3>
          </div>
          <div class="card-content">
            <div class="quick-prompts">
              <div 
                v-for="prompt in quickPrompts"
                :key="prompt.id"
                @click="insertPrompt(prompt.text)"
                class="prompt-item"
              >
                <font-awesome-icon :icon="prompt.icon" />
                <span>{{ prompt.title }}</span>
              </div>
            </div>
          </div>
        </div>
      </aside>
    </main>

    <!-- Initialization Modal -->
    <transition name="modal-fade">
      <div v-if="showInitModal" class="modal-overlay" @click="showInitModal = false">
        <div class="modal-content" @click.stop>
          
          <!-- Thanos Animation Overlay -->
          <div v-if="thanosAnimationActive" class="thanos-animation-overlay" :class="thanosAnimationPhase">
            <div class="infinity-gauntlet">
              <div class="gauntlet-base">
                <div 
                  v-for="stone in infinityStones" 
                  :key="stone.name"
                  class="infinity-stone"
                  :class="{ active: stone.active, [stone.name.toLowerCase()]: true }"
                  :style="{ '--stone-color': stone.color }"
                >
                  <div class="stone-glow"></div>
                  <div class="stone-core"></div>
                </div>
              </div>
              
              <div class="power-waves" v-if="thanosAnimationPhase === 'power'">
                <div class="wave wave-1"></div>
                <div class="wave wave-2"></div>
                <div class="wave wave-3"></div>
              </div>
              
              <div class="snap-effect" v-if="thanosAnimationPhase === 'snap'">
                <div class="snap-particles">
                  <div v-for="i in 20" :key="i" class="particle" :style="{ '--delay': i * 0.1 + 's' }"></div>
                </div>
                <div class="reality-crack"></div>
              </div>
            </div>
            
            <div class="animation-text">
              <h2 v-if="thanosAnimationPhase === 'gathering'">
                <span class="text-glow">Coletando as Pedras do Infinito...</span>
              </h2>
              <h2 v-if="thanosAnimationPhase === 'assembling'">
                <span class="text-glow">Assemblando o Gauntlet...</span>
              </h2>
              <h2 v-if="thanosAnimationPhase === 'power'">
                <span class="text-glow">Canalizando o Poder Cósmico...</span>
              </h2>
              <h2 v-if="thanosAnimationPhase === 'complete'">
                <span class="text-glow success">Thanos AI Inicializado!</span>
              </h2>
              <h2 v-if="thanosAnimationPhase === 'snap'">
                <span class="text-glow snap">*SNAP* 🫰</span>
              </h2>
              <h2 v-if="thanosAnimationPhase === 'failed'">
                <span class="text-glow error">Falha na Inicialização...</span>
              </h2>
            </div>
          </div>
          <div class="modal-header">
            <h2>Inicializar Thanos AI</h2>
            <button @click="showInitModal = false" class="modal-close">
              <font-awesome-icon icon="fa-solid fa-times" />
            </button>
          </div>
          
          <div class="demo-notice">
            <div class="notice-content">
              <font-awesome-icon icon="fa-solid fa-info-circle" />
              <span>💡 Clique no botão abaixo para ver a animação épica do Thanos!</span>
            </div>
            <div class="demo-actions">
              <button @click="initializeThanos" :disabled="isLoading" class="demo-button">
                <font-awesome-icon :icon="isLoading ? 'fa-spinner fa-spin' : 'fa-play'" />
                <span>{{ isLoading ? 'Executando...' : 'Testar Animação' }}</span>
              </button>
            </div>
            <div class="debug-info" v-if="isLoading">
              <small>Debug: isLoading = {{ isLoading }} | Animation = {{ thanosAnimationActive }}</small>
              <button @click="isLoading = false; thanosAnimationActive = false" class="debug-reset">Reset</button>
            </div>
          </div>
          
          <div class="modal-body">
            <div class="init-tabs">
              <div class="tab-header">
                <div
                  v-for="tab in initTabs"
                  :key="tab.id"
                  :class="['tab-item', { active: activeInitTab === tab.id }]"
                  @click="activeInitTab = tab.id"
                >
                  <font-awesome-icon :icon="tab.icon" />
                  <span>{{ tab.name }}</span>
                </div>
              </div>

              <div class="tab-content">
                <!-- Document Tab -->
                <div v-show="activeInitTab === 'document'" class="tab-pane">
                  <div class="form-group">
                    <label>Tipo de Documento:</label>
                    <select v-model="selectedDocumentType" class="form-select">
                      <option value="Texto">Texto</option>
                      <option value="PDF">PDF</option>
                      <option value="Site">Site</option>
                      <option value="Youtube">YouTube</option>
                      <option value="CSV">CSV</option>
                    </select>
                  </div>

                  <div v-if="['Site', 'Youtube'].includes(selectedDocumentType)" class="form-group">
                    <label>URL:</label>
                    <input
                      type="text"
                      v-model="documentUrl"
                      :placeholder="selectedDocumentType === 'Site' ? 'https://exemplo.com' : 'https://youtube.com/watch?v=ID'"
                      class="form-input"
                    />
                  </div>

                  <div v-if="['PDF', 'Texto', 'CSV'].includes(selectedDocumentType)" class="form-group">
                    <label>Upload de Arquivo:</label>
                    <input
                      type="file"
                      @change="handleFileUpload"
                      :accept="getFileAccept()"
                      class="form-file"
                    />
                    <div v-if="documentFile" class="file-preview">
                      <font-awesome-icon icon="fa-solid fa-file" />
                      <span>{{ documentFile.name }}</span>
                    </div>
                  </div>
                </div>

                <!-- Model Tab -->
                <div v-show="activeInitTab === 'model'" class="tab-pane">
                  <div class="form-group">
                    <label>Provedor:</label>
                    <select v-model="selectedProvider" class="form-select">
                      <option v-for="provider in availableProviders" :key="provider">
                        {{ provider }}
                      </option>
                    </select>
                  </div>

                  <div class="form-group">
                    <label>Modelo:</label>
                    <select v-model="selectedModel" class="form-select">
                      <option v-for="model in getModelsForProvider(selectedProvider)" :key="model">
                        {{ model }}
                      </option>
                    </select>
                  </div>

                  <div class="form-group">
                    <label>API Key:</label>
                    <input
                      type="password"
                      v-model="apiKey"
                      placeholder="Sua API key aqui"
                      class="form-input"
                    />
                  </div>
                </div>

                <!-- Advanced Tab -->
                <div v-show="activeInitTab === 'advanced'" class="tab-pane">
                  <div class="form-group">
                    <label class="checkbox-label">
                      <input type="checkbox" v-model="showSummary">
                      <span class="checkbox-custom"></span>
                      Mostrar resumo do documento
                    </label>
                  </div>

                  <div class="form-group">
                    <label class="checkbox-label">
                      <input type="checkbox" v-model="useRAG">
                      <span class="checkbox-custom"></span>
                      Usar RAG (Retrieval Augmented Generation)
                    </label>
                  </div>

                  <div class="form-group">
                    <label>Idioma:</label>
                    <select v-model="language" class="form-select">
                      <option value="pt">Português</option>
                      <option value="en">English</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <button @click="showInitModal = false" class="action-button secondary">
              Cancelar
            </button>
            <button
              @click="initializeThanos"
              :disabled="isLoading"
              class="action-button primary"
            >
              <font-awesome-icon :icon="isLoading ? 'fa-spinner fa-spin' : 'fa-rocket'" />
              <span>{{ isLoading ? 'Inicializando...' : 'Inicializar' }}</span>
            </button>
          </div>
        </div>
      </div>
    </transition>

    <!-- Creative Floating Actions -->
    <div class="floating-hub" :class="{ 'expanded': fabExpanded }">
      <!-- Main Trigger Button -->
      <button 
        @click="toggleFab"
        class="fab-trigger"
        :class="{ 'active': fabExpanded }"
      >
        <font-awesome-icon :icon="fabExpanded ? 'fa-times' : 'fa-brain'" />
      </button>

      <!-- Action Buttons -->
      <transition-group name="fab-transition" tag="div">
        <button 
          v-for="(action, index) in quickActions" 
          v-show="fabExpanded"
          :key="action.id"
          class="fab-action"
          :style="getActionStyle(index)"
          @click="executeAction(action)"
        >
          <font-awesome-icon :icon="action.icon" />
          <span class="action-label">{{ action.label }}</span>
        </button>
      </transition-group>
    </div>
  </div>
</template>

<script>
import { computed, ref, onMounted, nextTick, watch } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { thanosService } from '@/services/thanos'
import { playSound } from '@/utils/sound'

// Translations for i18n support
const TRANSLATIONS = {
  'pt': {
    'bem_vindo': 'Bem-vindo ao Thanos 🧠',
    'inicializar_chat': 'Inicializar Thanos',
    'apagar_historico': 'Apagar Histórico de Conversa',
    'resumo_doc': 'Resumo do Documento',
    'inicializacao_sucesso': 'Thanos inicializado com sucesso! Como posso ajudar com seus estudos hoje?'
  }
}

// Model configuration
const CONFIG_MODELOS = {
  'OpenAI': {
    'modelos': ['gpt-4o-mini', 'gpt-4o', 'gpt-4-turbo', 'gpt-3.5-turbo']
  },
  'Groq': {
    'modelos': ['llama-3.1-70b-versatile', 'gemma2-9b-it', 'mixtral-8x7b-32768']
  },
  'Anthropic': {
    'modelos': ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku', 'claude-2.1']
  }
}

export default {
  name: 'Thanos',
  setup() {
    const store = useStore()
    const router = useRouter()

    // Reactive data
    const userInput = ref('')
    const isLoading = ref(false)
    const showInitModal = ref(false)
    const activeOption = ref('chat')
    const activeInitTab = ref('document')
    const fabExpanded = ref(false)
    
    // Animação Thanos
    const thanosAnimationActive = ref(false)
    const thanosAnimationPhase = ref('')
    const infinityStones = ref([
      { name: 'Mind', color: '#FFD700', active: false },
      { name: 'Soul', color: '#FF6B35', active: false },
      { name: 'Time', color: '#00FF41', active: false },
      { name: 'Space', color: '#0099FF', active: false },
      { name: 'Reality', color: '#FF0040', active: false },
      { name: 'Power', color: '#9932CC', active: false }
    ])

    // Document handling
    const selectedDocumentType = ref('Texto')
    const documentUrl = ref('')
    const documentFile = ref(null)

    // Model and provider selection
    const selectedProvider = ref('OpenAI')
    const selectedModel = ref('gpt-4o')
    const apiKey = ref('')
    const availableProviders = Object.keys(CONFIG_MODELOS)
    const apiKeyMasked = computed(() => {
      if (!apiKey.value) return ''
      try {
        return apiKey.value.length > 8 
          ? apiKey.value.substring(0, 4) + '*'.repeat(apiKey.value.length - 8) + apiKey.value.substring(apiKey.value.length - 4)
          : '*'.repeat(apiKey.value.length)
      } catch (error) {
        return ''
      }
    })

    // Settings with enhanced configuration
    const language = ref('pt')
    const showSummary = ref(true)
    const useRAG = ref(false)
    const documentSummary = ref('')
    const ragSettings = ref({
      enabled: false,
      chunkSize: 1000,
      chunkOverlap: 200,
      similarityThreshold: 0.75,
      maxChunks: 5
    })
    const modelSettings = ref({
      temperature: 0.7,
      maxTokens: 2000,
      streamResponses: false
    })

    // Notes
    const quickNotes = ref(localStorage.getItem('thanos_quickNotes') || '')

    // Computed state
    const isInitialized = computed(() => store.getters['thanos/isInitialized'])
    const chatSessions = computed(() => store.getters['thanos/allSessions'])
    const selectedSessionIndex = ref(0)
    const currentSession = computed(() => {
      return chatSessions.value[selectedSessionIndex.value] || null
    })

    // UI Configuration
    const chatOptions = ref([
      { id: 'chat', name: 'Chat', icon: 'fa-solid fa-comments' },
      { id: 'history', name: 'Histórico', icon: 'fa-solid fa-history' },
      { id: 'settings', name: 'Configurações', icon: 'fa-solid fa-cog' }
    ])

    const initTabs = ref([
      { id: 'document', name: 'Documento', icon: 'fa-solid fa-file' },
      { id: 'model', name: 'Modelo', icon: 'fa-solid fa-brain' },
      { id: 'advanced', name: 'Avançado', icon: 'fa-solid fa-cog' }
    ])

    const quickPrompts = ref([
      {
        id: 1,
        title: 'Neuroplasticidade',
        icon: 'fa-solid fa-brain',
        text: 'Explique como funciona a neuroplasticidade e como ela se relaciona com o aprendizado.'
      },
      {
        id: 2,
        title: 'Fisiologia Cardíaca',
        icon: 'fa-solid fa-heart',
        text: 'Liste 5 pontos-chave sobre fisiologia cardíaca que são frequentemente cobrados em provas.'
      },
      {
        id: 3,
        title: 'Diabetes',
        icon: 'fa-solid fa-syringe',
        text: 'Quais são as principais complicações da diabetes e como diagnosticá-las precocemente?'
      },
      {
        id: 4,
        title: 'Antibióticos',
        icon: 'fa-solid fa-pills',
        text: 'Explique o mecanismo de ação dos antibióticos beta-lactâmicos.'
      }
    ])

    const quickActions = ref([
      { 
        id: 'new-chat', 
        label: 'Nova Conversa', 
        icon: 'fa-plus',
        action: 'createNewSession'
      },
      { 
        id: 'init-ai', 
        label: 'Inicializar IA', 
        icon: 'fa-rocket',
        action: 'showInit'
      },
      { 
        id: 'export', 
        label: 'Exportar', 
        icon: 'fa-download',
        action: 'exportSession'
      },
      { 
        id: 'flashcards', 
        label: 'Criar Flashcards', 
        icon: 'fa-clone',
        action: 'createFlashcards'
      },
      { 
        id: 'analytics', 
        label: 'Analytics', 
        icon: 'fa-chart-bar',
        action: 'viewAnalytics'
      }
    ])

    // Refs
    const chatContainer = ref(null)

    // Methods
    const scrollToBottom = () => {
      nextTick(() => {
        const el = chatContainer.value
        if (el) el.scrollTop = el.scrollHeight
      })
    }

    const autoResize = (event) => {
      const textarea = event.target
      textarea.style.height = 'auto'
      textarea.style.height = textarea.scrollHeight + 'px'
    }

    const formatTimestamp = (ts) => {
      if (!ts) return 'N/A'
      const date = new Date(ts)
      return date.toLocaleString()
    }

    const createNewSession = async () => {
      try {
        await store.dispatch('thanos/createNewSession')
        selectedSessionIndex.value = chatSessions.value.length - 1
        playSound('success')
      } catch (error) {
        console.error('Erro ao criar nova sessão:', error)
      }
    }

    const selectSession = (idx) => {
      selectedSessionIndex.value = idx
      scrollToBottom()
      playSound('click')
    }

    const updateSessionTitle = () => {
      if (!currentSession.value) return
      store.dispatch('thanos/updateSessionTitle', {
        index: selectedSessionIndex.value,
        title: currentSession.value.title
      })
    }

    const clearHistory = async () => {
      try {
        await store.dispatch('thanos/clearSessions')
        createNewSession()
        playSound('success')
      } catch (error) {
        console.error('Erro ao limpar histórico:', error)
      }
    }

    const saveQuickNotes = () => {
      localStorage.setItem('thanos_quickNotes', quickNotes.value)
      playSound('success')
    }

    const clearInput = () => {
      userInput.value = ''
      playSound('click')
    }

    const openInitModal = () => {
      // Reset completo dos estados antes de abrir modal
      isLoading.value = false
      thanosAnimationActive.value = false
      thanosAnimationPhase.value = ''
      infinityStones.value.forEach(stone => {
        stone.active = false
      })
      showInitModal.value = true
    }

    const insertPrompt = (text) => {
      userInput.value = text
      playSound('click')
    }

    const toggleFab = () => {
      fabExpanded.value = !fabExpanded.value
    }

    const executeAction = (action) => {
      switch(action.action) {
        case 'createNewSession':
          createNewSession()
          break
        case 'showInit':
          openInitModal()
          break
        case 'exportSession':
          exportCurrentSession()
          break
        case 'createFlashcards':
          createFlashcardsFromSession()
          break
        case 'viewAnalytics':
          viewSessionAnalytics()
          break
      }
      fabExpanded.value = false
    }

    const getActionStyle = (index) => {
      const positions = [
        { bottom: '80px', right: '0' },
        { bottom: '140px', right: '0' },
        { bottom: '200px', right: '0' },
        { bottom: '260px', right: '0' },
        { bottom: '320px', right: '0' }
      ]
      return positions[index] || { bottom: `${80 + (index * 60)}px`, right: '0' }
    }

    const getFileAccept = () => {
      const acceptMap = {
        'PDF': '.pdf,application/pdf',
        'Texto': '.txt,.text,.md,.markdown,text/plain,text/markdown',
        'CSV': '.csv,.tsv,text/csv,text/tab-separated-values'
      }
      return acceptMap[selectedDocumentType.value] || ''
    }

    const getModelsForProvider = (provider) => {
      return CONFIG_MODELOS[provider]?.modelos || []
    }

    const handleFileUpload = (event) => {
      if (event.target.files && event.target.files.length > 0) {
        documentFile.value = event.target.files[0]
      }
    }

    const t = (key) => {
      return TRANSLATIONS[language.value]?.[key] || TRANSLATIONS['pt'][key] || key
    }

    const initializeThanos = async () => {
      try {
        // Iniciar animação épica SEMPRE (para teste)
        startThanosInitAnimation()
        isLoading.value = true

        // Se não tem API key, apenas simula o processo
        if (!apiKey.value) {
          console.log('Modo demo: Simulando inicialização...')
          // Simular tempo de processamento
          await new Promise(resolve => setTimeout(resolve, 9000))
          
          // Completar animação com sucesso
          completeThanosInitAnimation()
          
          setTimeout(() => {
            showInitModal.value = false
          }, 1500)
          playSound('success')
          return
        }

        const config = {
          provider: selectedProvider.value,
          model: selectedModel.value,
          apiKey: apiKey.value,
          documentType: selectedDocumentType.value.toLowerCase(),
          useRAG: useRAG.value,
          showSummary: showSummary.value,
          language: language.value,
          ragSettings: ragSettings.value,
          modelSettings: modelSettings.value
        }

        let document = null

        if (selectedDocumentType.value === 'Site' && documentUrl.value) {
          document = await thanosService.loadFromUrl(documentUrl.value, 'site')
        } else if (selectedDocumentType.value === 'Youtube' && documentUrl.value) {
          document = await thanosService.loadFromUrl(documentUrl.value, 'youtube')
        } else if (documentFile.value) {
          document = await thanosService.uploadDocument(documentFile.value, selectedDocumentType.value.toLowerCase())
        } else {
          // Documento demo para teste
          document = "Documento de demonstração para teste da animação do Thanos AI. Este é um exemplo de como o sistema processa conteúdo."
        }

        if (document) {
          const context = { ...config, document, timestamp: new Date().toISOString() }
          
          if (showSummary.value && typeof document === 'string') {
            documentSummary.value = document.substring(0, 250) + '...'
          } else if (showSummary.value && document && document.summary) {
            documentSummary.value = document.summary
          }

          await store.dispatch('thanos/initializeThanos', context)
          localStorage.setItem(`thanos_api_key_${selectedProvider.value}`, apiKey.value)
          
          if (currentSession.value) {
            await store.dispatch('thanos/addAIMessage', {
              sessionIndex: selectedSessionIndex.value,
              text: t('inicializacao_sucesso')
            })
          }

          // Completar animação com sucesso
          completeThanosInitAnimation()
          
          setTimeout(() => {
            showInitModal.value = false
          }, 1500)
          playSound('success')
        }
      } catch (error) {
        console.error('Erro ao inicializar Thanos:', error)
        failThanosInitAnimation()
        playSound('error')
      } finally {
        setTimeout(() => {
          isLoading.value = false
        }, 2000)
      }
    }

    const sendMessage = async () => {
      if (!currentSession.value || !userInput.value.trim()) return

      const content = userInput.value.trim()
      userInput.value = ''
      isLoading.value = true
      playSound('click')

      try {
        await store.dispatch('thanos/addUserMessage', {
          sessionIndex: selectedSessionIndex.value,
          text: content
        })

        scrollToBottom()

        let aiReply
        if (isInitialized.value) {
          const context = store.getters['thanos/context']
          const messagePayload = {
            message: content,
            context: context,
            sessionIndex: selectedSessionIndex.value,
            useRAG: useRAG.value,
            language: language.value
          }
          aiReply = await store.dispatch('thanos/sendMessage', messagePayload)
        } else {
          aiReply = await thanosService.simulateResponse(content, language.value)
          await store.dispatch('thanos/addAIMessage', {
            sessionIndex: selectedSessionIndex.value,
            text: aiReply
          })
        }

        playSound('notification')
      } catch (error) {
        console.error('Erro ao enviar mensagem:', error)
        playSound('error')
      } finally {
        isLoading.value = false
        scrollToBottom()
      }
    }

    const exportCurrentSession = () => {
      if (!currentSession.value) return

      let content = `Título: ${currentSession.value.title}\n\n`
      content += `Data: ${new Date(currentSession.value.updatedAt).toLocaleString()}\n`
      content += `Provedor: ${selectedProvider.value}\n`
      content += `Modelo: ${selectedModel.value}\n\n`
      
      if (documentSummary.value) {
        content += `Documento Analisado:\n${documentSummary.value}\n\n`
      }
      
      content += '=== CONVERSA ===\n\n'
      currentSession.value.messages.forEach((m) => {
        const who = m.sender === 'user' ? 'Você' : 'Thanos'
        const timestamp = new Date(m.timestamp).toLocaleTimeString()
        content += `[${who}] (${timestamp}):\n${m.text}\n\n`
      })

      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `thanos_${currentSession.value.title || 'conversa'}_${new Date().toISOString().split('T')[0]}.txt`
      link.click()
      URL.revokeObjectURL(url)
      playSound('success')
    }

    const createFlashcardsFromSession = async () => {
      if (!currentSession.value || currentSession.value.messages.length === 0) {
        alert('Nenhuma conversa ativa para criar flashcards.')
        return
      }

      try {
        // Coletar perguntas e respostas da sessão
        const flashcardsData = []
        for (let i = 0; i < currentSession.value.messages.length - 1; i += 2) {
          const userMsg = currentSession.value.messages[i]
          const aiMsg = currentSession.value.messages[i + 1]
          
          if (userMsg && aiMsg && userMsg.sender === 'user' && aiMsg.sender === 'ai') {
            flashcardsData.push({
              pergunta: userMsg.text,
              resposta: aiMsg.text,
              categoria: 'Thanos AI',
              origem: `Sessão: ${currentSession.value.title}`,
              dificuldade: 'Média'
            })
          }
        }

        if (flashcardsData.length === 0) {
          alert('Não há pares pergunta-resposta suficientes para criar flashcards.')
          return
        }

        // Integrar com sistema de flashcards
        await store.dispatch('flashcards/importFromThanos', {
          cards: flashcardsData,
          sessionId: currentSession.value.id,
          sessionTitle: currentSession.value.title
        })

        playSound('success')
        alert(`${flashcardsData.length} flashcards criados com sucesso!`)
        
        // Redirecionar para página de flashcards
        router.push('/flashcards')
      } catch (error) {
        console.error('Erro ao criar flashcards:', error)
        playSound('error')
        alert('Erro ao criar flashcards. Tente novamente.')
      }
    }

    const viewSessionAnalytics = async () => {
      if (!currentSession.value) return

      try {
        const analytics = await thanosService.getSessionAnalytics(currentSession.value.id)
        
        // Criar modal com análises
        const analyticsContent = `
          ℹ️ Estatísticas da Sessão:
          
          💬 Mensagens: ${analytics.message_count.total}
          👤 Do usuário: ${analytics.message_count.user}
          🤖 Do assistente: ${analytics.message_count.assistant}
          
          🪙 Tokens utilizados: ${analytics.tokens_used}
          🌍 Idioma: ${analytics.language}
          🔍 RAG habilitado: ${analytics.rag_enabled ? 'Sim' : 'Não'}
          
          🕰️ Sessão criada: ${new Date(analytics.created_at).toLocaleString()}
        `
        
        alert(analyticsContent)
        playSound('notification')
      } catch (error) {
        console.error('Erro ao obter analytics:', error)
        playSound('error')
      }
    }

    // Funções de Animação Thanos
    const startThanosInitAnimation = () => {
      thanosAnimationActive.value = true
      thanosAnimationPhase.value = 'gathering'
      
      // Resetar as pedras
      infinityStones.value.forEach(stone => {
        stone.active = false
      })
      
      // Ativar as pedras sequencialmente
      infinityStones.value.forEach((stone, index) => {
        setTimeout(() => {
          stone.active = true
          playSound('notification')
        }, index * 800)
      })
      
      // Fase de assemblagem do gauntlet
      setTimeout(() => {
        thanosAnimationPhase.value = 'assembling'
      }, 5000)
      
      // Fase de poder
      setTimeout(() => {
        thanosAnimationPhase.value = 'power'
      }, 7000)
    }

    const completeThanosInitAnimation = () => {
      thanosAnimationPhase.value = 'complete'
      
      setTimeout(() => {
        thanosAnimationPhase.value = 'snap'
      }, 500)
      
      setTimeout(() => {
        thanosAnimationActive.value = false
        thanosAnimationPhase.value = ''
      }, 2500)
    }

    const failThanosInitAnimation = () => {
      thanosAnimationPhase.value = 'failed'
      
      setTimeout(() => {
        thanosAnimationActive.value = false
        thanosAnimationPhase.value = ''
        infinityStones.value.forEach(stone => {
          stone.active = false
        })
      }, 2000)
    }

    // Watchers
    watch(() => currentSession.value?.messages?.length, (newVal, oldVal) => {
      if (newVal > oldVal) {
        scrollToBottom()
      }
    })

    watch(selectedProvider, (newProvider) => {
      const savedKey = localStorage.getItem(`thanos_api_key_${newProvider}`)
      apiKey.value = savedKey || ''
    })

    watch(showInitModal, (newValue) => {
      if (newValue) {
        // Reset estados quando modal abre
        isLoading.value = false
        thanosAnimationActive.value = false
        thanosAnimationPhase.value = ''
        infinityStones.value.forEach(stone => {
          stone.active = false
        })
      }
    })

    // Lifecycle
    onMounted(() => {
      scrollToBottom()
      if (chatSessions.value.length === 0) {
        createNewSession()
      }
      
      const savedKey = localStorage.getItem(`thanos_api_key_${selectedProvider.value}`)
      if (savedKey) {
        apiKey.value = savedKey
      }
      
      // Reset loading state na inicialização
      isLoading.value = false
      thanosAnimationActive.value = false
    })

    return {
      // Reactive data
      userInput,
      isLoading,
      showInitModal,
      activeOption,
      activeInitTab,
      fabExpanded,
      selectedDocumentType,
      documentUrl,
      documentFile,
      selectedProvider,
      selectedModel,
      apiKey,
      apiKeyMasked,
      availableProviders,
      language,
      showSummary,
      useRAG,
      documentSummary,
      quickNotes,
      ragSettings,
      modelSettings,
      
      // Animação Thanos
      thanosAnimationActive,
      thanosAnimationPhase,
      infinityStones,
      
      // Computed
      isInitialized,
      chatSessions,
      selectedSessionIndex,
      currentSession,
      
      // Configuration
      chatOptions,
      initTabs,
      quickPrompts,
      quickActions,
      
      // Refs
      chatContainer,
      
      // Methods
      autoResize,
      formatTimestamp,
      createNewSession,
      selectSession,
      updateSessionTitle,
      clearHistory,
      saveQuickNotes,
      clearInput,
      openInitModal,
      insertPrompt,
      toggleFab,
      executeAction,
      getActionStyle,
      getFileAccept,
      getModelsForProvider,
      handleFileUpload,
      initializeThanos,
      sendMessage,
      exportCurrentSession,
      createFlashcardsFromSession,
      viewSessionAnalytics,
      startThanosInitAnimation,
      completeThanosInitAnimation,
      failThanosInitAnimation,
      t
    }
  }
}
</script>

<style scoped>
/* Container */
.thanos-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  position: relative;
  overflow-x: hidden;
}

/* Premium Hero Section */
.thanos-hero {
  position: relative;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  overflow: hidden;
}

/* Hero Background */
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.float-element {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(78, 205, 196, 0.1), rgba(102, 126, 234, 0.1));
  animation: float 20s infinite linear;
}

.element-1 {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
}

.element-2 {
  width: 60px;
  height: 60px;
  top: 20%;
  right: 15%;
  animation-delay: -5s;
}

.element-3 {
  width: 100px;
  height: 100px;
  bottom: 15%;
  left: 20%;
  animation-delay: -10s;
}

.element-4 {
  width: 40px;
  height: 40px;
  top: 60%;
  right: 25%;
  animation-delay: -15s;
}

.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(78, 205, 196, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(78, 205, 196, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 30s linear infinite;
}

@keyframes float {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% { 
    transform: translateY(-40px) rotate(180deg);
    opacity: 0.7;
  }
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* Hero Content */
.hero-content {
  position: relative;
  z-index: 10;
  text-align: center;
  max-width: 1000px;
  padding: 2.5rem 2rem;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(25px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.25);
}

.hero-wrapper {
  max-width: 700px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.hero-title-section {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.title-content {
  position: relative;
  z-index: 15;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin: 0;
  letter-spacing: -1px;
  line-height: 1.1;
}

.title-main {
  background: linear-gradient(45deg, #fff, #4ecdc4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-ai {
  background: linear-gradient(45deg, #4ecdc4, #667eea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 8px 30px rgba(78, 205, 196, 0.3);
}

.hero-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.75);
  font-weight: 400;
  line-height: 1.5;
  margin: 0;
  max-width: 400px;
}

/* Hero Search */
.hero-search {
  position: relative;
  width: 100%;
}

.search-wrapper {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
  display: flex;
  align-items: stretch;
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  padding: 6px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.search-wrapper:focus-within {
  border-color: rgba(78, 205, 196, 0.5);
  box-shadow: 0 0 30px rgba(78, 205, 196, 0.2);
}

.search-icon-container {
  padding: 1rem 1.2rem;
  color: rgba(255, 255, 255, 0.5);
  font-size: 1rem;
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  padding: 1rem 0.8rem;
  border: none;
  background: transparent;
  color: white;
  font-size: 1rem;
  outline: none;
  resize: none;
  min-height: 20px;
  max-height: 100px;
  font-family: inherit;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.search-clear {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 1rem 1.2rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.search-clear:hover {
  color: white;
  transform: scale(1.1);
}

/* Stats Showcase */
.stats-showcase {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.stat-card {
  background: rgba(255, 255, 255, 0.04);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 1.5rem 1.8rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 140px;
  flex: 0 0 auto;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(78, 205, 196, 0.1), transparent);
  transition: left 0.5s ease;
}

.stat-card:hover::before {
  left: 100%;
}

.stat-card:hover {
  transform: translateY(-6px);
  background: rgba(255, 255, 255, 0.06);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
  border-color: rgba(78, 205, 196, 0.15);
}

.stat-icon {
  font-size: 2rem;
  background: linear-gradient(45deg, #4ecdc4, #667eea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.8rem;
  position: relative;
  z-index: 2;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-number {
  display: block;
  font-size: 2.2rem;
  font-weight: 800;
  background: linear-gradient(45deg, #4ecdc4, #667eea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.3rem;
  line-height: 1;
}

.stat-label {
  color: rgba(255, 255, 255, 0.65);
  font-size: 0.85rem;
  font-weight: 500;
  line-height: 1.2;
}

/* Filter Pills */
.filter-section {
  max-width: 1200px;
  margin: -2rem auto 3rem;
  padding: 0 2rem;
  position: relative;
  z-index: 10;
}

.filter-container {
  display: flex;
  gap: 1.5rem;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  padding: 1rem 0;
  justify-content: center;
  flex-wrap: wrap;
}

.filter-pill {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.2rem 2.5rem;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  font-size: 1.1rem;
  min-height: 58px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.filter-pill::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(78, 205, 196, 0.1), transparent);
  transition: left 0.6s ease;
}

.filter-pill:hover::before {
  left: 100%;
}

.filter-pill:hover {
  background: rgba(255, 255, 255, 0.12);
  color: #4ecdc4;
  transform: translateY(-4px);
  border-color: rgba(78, 205, 196, 0.3);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.filter-pill.active {
  background: linear-gradient(135deg, #4ecdc4, #667eea);
  color: white;
  border-color: rgba(78, 205, 196, 0.5);
  box-shadow: 0 8px 32px rgba(78, 205, 196, 0.4);
  transform: translateY(-2px);
}

.pill-icon {
  font-size: 1.4rem;
  transition: all 0.3s ease;
}

/* Main Content */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 2rem;
  position: relative;
  z-index: 5;
}

/* Section Headers */
.section-header {
  margin-bottom: 3rem;
  position: relative;
  padding: 2rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 1.2rem;
  padding: 1.2rem 2.5rem;
  margin: 0 0 1.5rem 0;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 50px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
  font-size: 1.1rem;
  min-height: 58px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.section-title::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(78, 205, 196, 0.1), transparent);
  transition: left 0.6s ease;
}

.section-header:hover .section-title::before {
  left: 100%;
}

.section-header:hover .section-title {
  background: rgba(255, 255, 255, 0.12);
  color: #4ecdc4;
  transform: translateY(-4px);
  border-color: rgba(78, 205, 196, 0.3);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.title-content {
  display: flex;
  align-items: center;
  gap: 1.2rem;
  position: relative;
  z-index: 2;
}

.section-icon {
  font-size: 1.4rem;
  transition: all 0.3s ease;
}

.title-text {
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.title-text .highlight {
  font-weight: 600;
}

.section-description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.95rem;
  text-align: center;
  max-width: 400px;
  line-height: 1.5;
  position: relative;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

/* Chat Interface */
.chat-interface {
  background: rgba(255, 255, 255, 0.04);
  backdrop-filter: blur(25px);
  border-radius: 25px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  height: 70vh;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.chat-header {
  margin-bottom: 1.5rem;
}

.session-input-title {
  width: 100%;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 15px;
  color: white;
  font-size: 1.1rem;
  outline: none;
  transition: all 0.3s ease;
}

.session-input-title:focus {
  border-color: rgba(78, 205, 196, 0.5);
  box-shadow: 0 0 20px rgba(78, 205, 196, 0.2);
}

/* Document Summary */
.document-summary {
  background: rgba(78, 205, 196, 0.1);
  border-radius: 15px;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(78, 205, 196, 0.2);
  overflow: hidden;
}

.summary-header {
  background: rgba(78, 205, 196, 0.2);
  padding: 1rem 1.5rem;
  font-weight: 600;
  color: #4ecdc4;
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.summary-content {
  padding: 1.5rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

/* Chat History */
.chat-history {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 15px;
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  scroll-behavior: smooth;
}

.chat-history::-webkit-scrollbar {
  width: 6px;
}

.chat-history::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.chat-history::-webkit-scrollbar-thumb {
  background: rgba(78, 205, 196, 0.3);
  border-radius: 3px;
}

/* Welcome Message */
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  background: rgba(78, 205, 196, 0.1);
  border-radius: 20px;
  padding: 3rem 2rem;
  margin: 2rem 0;
  color: rgba(255, 255, 255, 0.9);
  border: 1px dashed rgba(78, 205, 196, 0.3);
}

.welcome-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  color: #4ecdc4;
}

.welcome-message h3 {
  margin: 0.5rem 0 1.5rem;
  color: white;
  font-size: 1.8rem;
  font-weight: 700;
}

.welcome-message ul {
  text-align: left;
  padding-left: 1.5rem;
}

.welcome-message li {
  margin-bottom: 0.5rem;
}

/* Messages */
.message {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  animation: message-appear 0.3s ease-out;
}

.message.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 1.2rem;
}

.message.user .message-avatar {
  background: linear-gradient(135deg, #4ecdc4, #667eea);
  color: white;
}

.message.ai .message-avatar {
  background: rgba(78, 205, 196, 0.2);
  color: #4ecdc4;
  border: 2px solid rgba(78, 205, 196, 0.3);
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-text {
  padding: 1rem 1.5rem;
  border-radius: 20px;
  line-height: 1.6;
  word-wrap: break-word;
  position: relative;
}

.message.user .message-text {
  background: linear-gradient(135deg, #4ecdc4, #667eea);
  color: white;
  border-bottom-right-radius: 5px;
}

.message.ai .message-text {
  background: rgba(255, 255, 255, 0.06);
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom-left-radius: 5px;
}

.message-meta {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 0.5rem;
  padding: 0 1.5rem;
}

@keyframes message-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-bottom: 1rem;
}

.typing-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(78, 205, 196, 0.2);
  color: #4ecdc4;
  border: 2px solid rgba(78, 205, 196, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.typing-content {
  background: rgba(255, 255, 255, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 1rem 1.5rem;
  border-bottom-left-radius: 5px;
}

.typing-dots {
  display: flex;
  gap: 0.3rem;
  margin-bottom: 0.3rem;
}

.typing-dot {
  width: 8px;
  height: 8px;
  background: #4ecdc4;
  border-radius: 50%;
  animation: typing-animation 1.5s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: 0s; }
.typing-dot:nth-child(2) { animation-delay: 0.2s; }
.typing-dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing-animation {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

.typing-text {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
}

/* Chat Input */
.chat-input-area {
  display: flex;
  gap: 1rem;
}

.input-wrapper {
  display: flex;
  gap: 1rem;
  width: 100%;
}

.init-trigger-button,
.send-button {
  padding: 1rem 2rem;
  border: none;
  border-radius: 15px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.init-trigger-button {
  background: linear-gradient(135deg, #4ecdc4, #667eea);
  color: white;
  flex: 1;
}

.send-button {
  background: linear-gradient(135deg, #4ecdc4, #667eea);
  color: white;
}

.init-trigger-button:hover,
.send-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Sidebar Content */
.sidebar-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.sidebar-card {
  background: rgba(255, 255, 255, 0.04);
  backdrop-filter: blur(25px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.card-header {
  background: rgba(78, 205, 196, 0.1);
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.card-header h3 {
  margin: 0;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
}

.card-header .fa-solid {
  color: #4ecdc4;
  font-size: 1.3rem;
}

.card-content {
  padding: 1.5rem;
}

/* Sessions List */
.sessions-list {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 1.5rem;
}

.session-item {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.06);
  border-radius: 10px;
  margin-bottom: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.session-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

.session-item.active {
  background: rgba(78, 205, 196, 0.2);
  border-color: rgba(78, 205, 196, 0.3);
}

.session-title {
  font-weight: 600;
  color: white;
  margin-bottom: 0.3rem;
}

.session-meta {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

/* Session Actions */
.session-actions {
  display: flex;
  gap: 0.8rem;
}

.action-button {
  padding: 0.8rem 1.2rem;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  flex: 1;
  justify-content: center;
  font-size: 0.9rem;
}

.action-button.primary {
  background: linear-gradient(135deg, #4ecdc4, #667eea);
  color: white;
}

.action-button.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-button:hover {
  transform: translateY(-2px);
}

.action-button.primary:hover {
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
}

.action-button.secondary:hover {
  background: rgba(255, 255, 255, 0.15);
}

/* Notes Textarea */
.notes-textarea {
  width: 100%;
  background: rgba(255, 255, 255, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 10px;
  padding: 1rem;
  color: white;
  font-size: 0.9rem;
  resize: vertical;
  outline: none;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.notes-textarea:focus {
  border-color: rgba(78, 205, 196, 0.5);
  box-shadow: 0 0 20px rgba(78, 205, 196, 0.2);
}

/* Quick Prompts */
.quick-prompts {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.prompt-item {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.06);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
  border: 1px solid transparent;
}

.prompt-item:hover {
  background: rgba(78, 205, 196, 0.1);
  border-color: rgba(78, 205, 196, 0.3);
  transform: translateX(5px);
}

.prompt-item .fa-solid {
  color: #4ecdc4;
  font-size: 1.1rem;
}

.prompt-item span {
  color: white;
  font-weight: 500;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.modal-content {
  background: linear-gradient(145deg, rgba(15, 15, 35, 0.95), rgba(26, 26, 46, 0.95));
  border-radius: 20px;
  border: 1px solid rgba(78, 205, 196, 0.2);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  backdrop-filter: blur(25px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.modal-header {
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
}

.modal-close {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 0.5rem;
  font-size: 1.2rem;
  transition: all 0.2s ease;
}

.modal-close:hover {
  color: white;
  transform: scale(1.1);
}

.modal-body {
  padding: 2rem;
}

.modal-footer {
  padding: 1rem 2rem 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* Init Tabs */
.init-tabs {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.tab-header {
  display: flex;
  gap: 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 1rem;
}

.tab-item {
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.06);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  flex: 1;
  justify-content: center;
}

.tab-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.tab-item.active {
  background: rgba(78, 205, 196, 0.2);
  color: #4ecdc4;
  border: 1px solid rgba(78, 205, 196, 0.3);
}

.tab-content {
  min-height: 200px;
}

.tab-pane {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Form Elements */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  font-size: 0.9rem;
}

.form-select,
.form-input {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 10px;
  color: white;
  font-size: 0.9rem;
  outline: none;
  transition: all 0.3s ease;
}

.form-select:focus,
.form-input:focus {
  border-color: rgba(78, 205, 196, 0.5);
  box-shadow: 0 0 20px rgba(78, 205, 196, 0.2);
}

.form-file {
  padding: 0.8rem;
  background: rgba(255, 255, 255, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 10px;
  color: white;
  cursor: pointer;
}

.file-preview {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.8rem;
  background: rgba(78, 205, 196, 0.1);
  border-radius: 8px;
  color: #4ecdc4;
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

/* Checkbox */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(78, 205, 196, 0.5);
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
  background: #4ecdc4;
  border-color: #4ecdc4;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* Enhanced Form Elements */
.form-range {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  outline: none;
  appearance: none;
  margin: 0.5rem 0;
}

.form-range::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  background: #4ecdc4;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(78, 205, 196, 0.3);
}

.form-range::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #4ecdc4;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(78, 205, 196, 0.3);
}

.range-value {
  display: inline-block;
  margin-left: 1rem;
  padding: 0.2rem 0.8rem;
  background: rgba(78, 205, 196, 0.2);
  border-radius: 15px;
  color: #4ecdc4;
  font-size: 0.85rem;
  font-weight: 600;
  min-width: 40px;
  text-align: center;
}

/* RAG Configuration */
.rag-config {
  background: rgba(78, 205, 196, 0.05);
  border: 1px solid rgba(78, 205, 196, 0.2);
  border-radius: 10px;
  padding: 1.5rem;
  margin: 1rem 0;
}

.model-config {
  background: rgba(102, 126, 234, 0.05);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 10px;
  padding: 1.5rem;
  margin: 1rem 0;
}

/* API Key Security */
.api-key-input {
  position: relative;
}

.api-key-preview {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: rgba(78, 205, 196, 0.1);
  border-radius: 6px;
  color: #4ecdc4;
  font-size: 0.85rem;
}

.security-note {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
}

.security-note .fa-solid {
  color: #4ecdc4;
}

/* Enhanced Analytics */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.stat-item {
  background: rgba(78, 205, 196, 0.1);
  border-radius: 10px;
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(78, 205, 196, 0.15);
  transform: translateY(-2px);
}

.stat-item .stat-value {
  display: block;
  font-size: 1.2rem;
  font-weight: 700;
  color: #4ecdc4;
  margin-bottom: 0.3rem;
}

.stat-item .stat-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Floating Action Button */
.floating-hub {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
}

.fab-trigger {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4ecdc4, #667eea);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  box-shadow: 0 4px 20px rgba(78, 205, 196, 0.4);
  color: white;
  font-size: 1.5rem;
}

.fab-trigger:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 30px rgba(78, 205, 196, 0.5);
}

.fab-trigger.active {
  transform: scale(0.9) rotate(180deg);
  background: linear-gradient(135deg, #E74C3C, #C0392B);
}

.fab-action {
  position: absolute;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(78, 205, 196, 0.3);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4ecdc4;
  font-size: 1.2rem;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  right: 4px;
  opacity: 0;
  transform: scale(0);
}

.fab-action:hover {
  transform: scale(1.1);
  background: #4ecdc4;
  color: white;
}

.action-label {
  position: absolute;
  right: 70px;
  padding: 0.5rem 1rem;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 8px;
  white-space: nowrap;
  font-size: 0.875rem;
  font-weight: 600;
  opacity: 0;
  transform: translateX(10px);
  transition: all 0.3s ease;
  pointer-events: none;
}

.fab-action:hover .action-label {
  opacity: 1;
  transform: translateX(0);
}

.expanded .fab-action {
  opacity: 1;
  transform: scale(1);
}

/* Transitions */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.modal-fade-enter-active, .modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from, .modal-fade-leave-to {
  opacity: 0;
}

.fab-transition-enter-from,
.fab-transition-leave-to {
  opacity: 0;
  transform: scale(0);
}

.fab-transition-enter-active,
.fab-transition-leave-active {
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .hero-title {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .hero-content {
    padding: 2rem 1.5rem;
    margin: 1rem;
    max-width: 90vw;
  }
  
  .hero-wrapper {
    gap: 1.5rem;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .search-wrapper {
    max-width: 400px;
  }
  
  .stats-showcase {
    gap: 1rem;
  }
  
  .stat-card {
    padding: 1.2rem 1.4rem;
    min-width: 120px;
  }
  
  .filter-container {
    justify-content: flex-start;
    overflow-x: auto;
    flex-wrap: nowrap;
  }
  
  .main-content {
    padding: 1rem;
  }
  
  .chat-interface {
    height: 60vh;
  }
  
  .sidebar-content {
    order: -1;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .stat-number {
    font-size: 1.8rem;
  }
  
  .filter-pill {
    padding: 0.8rem 1.5rem;
    font-size: 0.9rem;
    min-height: 44px;
  }
  
  .section-title {
    padding: 1rem 2rem;
    font-size: 1rem;
  }
  
  .modal-content {
    width: 95%;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1.5rem;
  }
}

/* Demo Notice */
.demo-notice {
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.1), rgba(102, 126, 234, 0.1));
  border: 1px solid rgba(78, 205, 196, 0.3);
  border-radius: 15px;
  margin: 0 2rem 1rem;
  padding: 1rem 1.5rem;
  animation: notice-glow 2s ease-in-out infinite alternate;
}

.notice-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.notice-content .fa-solid {
  color: #4ecdc4;
  font-size: 1.2rem;
}

.demo-actions {
  margin-top: 1rem;
  text-align: center;
}

.demo-button {
  background: linear-gradient(135deg, #4ecdc4, #667eea);
  border: none;
  border-radius: 12px;
  color: white;
  padding: 1rem 2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  font-size: 1rem;
  box-shadow: 0 4px 16px rgba(78, 205, 196, 0.3);
}

.demo-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.5);
}

.demo-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.debug-info {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.debug-info small {
  color: rgba(255, 255, 255, 0.6);
  font-family: monospace;
}

.debug-reset {
  background: rgba(255, 0, 64, 0.8);
  border: none;
  border-radius: 6px;
  color: white;
  padding: 0.3rem 0.8rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.debug-reset:hover {
  background: rgba(255, 0, 64, 1);
  transform: scale(1.05);
}

@keyframes notice-glow {
  0% {
    box-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
  }
  100% {
    box-shadow: 0 0 20px rgba(78, 205, 196, 0.5);
  }
}

/* Thanos Animation Styles */
.thanos-animation-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, 
    rgba(15, 15, 35, 0.98) 0%, 
    rgba(0, 0, 0, 0.95) 70%, 
    rgba(0, 0, 0, 1) 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: 20px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.infinity-gauntlet {
  position: relative;
  width: 300px;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 3rem;
}

.gauntlet-base {
  position: relative;
  width: 200px;
  height: 300px;
  background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
  border-radius: 20px 20px 40px 40px;
  box-shadow: 
    inset 0 5px 15px rgba(255, 255, 255, 0.1),
    inset 0 -5px 15px rgba(0, 0, 0, 0.5),
    0 10px 30px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  padding: 20px;
  gap: 15px;
  animation: gauntlet-assemble 2s ease-in-out;
}

.thanos-animation-overlay.assembling .gauntlet-base {
  animation: gauntlet-power 1s ease-in-out infinite alternate;
}

.thanos-animation-overlay.power .gauntlet-base {
  animation: gauntlet-overload 0.5s ease-in-out infinite;
  box-shadow: 
    inset 0 5px 15px rgba(255, 255, 255, 0.2),
    inset 0 -5px 15px rgba(0, 0, 0, 0.5),
    0 10px 50px rgba(78, 205, 196, 0.8);
}

.infinity-stone {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #333;
  border: 3px solid #555;
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  opacity: 0.3;
  transform: scale(0.7);
}

.infinity-stone.active {
  opacity: 1;
  transform: scale(1.2);
  border-color: var(--stone-color);
  box-shadow: 
    0 0 20px var(--stone-color),
    0 0 40px var(--stone-color),
    inset 0 0 15px var(--stone-color);
  animation: stone-pulse 2s ease-in-out infinite;
}

.stone-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  background: radial-gradient(circle, var(--stone-color) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.8s ease;
  animation: glow-rotate 4s linear infinite;
}

.infinity-stone.active .stone-glow {
  opacity: 0.8;
}

.stone-core {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60%;
  height: 60%;
  background: var(--stone-color);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.8s ease;
}

.infinity-stone.active .stone-core {
  opacity: 1;
  animation: core-pulse 1.5s ease-in-out infinite alternate;
}

/* Stone specific positions */
.infinity-stone.mind { order: 1; }
.infinity-stone.soul { order: 2; }
.infinity-stone.time { order: 3; }
.infinity-stone.space { order: 4; }
.infinity-stone.reality { order: 5; }
.infinity-stone.power { order: 6; }

/* Power Waves */
.power-waves {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.wave {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100px;
  height: 100px;
  border: 2px solid rgba(78, 205, 196, 0.6);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: wave-expand 2s ease-out infinite;
}

.wave-1 { animation-delay: 0s; }
.wave-2 { 
  animation-delay: 0.7s; 
  border-color: rgba(255, 215, 0, 0.6);
}
.wave-3 { 
  animation-delay: 1.4s; 
  border-color: rgba(255, 0, 64, 0.6);
}

/* Snap Effect */
.snap-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.snap-particles {
  position: relative;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: linear-gradient(45deg, #FFD700, #FF6B35, #00FF41, #0099FF, #FF0040, #9932CC);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  animation-duration: 1.5s;
  animation-timing-function: ease-out;
  animation-fill-mode: forwards;
  animation-delay: var(--delay);
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
}

.reality-crack {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    linear-gradient(45deg, transparent 49%, rgba(255, 255, 255, 0.8) 50%, transparent 51%),
    linear-gradient(-45deg, transparent 49%, rgba(255, 255, 255, 0.8) 50%, transparent 51%);
  opacity: 0;
  animation: reality-crack 0.5s ease-in-out 0.5s forwards;
}

/* Animation Text */
.animation-text {
  text-align: center;
  position: relative;
  z-index: 10;
}

.animation-text h2 {
  font-size: 2rem;
  margin: 0;
  color: white;
  font-weight: 700;
}

.text-glow {
  background: linear-gradient(45deg, #4ecdc4, #667eea, #FFD700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(78, 205, 196, 0.5);
  animation: text-shimmer 2s ease-in-out infinite alternate;
}

.text-glow.success {
  background: linear-gradient(45deg, #00FF41, #32CD32);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(0, 255, 65, 0.8);
}

.text-glow.error {
  background: linear-gradient(45deg, #FF0040, #FF6B35);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(255, 0, 64, 0.8);
}

.text-glow.snap {
  font-size: 3rem !important;
  background: linear-gradient(45deg, #FFD700, #FFFFFF, #FFD700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 50px rgba(255, 215, 0, 1);
  animation: snap-text 1s ease-in-out;
}

/* Keyframe Animations */
@keyframes gauntlet-assemble {
  0% {
    opacity: 0;
    transform: scale(0.5) rotateY(180deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotateY(0deg);
  }
}

@keyframes gauntlet-power {
  0% {
    box-shadow: 
      inset 0 5px 15px rgba(255, 255, 255, 0.1),
      inset 0 -5px 15px rgba(0, 0, 0, 0.5),
      0 10px 30px rgba(0, 0, 0, 0.5);
  }
  100% {
    box-shadow: 
      inset 0 5px 15px rgba(255, 255, 255, 0.2),
      inset 0 -5px 15px rgba(0, 0, 0, 0.5),
      0 10px 40px rgba(78, 205, 196, 0.6);
  }
}

@keyframes gauntlet-overload {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes stone-pulse {
  0%, 100% {
    box-shadow: 
      0 0 20px var(--stone-color),
      0 0 40px var(--stone-color),
      inset 0 0 15px var(--stone-color);
  }
  50% {
    box-shadow: 
      0 0 30px var(--stone-color),
      0 0 60px var(--stone-color),
      inset 0 0 25px var(--stone-color);
  }
}

@keyframes glow-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes core-pulse {
  0% { transform: translate(-50%, -50%) scale(1); }
  100% { transform: translate(-50%, -50%) scale(1.2); }
}

@keyframes wave-expand {
  0% {
    width: 100px;
    height: 100px;
    opacity: 1;
  }
  100% {
    width: 400px;
    height: 400px;
    opacity: 0;
  }
}

@keyframes particle-explode {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) translate(200px, -200px) scale(0);
    opacity: 0;
  }
}

/* Animações específicas para cada partícula */
.particle:nth-child(1) { animation-name: particle-1; }
.particle:nth-child(2) { animation-name: particle-2; }
.particle:nth-child(3) { animation-name: particle-3; }
.particle:nth-child(4) { animation-name: particle-4; }
.particle:nth-child(5) { animation-name: particle-5; }
.particle:nth-child(6) { animation-name: particle-6; }
.particle:nth-child(7) { animation-name: particle-7; }
.particle:nth-child(8) { animation-name: particle-8; }
.particle:nth-child(9) { animation-name: particle-9; }
.particle:nth-child(10) { animation-name: particle-10; }
.particle:nth-child(11) { animation-name: particle-11; }
.particle:nth-child(12) { animation-name: particle-12; }
.particle:nth-child(13) { animation-name: particle-13; }
.particle:nth-child(14) { animation-name: particle-14; }
.particle:nth-child(15) { animation-name: particle-15; }
.particle:nth-child(16) { animation-name: particle-16; }
.particle:nth-child(17) { animation-name: particle-17; }
.particle:nth-child(18) { animation-name: particle-18; }
.particle:nth-child(19) { animation-name: particle-19; }
.particle:nth-child(20) { animation-name: particle-20; }

@keyframes particle-1 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(150px, -180px) scale(0); opacity: 0; } }
@keyframes particle-2 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(-120px, -150px) scale(0); opacity: 0; } }
@keyframes particle-3 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(180px, 100px) scale(0); opacity: 0; } }
@keyframes particle-4 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(-200px, 120px) scale(0); opacity: 0; } }
@keyframes particle-5 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(90px, -220px) scale(0); opacity: 0; } }
@keyframes particle-6 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(-160px, -80px) scale(0); opacity: 0; } }
@keyframes particle-7 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(220px, -50px) scale(0); opacity: 0; } }
@keyframes particle-8 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(-80px, 200px) scale(0); opacity: 0; } }
@keyframes particle-9 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(130px, 180px) scale(0); opacity: 0; } }
@keyframes particle-10 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(-250px, -100px) scale(0); opacity: 0; } }
@keyframes particle-11 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(70px, -160px) scale(0); opacity: 0; } }
@keyframes particle-12 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(-110px, 250px) scale(0); opacity: 0; } }
@keyframes particle-13 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(190px, -120px) scale(0); opacity: 0; } }
@keyframes particle-14 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(-170px, 80px) scale(0); opacity: 0; } }
@keyframes particle-15 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(110px, 210px) scale(0); opacity: 0; } }
@keyframes particle-16 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(-90px, -200px) scale(0); opacity: 0; } }
@keyframes particle-17 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(240px, 60px) scale(0); opacity: 0; } }
@keyframes particle-18 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(-140px, -130px) scale(0); opacity: 0; } }
@keyframes particle-19 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(80px, 170px) scale(0); opacity: 0; } }
@keyframes particle-20 { 0% { transform: translate(-50%, -50%) scale(1); opacity: 1; } 100% { transform: translate(-50%, -50%) translate(-210px, -70px) scale(0); opacity: 0; } }

@keyframes reality-crack {
  0% { opacity: 0; }
  50% { opacity: 1; }
  100% { opacity: 0; }
}

@keyframes text-shimmer {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

@keyframes snap-text {
  0% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.2);
    filter: brightness(2);
  }
  100% {
    transform: scale(1);
    filter: brightness(1);
  }
}

/* Particle variations for more randomness */
.particle:nth-child(odd) {
  animation-duration: 1.2s;
}

.particle:nth-child(even) {
  animation-duration: 1.8s;
}

.particle:nth-child(3n) {
  width: 6px;
  height: 6px;
}

.particle:nth-child(5n) {
  background: linear-gradient(45deg, #FFD700, #FF0040);
}

.particle:nth-child(7n) {
  background: linear-gradient(45deg, #00FF41, #0099FF);
}

/* Failed animation styles */
.thanos-animation-overlay.failed .gauntlet-base {
  animation: gauntlet-failure 1s ease-in-out;
}

.thanos-animation-overlay.failed .infinity-stone.active {
  animation: stone-failure 1s ease-in-out;
}

@keyframes gauntlet-failure {
  0%, 100% { transform: scale(1); }
  25% { transform: scale(1.1) rotate(5deg); }
  75% { transform: scale(0.9) rotate(-5deg); }
}

@keyframes stone-failure {
  0% { opacity: 1; }
  50% { opacity: 0.3; }
  100% { opacity: 0.1; }
}
</style>