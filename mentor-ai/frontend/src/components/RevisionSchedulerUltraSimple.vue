<template>
  <div class="revision-scheduler-ultra">
    <!-- Neural Background Animation -->
    <div class="neural-background">
      <canvas ref="neuralCanvas"></canvas>
      <div class="quantum-particles">
        <div v-for="i in 30" :key="`particle-${i}`" :class="`quantum-particle particle-${i % 5}`"></div>
      </div>
    </div>

    <!-- Main Container -->
    <div class="content-wrapper">
      <!-- Ultra Header -->
      <header class="ultra-header">
        <div class="header-glass">
          <div class="header-content">
            <div class="logo-section">
              <div class="neural-logo">
                <font-awesome-icon icon="brain" class="brain-3d" />
                <div class="neural-pulse"></div>
              </div>
              <div class="title-group">
                <h1 class="glitch-text" data-text="Sistema de Revisões Espaçadas">
                  Sistema de Revisões Espaçadas
                </h1>
                <p class="subtitle-animated">
                  <span class="text-gradient">Metodologia Científica para Máxima Retenção</span>
                </p>
              </div>
            </div>
            
            <!-- Live Stats -->
            <div class="stats-summary">
              <div class="stat-card glass-morph">
                <div class="stat-value">{{ totalRevisoes }}</div>
                <div class="stat-label">Revisões Agendadas</div>
              </div>
              <div class="stat-card glass-morph">
                <div class="stat-value">{{ taxaRetencao }}%</div>
                <div class="stat-label">Taxa de Retenção</div>
              </div>
              <div class="stat-card glass-morph">
                <div class="stat-value">{{ diasEstudados }}</div>
                <div class="stat-label">Dias de Estudo</div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Main Content Grid -->
      <div class="content-grid">
        <!-- Estudo Teórico Section -->
        <section class="study-section teorico-section glass-panel">
          <div class="section-header">
            <h2>
              <font-awesome-icon icon="book" />
              Registro de Estudo Teórico
            </h2>
            <p>Registre seu primeiro contato com o conteúdo</p>
          </div>

          <form @submit.prevent="registrarEstudoTeorico" class="neural-form">
            <div class="form-field floating">
              <input 
                v-model="estudoTeorico.materia" 
                type="text" 
                required
                class="neural-input"
                id="materia"
              />
              <label for="materia" class="floating-label">
                <font-awesome-icon icon="bookmark" />
                Matéria / Tópico
              </label>
              <div class="field-glow"></div>
            </div>

            <div class="form-field floating">
              <input 
                v-model="estudoTeorico.data" 
                type="date" 
                required
                class="neural-input"
                id="data"
              />
              <label for="data" class="floating-label">
                <font-awesome-icon icon="calendar" />
                Data do Estudo Teórico
              </label>
            </div>

            <div class="form-field">
              <label class="field-label">
                <font-awesome-icon icon="signal" />
                Grau de Dificuldade
              </label>
              <div class="difficulty-selector">
                <button 
                  type="button"
                  v-for="level in ['Fácil', 'Difícil']"
                  :key="level"
                  :class="['diff-option', { active: estudoTeorico.dificuldade === level }]"
                  @click="estudoTeorico.dificuldade = level"
                >
                  <font-awesome-icon :icon="level === 'Fácil' ? 'smile' : 'frown'" />
                  <span>{{ level }}</span>
                  <small>{{ level === 'Fácil' ? 'Primeiro contato em 2 dias' : 'Primeiro contato em 1 dia' }}</small>
                </button>
              </div>
            </div>

            <div v-if="estudoTeorico.dificuldade" class="prediction-display glass-inner">
              <div class="preview-card">
                <font-awesome-icon icon="calendar-check" class="preview-icon" />
                <div>
                  <strong>Primeiro Contato Agendado:</strong>
                  <p>{{ calcularPrimeiroContato() }}</p>
                </div>
              </div>
            </div>

            <button type="submit" class="submit-btn-ultra">
              <span class="btn-text">
                <font-awesome-icon icon="plus-circle" />
                Registrar Estudo Teórico
              </span>
              <div class="btn-glow"></div>
            </button>
          </form>
        </section>

        <!-- Revisão Prática Section -->
        <section class="study-section pratica-section glass-panel">
          <div class="section-header">
            <h2>
              <font-awesome-icon icon="tasks" />
              Registro de Revisão Prática
            </h2>
            <p>Registre seu desempenho em questões</p>
          </div>

          <form @submit.prevent="registrarRevisaoPratica" class="neural-form">
            <div class="form-field">
              <label class="field-label">
                <font-awesome-icon icon="bookmark" />
                Matéria / Tópico
              </label>
              <select v-model="revisaoPratica.materiaId" required class="neural-select">
                <option value="">Selecione a matéria</option>
                <option 
                  v-for="estudo in estudosTeoricosPendentes" 
                  :key="estudo.id" 
                  :value="estudo.id"
                >
                  {{ estudo.materia }} - {{ formatDate(estudo.primeiroContato) }}
                </option>
              </select>
            </div>

            <div class="form-row">
              <div class="form-field">
                <label class="field-label">
                  <font-awesome-icon icon="clipboard-list" />
                  Total de Questões
                </label>
                <div class="number-input-fancy">
                  <button 
                    @click="revisaoPratica.totalQuestoes = Math.max(20, revisaoPratica.totalQuestoes - 1)" 
                    type="button"
                    class="number-btn"
                  >
                    <font-awesome-icon icon="minus" />
                  </button>
                  <input 
                    v-model.number="revisaoPratica.totalQuestoes" 
                    type="number" 
                    min="20"
                    max="30"
                    class="number-display"
                  />
                  <button 
                    @click="revisaoPratica.totalQuestoes = Math.min(30, revisaoPratica.totalQuestoes + 1)" 
                    type="button"
                    class="number-btn"
                  >
                    <font-awesome-icon icon="plus" />
                  </button>
                </div>
              </div>

              <div class="form-field">
                <label class="field-label">
                  <font-awesome-icon icon="check-circle" />
                  Questões Corretas
                </label>
                <input 
                  v-model.number="revisaoPratica.acertos" 
                  type="number" 
                  min="0"
                  :max="revisaoPratica.totalQuestoes"
                  required
                  class="neural-input"
                />
              </div>
            </div>

            <div v-if="percentualCalculado > 0" class="performance-display">
              <div class="circular-progress">
                <svg viewBox="0 0 200 200">
                  <circle cx="100" cy="100" r="90" class="progress-bg"/>
                  <circle 
                    cx="100" cy="100" r="90" 
                    class="progress-fill"
                    :stroke-dasharray="`${percentualCalculado * 5.65} 565`"
                  />
                </svg>
                <div class="progress-center">
                  <span class="percentage-large">{{ percentualCalculado }}%</span>
                  <span class="percentage-label">Desempenho</span>
                </div>
              </div>
              
              <div class="next-revision-preview glass-inner">
                <font-awesome-icon icon="clock" />
                <div>
                  <strong>Próxima Revisão:</strong>
                  <p>{{ proximaRevisaoTexto }}</p>
                </div>
              </div>
            </div>

            <button 
              type="submit" 
              class="submit-btn-ultra"
              :disabled="!percentualCalculado"
            >
              <span class="btn-text">
                <font-awesome-icon icon="chart-line" />
                Registrar Desempenho
              </span>
              <div class="btn-glow"></div>
            </button>
          </form>
        </section>

        <!-- Tabelas de Revisão -->
        <section class="revision-grid-section glass-panel">
          <div class="section-header">
            <h2>
              <font-awesome-icon icon="table" />
              Grades de Revisão
            </h2>
            <div class="view-controls">
              <button 
                v-for="view in ['teorico', 'pratico', 'calendario']"
                :key="view"
                :class="['view-btn', { active: viewMode === view }]"
                @click="viewMode = view"
              >
                <font-awesome-icon :icon="viewIcons[view]" />
                {{ viewLabels[view] }}
              </button>
            </div>
          </div>

          <!-- Grade Teórico -->
          <div v-if="viewMode === 'teorico'" class="revision-table">
            <table>
              <thead>
                <tr>
                  <th>Matéria</th>
                  <th>Data Estudo</th>
                  <th>Dificuldade</th>
                  <th>Primeiro Contato</th>
                  <th>Status</th>
                  <th>Ações</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="estudo in estudosTeoricos" :key="estudo.id" class="table-row">
                  <td class="subject-cell">{{ estudo.materia }}</td>
                  <td>{{ formatDate(estudo.data) }}</td>
                  <td>
                    <span :class="['badge', 'badge-' + estudo.dificuldade.toLowerCase()]">
                      {{ estudo.dificuldade }}
                    </span>
                  </td>
                  <td class="date-cell">{{ formatDate(estudo.primeiroContato) }}</td>
                  <td>
                    <span :class="['status', getStatus(estudo.primeiroContato)]">
                      {{ getStatusText(estudo.primeiroContato) }}
                    </span>
                  </td>
                  <td>
                    <button @click="marcarComoConcluido(estudo.id)" class="action-btn">
                      <font-awesome-icon icon="check" />
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Grade Prático -->
          <div v-if="viewMode === 'pratico'" class="revision-table">
            <table>
              <thead>
                <tr>
                  <th>Matéria</th>
                  <th>Data Revisão</th>
                  <th>Desempenho</th>
                  <th>Intervalo</th>
                  <th>Próxima Revisão</th>
                  <th>Ações</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="revisao in revisoesPraticas" :key="revisao.id" class="table-row">
                  <td class="subject-cell">{{ revisao.materia }}</td>
                  <td>{{ formatDate(revisao.data) }}</td>
                  <td>
                    <div class="performance-cell">
                      <div class="mini-meter">
                        <div class="mini-fill" :style="{ width: revisao.percentual + '%' }"></div>
                      </div>
                      <span>{{ revisao.percentual }}%</span>
                    </div>
                  </td>
                  <td>{{ revisao.intervalo }} dias</td>
                  <td class="date-cell">{{ formatDate(revisao.proximaRevisao) }}</td>
                  <td>
                    <button @click="marcarRevisaoConcluida(revisao.id)" class="action-btn">
                      <font-awesome-icon icon="check" />
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Calendário -->
          <div v-if="viewMode === 'calendario'" class="calendar-view">
            <div class="calendar-header">
              <button @click="previousMonth" class="nav-btn">
                <font-awesome-icon icon="chevron-left" />
              </button>
              <h3>{{ currentMonthYear }}</h3>
              <button @click="nextMonth" class="nav-btn">
                <font-awesome-icon icon="chevron-right" />
              </button>
            </div>
            <div class="calendar-grid">
              <div v-for="day in weekDays" :key="day" class="weekday">{{ day }}</div>
              <div 
                v-for="(day, index) in calendarDays" 
                :key="index"
                :class="['calendar-day', { 
                  'other-month': !day.currentMonth,
                  'today': day.isToday,
                  'has-revision': day.revisions.length > 0
                }]"
              >
                <span class="day-number">{{ day.date }}</span>
                <div v-if="day.revisions.length > 0" class="day-revisions">
                  <div 
                    v-for="rev in day.revisions" 
                    :key="rev.id"
                    :class="['revision-dot', rev.type]"
                    :title="rev.materia"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

export default {
  name: 'RevisionSchedulerUltraSimple',
  
  setup() {
    const store = useStore()
    const router = useRouter()
    
    // Refs
    const neuralCanvas = ref(null)
    
    // Estado do formulário teórico
    const estudoTeorico = reactive({
      materia: '',
      data: '',
      dificuldade: ''
    })
    
    // Estado do formulário prático
    const revisaoPratica = reactive({
      materiaId: '',
      totalQuestoes: 30,
      acertos: 0
    })
    
    // Dados armazenados
    const estudosTeoricos = ref([])
    const revisoesPraticas = ref([])
    
    // Controles de visualização
    const viewMode = ref('teorico')
    const currentMonth = ref(new Date())
    
    // Estatísticas
    const totalRevisoes = ref(0)
    const taxaRetencao = ref(85)
    const diasEstudados = ref(0)
    
    // Computed
    const percentualCalculado = computed(() => {
      if (revisaoPratica.totalQuestoes > 0) {
        return Math.round((revisaoPratica.acertos / revisaoPratica.totalQuestoes) * 100)
      }
      return 0
    })
    
    const proximaRevisaoTexto = computed(() => {
      const dias = calcularIntervaloRevisao(percentualCalculado.value)
      return `Em ${dias} dias (${addDays(new Date(), dias).toLocaleDateString('pt-BR')})`
    })
    
    const estudosTeoricosPendentes = computed(() => {
      return estudosTeoricos.value.filter(e => !e.concluido)
    })
    
    const currentMonthYear = computed(() => {
      return currentMonth.value.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' })
    })
    
    const weekDays = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
    
    const viewIcons = {
      teorico: 'book',
      pratico: 'tasks',
      calendario: 'calendar'
    }
    
    const viewLabels = {
      teorico: 'Teórico',
      pratico: 'Prático',
      calendario: 'Calendário'
    }
    
    const calendarDays = computed(() => {
      const year = currentMonth.value.getFullYear()
      const month = currentMonth.value.getMonth()
      const firstDay = new Date(year, month, 1)
      const lastDay = new Date(year, month + 1, 0)
      const startCalendar = new Date(firstDay)
      startCalendar.setDate(startCalendar.getDate() - firstDay.getDay())
      
      const days = []
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      for (let i = 0; i < 42; i++) {
        const current = new Date(startCalendar)
        current.setDate(startCalendar.getDate() + i)
        
        const dayRevisions = getRevisionsForDate(current)
        
        days.push({
          date: current.getDate(),
          currentMonth: current.getMonth() === month,
          isToday: current.getTime() === today.getTime(),
          revisions: dayRevisions
        })
      }
      
      return days
    })
    
    // Methods
    const initNeuralBackground = () => {
      if (!neuralCanvas.value) return
      
      const canvas = neuralCanvas.value
      const ctx = canvas.getContext('2d')
      
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
      
      const nodes = []
      
      // Generate nodes
      for (let i = 0; i < 30; i++) {
        nodes.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          vx: (Math.random() - 0.5) * 0.3,
          vy: (Math.random() - 0.5) * 0.3,
          radius: Math.random() * 2 + 1
        })
      }
      
      const animate = () => {
        ctx.fillStyle = 'rgba(15, 23, 42, 0.05)'
        ctx.fillRect(0, 0, canvas.width, canvas.height)
        
        nodes.forEach((node, i) => {
          node.x += node.vx
          node.y += node.vy
          
          if (node.x < 0 || node.x > canvas.width) node.vx *= -1
          if (node.y < 0 || node.y > canvas.height) node.vy *= -1
          
          // Draw connections
          nodes.slice(i + 1).forEach(otherNode => {
            const distance = Math.sqrt(
              Math.pow(node.x - otherNode.x, 2) + 
              Math.pow(node.y - otherNode.y, 2)
            )
            
            if (distance < 100) {
              ctx.beginPath()
              ctx.moveTo(node.x, node.y)
              ctx.lineTo(otherNode.x, otherNode.y)
              ctx.strokeStyle = `rgba(99, 102, 241, ${0.2 * (1 - distance / 100)})`
              ctx.lineWidth = 0.5
              ctx.stroke()
            }
          })
          
          // Draw node
          ctx.beginPath()
          ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2)
          ctx.fillStyle = 'rgba(99, 102, 241, 0.6)'
          ctx.fill()
        })
        
        requestAnimationFrame(animate)
      }
      
      animate()
    }
    
    const calcularPrimeiroContato = () => {
      if (!estudoTeorico.data || !estudoTeorico.dificuldade) return ''
      
      const dataEstudo = new Date(estudoTeorico.data)
      const dias = estudoTeorico.dificuldade === 'Fácil' ? 2 : 1
      
      return addDays(dataEstudo, dias).toLocaleDateString('pt-BR')
    }
    
    const calcularIntervaloRevisao = (percentual) => {
      if (percentual <= 50) return 2
      if (percentual <= 55) return 7
      if (percentual <= 60) return 14
      if (percentual <= 65) return 18
      if (percentual <= 75) return 24
      if (percentual <= 80) return 30
      return 35
    }
    
    const registrarEstudoTeorico = async () => {
      const dataEstudo = new Date(estudoTeorico.data)
      const diasAte = estudoTeorico.dificuldade === 'Fácil' ? 2 : 1
      const primeiroContato = addDays(dataEstudo, diasAte)
      
      const novoEstudo = {
        id: Date.now(),
        ...estudoTeorico,
        primeiroContato: primeiroContato,
        concluido: false
      }
      
      estudosTeoricos.value.push(novoEstudo)
      salvarDados()
      atualizarEstatisticas()
      
      // Limpar formulário
      estudoTeorico.materia = ''
      estudoTeorico.data = ''
      estudoTeorico.dificuldade = ''
      
      // Toast de sucesso
      console.log('Estudo teórico registrado com sucesso!')
    }
    
    const registrarRevisaoPratica = async () => {
      const estudo = estudosTeoricos.value.find(e => e.id === parseInt(revisaoPratica.materiaId))
      if (!estudo) return
      
      const intervalo = calcularIntervaloRevisao(percentualCalculado.value)
      const proximaRevisao = addDays(new Date(), intervalo)
      
      const novaRevisao = {
        id: Date.now(),
        materiaId: revisaoPratica.materiaId,
        materia: estudo.materia,
        data: new Date(),
        totalQuestoes: revisaoPratica.totalQuestoes,
        acertos: revisaoPratica.acertos,
        percentual: percentualCalculado.value,
        intervalo: intervalo,
        proximaRevisao: proximaRevisao
      }
      
      revisoesPraticas.value.push(novaRevisao)
      salvarDados()
      atualizarEstatisticas()
      
      // Limpar formulário
      revisaoPratica.materiaId = ''
      revisaoPratica.totalQuestoes = 30
      revisaoPratica.acertos = 0
      
      console.log('Revisão prática registrada com sucesso!')
    }
    
    const marcarComoConcluido = (id) => {
      const estudo = estudosTeoricos.value.find(e => e.id === id)
      if (estudo) {
        estudo.concluido = true
        salvarDados()
      }
    }
    
    const marcarRevisaoConcluida = (id) => {
      const index = revisoesPraticas.value.findIndex(r => r.id === id)
      if (index !== -1) {
        revisoesPraticas.value.splice(index, 1)
        salvarDados()
        atualizarEstatisticas()
      }
    }
    
    const getRevisionsForDate = (date) => {
      const dateStr = date.toDateString()
      const revisions = []
      
      // Verificar estudos teóricos
      estudosTeoricos.value.forEach(estudo => {
        if (new Date(estudo.primeiroContato).toDateString() === dateStr) {
          revisions.push({
            id: estudo.id,
            materia: estudo.materia,
            type: 'teorico'
          })
        }
      })
      
      // Verificar revisões práticas
      revisoesPraticas.value.forEach(revisao => {
        if (new Date(revisao.proximaRevisao).toDateString() === dateStr) {
          revisions.push({
            id: revisao.id,
            materia: revisao.materia,
            type: 'pratico'
          })
        }
      })
      
      return revisions
    }
    
    const previousMonth = () => {
      const newMonth = new Date(currentMonth.value)
      newMonth.setMonth(newMonth.getMonth() - 1)
      currentMonth.value = newMonth
    }
    
    const nextMonth = () => {
      const newMonth = new Date(currentMonth.value)
      newMonth.setMonth(newMonth.getMonth() + 1)
      currentMonth.value = newMonth
    }
    
    const getStatus = (date) => {
      const today = new Date()
      const targetDate = new Date(date)
      
      if (targetDate < today) return 'overdue'
      if (targetDate.toDateString() === today.toDateString()) return 'today'
      return 'pending'
    }
    
    const getStatusText = (date) => {
      const status = getStatus(date)
      if (status === 'overdue') return 'Atrasado'
      if (status === 'today') return 'Hoje'
      return 'Pendente'
    }
    
    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('pt-BR')
    }
    
    const addDays = (date, days) => {
      const result = new Date(date)
      result.setDate(result.getDate() + days)
      return result
    }
    
    const salvarDados = () => {
      localStorage.setItem('revision-estudos-teoricos', JSON.stringify(estudosTeoricos.value))
      localStorage.setItem('revision-revisoes-praticas', JSON.stringify(revisoesPraticas.value))
    }
    
    const carregarDados = () => {
      const estudosSalvos = localStorage.getItem('revision-estudos-teoricos')
      const revisoesSalvas = localStorage.getItem('revision-revisoes-praticas')
      
      if (estudosSalvos) {
        estudosTeoricos.value = JSON.parse(estudosSalvos)
      }
      
      if (revisoesSalvas) {
        revisoesPraticas.value = JSON.parse(revisoesSalvas)
      }
      
      atualizarEstatisticas()
    }
    
    const atualizarEstatisticas = () => {
      totalRevisoes.value = estudosTeoricos.value.filter(e => !e.concluido).length + revisoesPraticas.value.length
      
      if (revisoesPraticas.value.length > 0) {
        const somaPercentuais = revisoesPraticas.value.reduce((acc, rev) => acc + rev.percentual, 0)
        taxaRetencao.value = Math.round(somaPercentuais / revisoesPraticas.value.length)
      }
      
      const datasEstudo = new Set()
      estudosTeoricos.value.forEach(e => datasEstudo.add(new Date(e.data).toDateString()))
      revisoesPraticas.value.forEach(r => datasEstudo.add(new Date(r.data).toDateString()))
      diasEstudados.value = datasEstudo.size
    }
    
    // Lifecycle
    onMounted(() => {
      initNeuralBackground()
      carregarDados()
    })
    
    onUnmounted(() => {
      // Cleanup if needed
    })
    
    return {
      // Refs
      neuralCanvas,
      
      // Estado
      estudoTeorico,
      revisaoPratica,
      estudosTeoricos,
      revisoesPraticas,
      viewMode,
      currentMonth,
      
      // Estatísticas
      totalRevisoes,
      taxaRetencao,
      diasEstudados,
      
      // Computed
      percentualCalculado,
      proximaRevisaoTexto,
      estudosTeoricosPendentes,
      currentMonthYear,
      weekDays,
      viewIcons,
      viewLabels,
      calendarDays,
      
      // Methods
      calcularPrimeiroContato,
      registrarEstudoTeorico,
      registrarRevisaoPratica,
      marcarComoConcluido,
      marcarRevisaoConcluida,
      previousMonth,
      nextMonth,
      getStatus,
      getStatusText,
      formatDate
    }
  }
}
</script>

<style lang="scss" scoped>
// Variables
$primary: #6366f1;
$secondary: #8b5cf6;
$success: #10b981;
$warning: #f59e0b;
$danger: #ef4444;
$dark: #1e293b;
$light: #f1f5f9;

// Mixins
@mixin glass($opacity: 0.1) {
  background: rgba(30, 41, 59, $opacity);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

@mixin glow($color: $primary) {
  box-shadow: 0 0 20px rgba($color, 0.5),
              0 0 40px rgba($color, 0.3),
              0 0 60px rgba($color, 0.1);
}

// Base Container
.revision-scheduler-ultra {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  position: relative;
  overflow-x: hidden;
}

// Neural Background
.neural-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  
  canvas {
    width: 100%;
    height: 100%;
  }
}

// Quantum Particles
.quantum-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  
  .quantum-particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: $primary;
    border-radius: 50%;
    opacity: 0;
    
    @for $i from 1 through 30 {
      &:nth-child(#{$i}) {
        top: random(100) * 1%;
        left: random(100) * 1%;
        animation: quantum-float #{10 + random(20)}s #{random(5)}s infinite;
      }
    }
  }
}

@keyframes quantum-float {
  0%, 100% {
    opacity: 0;
    transform: translateY(0) scale(0);
  }
  50% {
    opacity: 0.6;
    transform: translateY(-80px) scale(1);
  }
}

// Content Wrapper
.content-wrapper {
  position: relative;
  z-index: 1;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

// Ultra Header
.ultra-header {
  margin-bottom: 3rem;
  
  .header-glass {
    @include glass(0.8);
    border-radius: 1.5rem;
    padding: 2.5rem;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba($primary, 0.1) 0%, transparent 70%);
      animation: rotate 20s linear infinite;
    }
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.header-content {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

// Logo Section
.logo-section {
  display: flex;
  align-items: center;
  gap: 2rem;
  
  .neural-logo {
    position: relative;
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .brain-3d {
      font-size: 2.5rem;
      color: $primary;
      filter: drop-shadow(0 0 20px rgba($primary, 0.5));
      animation: brain-pulse 2s ease-in-out infinite;
    }
    
    .neural-pulse {
      position: absolute;
      width: 100%;
      height: 100%;
      border: 2px solid $primary;
      border-radius: 50%;
      animation: pulse-ring 2s ease-out infinite;
    }
  }
}

@keyframes brain-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.title-group {
  .glitch-text {
    font-size: 2.2rem;
    font-weight: 800;
    margin: 0;
    color: white;
    letter-spacing: -0.5px;
    position: relative;
    
    &::before,
    &::after {
      content: attr(data-text);
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    
    &::before {
      animation: glitch-1 0.5s infinite;
      color: $primary;
      z-index: -1;
    }
    
    &::after {
      animation: glitch-2 0.5s infinite;
      color: $secondary;
      z-index: -2;
    }
  }
  
  .subtitle-animated {
    margin: 0.5rem 0 0;
    font-size: 1rem;
    
    .text-gradient {
      background: linear-gradient(90deg, $primary, $secondary, $primary);
      background-size: 200% auto;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: gradient-shift 3s ease infinite;
    }
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

// Stats Summary
.stats-summary {
  display: flex;
  gap: 2rem;
  
  .stat-card {
    @include glass(0.5);
    padding: 1.5rem 2rem;
    border-radius: 1rem;
    text-align: center;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
      @include glow($primary);
    }
    
    .stat-value {
      font-size: 2rem;
      font-weight: 700;
      color: $primary;
      display: block;
    }
    
    .stat-label {
      font-size: 0.875rem;
      color: rgba(white, 0.7);
      margin-top: 0.25rem;
    }
  }
}

// Content Grid
.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
  
  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
}

// Glass Panel
.glass-panel {
  @include glass(0.8);
  border-radius: 1.5rem;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

// Section Header
.section-header {
  margin-bottom: 2rem;
  
  h2 {
    font-size: 1.5rem;
    color: white;
    margin: 0 0 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }
  
  p {
    color: rgba(white, 0.7);
    margin: 0;
  }
}

// Neural Form
.neural-form {
  .form-field {
    margin-bottom: 1.5rem;
    position: relative;
    
    &.floating {
      .neural-input {
        padding: 1.25rem 1rem 0.75rem;
        
        &:focus ~ .floating-label,
        &:valid ~ .floating-label {
          transform: translateY(-1.5rem) scale(0.85);
          color: $primary;
        }
      }
      
      .floating-label {
        position: absolute;
        top: 1rem;
        left: 1rem;
        color: rgba(white, 0.5);
        transition: all 0.3s ease;
        pointer-events: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
    }
  }
  
  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
}

// Neural Input
.neural-input,
.neural-select {
  width: 100%;
  padding: 1rem;
  background: rgba(white, 0.05);
  border: 1px solid rgba(white, 0.1);
  border-radius: 0.75rem;
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: $primary;
    background: rgba(white, 0.1);
    box-shadow: 0 0 0 3px rgba($primary, 0.2);
  }
  
  option {
    background: $dark;
  }
}

// Field Label
.field-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(white, 0.9);
  font-weight: 500;
  margin-bottom: 0.75rem;
}

// Difficulty Selector
.difficulty-selector {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  
  .diff-option {
    padding: 1.25rem;
    background: rgba(white, 0.05);
    border: 2px solid rgba(white, 0.1);
    border-radius: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    color: white;
    
    &.active {
      background: rgba($primary, 0.2);
      border-color: $primary;
    }
    
    &:hover:not(.active) {
      background: rgba(white, 0.1);
      transform: translateY(-2px);
    }
    
    svg {
      font-size: 2rem;
      margin-bottom: 0.5rem;
      display: block;
    }
    
    span {
      display: block;
      font-weight: 600;
      margin-bottom: 0.25rem;
    }
    
    small {
      display: block;
      font-size: 0.75rem;
      color: rgba(white, 0.6);
    }
  }
}

// Prediction Display
.prediction-display {
  margin-top: 1.5rem;
  padding: 1.25rem;
  border-radius: 1rem;
  
  .preview-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    
    .preview-icon {
      font-size: 2rem;
      color: $primary;
    }
    
    strong {
      display: block;
      color: white;
      margin-bottom: 0.25rem;
    }
    
    p {
      margin: 0;
      color: $primary;
      font-size: 1.1rem;
    }
  }
}

// Number Input Fancy
.number-input-fancy {
  display: flex;
  align-items: center;
  background: rgba(white, 0.05);
  border: 1px solid rgba(white, 0.1);
  border-radius: 0.75rem;
  overflow: hidden;
  
  .number-btn {
    width: 48px;
    height: 48px;
    background: none;
    border: none;
    color: rgba(white, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(white, 0.1);
      color: white;
    }
  }
  
  .number-display {
    flex: 1;
    text-align: center;
    background: none;
    border: none;
    color: white;
    font-size: 1.25rem;
    font-weight: 600;
    
    &::-webkit-inner-spin-button,
    &::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
  }
}

// Performance Display
.performance-display {
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: 2rem;
  align-items: center;
  margin: 2rem 0;
}

.circular-progress {
  position: relative;
  width: 200px;
  height: 200px;
  
  svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
    
    .progress-bg {
      fill: none;
      stroke: rgba(white, 0.1);
      stroke-width: 8;
    }
    
    .progress-fill {
      fill: none;
      stroke: $primary;
      stroke-width: 8;
      stroke-linecap: round;
      stroke-dasharray: 565;
      stroke-dashoffset: 565;
      transition: stroke-dasharray 0.6s ease;
    }
  }
  
  .progress-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    
    .percentage-large {
      display: block;
      font-size: 2.5rem;
      font-weight: 700;
      color: white;
    }
    
    .percentage-label {
      display: block;
      font-size: 0.875rem;
      color: rgba(white, 0.6);
      margin-top: 0.25rem;
    }
  }
}

.next-revision-preview {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 1rem;
  
  svg {
    font-size: 2rem;
    color: $primary;
  }
  
  strong {
    display: block;
    color: white;
    margin-bottom: 0.25rem;
  }
  
  p {
    margin: 0;
    color: $primary;
    font-size: 1.1rem;
  }
}

// Submit Button Ultra
.submit-btn-ultra {
  width: 100%;
  padding: 1.25rem 2rem;
  background: linear-gradient(135deg, $primary, $secondary);
  border: none;
  border-radius: 0.75rem;
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    @include glow($primary);
    
    .btn-glow {
      opacity: 1;
    }
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .btn-text {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
  }
  
  .btn-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(white, 0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
}

// Revision Grid Section
.revision-grid-section {
  grid-column: 1 / -1;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }
}

// View Controls
.view-controls {
  display: flex;
  gap: 0.5rem;
  
  .view-btn {
    padding: 0.5rem 1rem;
    background: rgba(white, 0.05);
    border: 1px solid rgba(white, 0.1);
    border-radius: 0.5rem;
    color: rgba(white, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    
    &.active {
      background: $primary;
      border-color: $primary;
      color: white;
    }
    
    &:hover:not(.active) {
      background: rgba(white, 0.1);
      color: white;
    }
  }
}

// Revision Table
.revision-table {
  overflow-x: auto;
  
  table {
    width: 100%;
    border-collapse: collapse;
    
    th, td {
      padding: 1rem;
      text-align: left;
      border-bottom: 1px solid rgba(white, 0.1);
    }
    
    th {
      color: rgba(white, 0.9);
      font-weight: 600;
      background: rgba(white, 0.05);
    }
    
    .table-row {
      transition: background 0.3s ease;
      
      &:hover {
        background: rgba(white, 0.05);
      }
      
      td {
        color: rgba(white, 0.8);
      }
    }
  }
}

// Badge
.badge {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  
  &.badge-fácil {
    background: rgba($success, 0.2);
    color: $success;
  }
  
  &.badge-difícil {
    background: rgba($danger, 0.2);
    color: $danger;
  }
}

// Status
.status {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  
  &.overdue {
    background: rgba($danger, 0.2);
    color: $danger;
  }
  
  &.today {
    background: rgba($warning, 0.2);
    color: $warning;
  }
  
  &.pending {
    background: rgba($primary, 0.2);
    color: $primary;
  }
}

// Performance Cell
.performance-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  
  .mini-meter {
    width: 60px;
    height: 6px;
    background: rgba(white, 0.1);
    border-radius: 3px;
    overflow: hidden;
    
    .mini-fill {
      height: 100%;
      background: $primary;
      transition: width 0.3s ease;
    }
  }
}

// Action Button
.action-btn {
  padding: 0.5rem 0.75rem;
  background: rgba(white, 0.1);
  border: 1px solid rgba(white, 0.2);
  border-radius: 0.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba($primary, 0.2);
    border-color: $primary;
  }
}

// Calendar View
.calendar-view {
  .calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    
    h3 {
      margin: 0;
      color: white;
      text-transform: capitalize;
    }
    
    .nav-btn {
      padding: 0.5rem 1rem;
      background: rgba(white, 0.1);
      border: none;
      border-radius: 0.5rem;
      color: white;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(white, 0.2);
      }
    }
  }
  
  .calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.5rem;
    
    .weekday {
      text-align: center;
      padding: 0.5rem;
      color: rgba(white, 0.6);
      font-size: 0.875rem;
      font-weight: 600;
    }
    
    .calendar-day {
      aspect-ratio: 1;
      padding: 0.5rem;
      background: rgba(white, 0.05);
      border: 1px solid rgba(white, 0.1);
      border-radius: 0.5rem;
      position: relative;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(white, 0.1);
        transform: translateY(-2px);
      }
      
      &.other-month {
        opacity: 0.3;
      }
      
      &.today {
        background: rgba($primary, 0.2);
        border-color: $primary;
      }
      
      &.has-revision {
        border-color: $warning;
      }
      
      .day-number {
        display: block;
        color: white;
        font-weight: 500;
      }
      
      .day-revisions {
        position: absolute;
        bottom: 0.25rem;
        left: 0.25rem;
        right: 0.25rem;
        display: flex;
        gap: 0.25rem;
        justify-content: center;
        
        .revision-dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          
          &.teorico {
            background: $primary;
          }
          
          &.pratico {
            background: $secondary;
          }
        }
      }
    }
  }
}

// Glass Inner
.glass-inner {
  @include glass(0.3);
  border-radius: 0.75rem;
}

// Responsive
@media (max-width: 768px) {
  .content-wrapper {
    padding: 1rem;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
    
    .logo-section {
      flex-direction: column;
    }
  }
  
  .stats-summary {
    flex-direction: column;
    width: 100%;
    
    .stat-card {
      width: 100%;
    }
  }
  
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .difficulty-selector {
    grid-template-columns: 1fr;
  }
  
  .performance-display {
    grid-template-columns: 1fr;
    justify-items: center;
  }
  
  .revision-table {
    font-size: 0.875rem;
    
    th, td {
      padding: 0.5rem;
    }
  }
}
</style>