<template>
  <div class="thanos-header" :class="{ 'glass-effect': useGlassEffect }">
    <div class="brand-container">
      <div class="logo-container" :class="{ 'animated': showAnimations }">
        <div class="logo-element-wrapper">
          <div class="logo-hexagon"></div>
          <div class="logo-circle">
            <i class="fas fa-brain"></i>
          </div>
          <div class="logo-rings" v-if="showAnimations">
            <div class="ring ring-1"></div>
            <div class="ring ring-2"></div>
            <div class="ring ring-3"></div>
          </div>
        </div>
      </div>
      <div class="brand-text">
        <h1>Thanos<span class="highlight">AI</span></h1>
        <div class="model-info" v-if="modelName">
          <div class="model-badge" :class="getModelClass">{{ modelName }}</div>
          <div class="model-status" v-if="isModelReady">
            <span class="status-dot"></span>
            <span class="status-text">Pronto</span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="header-center" v-if="documentTitle || documentType">
      <div class="document-info">
        <i :class="getDocumentIcon"></i>
        <span class="document-title">{{ documentTitle || 'Sem documento' }}</span>
        <span class="document-type" v-if="documentType">{{ documentType }}</span>
      </div>
    </div>
    
    <div class="header-controls">
      <div class="search-container" v-if="showSearch">
        <input 
          type="text" 
          v-model="searchQuery" 
          placeholder="Pesquisar no documento..." 
          class="search-input"
          @keyup.enter="handleSearch"
        />
        <button class="search-button" @click="handleSearch">
          <i class="fas fa-search"></i>
        </button>
      </div>
      
      <div class="view-mode-controls" v-if="showViewControls">
        <button 
          v-for="mode in viewModes" 
          :key="mode.id"
          @click="changeViewMode(mode.id)"
          :class="['view-mode-btn', { active: currentViewMode === mode.id }]"
          :title="mode.label"
        >
          <i :class="mode.icon"></i>
        </button>
      </div>
      
      <div class="user-menu">
        <div class="user-stat">
          <i class="fas fa-tachometer-alt"></i>
          <span>{{ useCredits }} créditos</span>
        </div>
        <button class="menu-button" @click="toggleUserMenu">
          <div class="user-avatar">
            <img v-if="userAvatar" :src="userAvatar" alt="Avatar" />
            <i v-else class="fas fa-user"></i>
          </div>
        </button>
        
        <div class="dropdown-menu" v-if="isUserMenuOpen">
          <div class="menu-header">
            <div class="user-info">
              <div class="user-name">{{ userName || 'Usuário' }}</div>
              <div class="user-email">{{ userEmail || '<EMAIL>' }}</div>
            </div>
          </div>
          <div class="menu-items">
            <div class="menu-item" @click="handleMenuItem('profile')">
              <i class="fas fa-user-circle"></i>
              <span>Perfil</span>
            </div>
            <div class="menu-item" @click="handleMenuItem('settings')">
              <i class="fas fa-cog"></i>
              <span>Configurações</span>
            </div>
            <div class="menu-item" @click="handleMenuItem('help')">
              <i class="fas fa-question-circle"></i>
              <span>Ajuda</span>
            </div>
            <div class="menu-divider"></div>
            <div class="menu-item logout" @click="handleMenuItem('logout')">
              <i class="fas fa-sign-out-alt"></i>
              <span>Sair</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'

export default {
  name: 'ThanosHeader',
  
  props: {
    modelName: {
      type: String,
      default: 'GPT-4o'
    },
    isModelReady: {
      type: Boolean,
      default: true
    },
    documentTitle: {
      type: String,
      default: ''
    },
    documentType: {
      type: String,
      default: ''
    },
    userName: {
      type: String,
      default: 'Dr. Medicina'
    },
    userEmail: {
      type: String,
      default: '<EMAIL>'
    },
    userAvatar: {
      type: String,
      default: ''
    },
    useCredits: {
      type: Number,
      default: 1250
    },
    useGlassEffect: {
      type: Boolean,
      default: true
    },
    showAnimations: {
      type: Boolean,
      default: true
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    showViewControls: {
      type: Boolean,
      default: true
    }
  },
  
  emits: ['search', 'view-mode-change', 'menu-action'],
  
  setup(props, { emit }) {
    const searchQuery = ref('')
    const isUserMenuOpen = ref(false)
    const currentViewMode = ref('default')
    
    // View modes
    const viewModes = [
      { id: 'default', label: 'Visão Padrão', icon: 'fas fa-columns' },
      { id: 'focus', label: 'Modo Foco', icon: 'fas fa-align-justify' },
      { id: 'split', label: 'Visão Dividida', icon: 'fas fa-th-large' },
      { id: '3d', label: 'Visão 3D', icon: 'fas fa-cube' }
    ]
    
    // Computed properties
    const getModelClass = computed(() => {
      if (props.modelName.includes('GPT-4')) return 'model-gpt4'
      if (props.modelName.includes('Claude')) return 'model-claude'
      if (props.modelName.includes('Gemini')) return 'model-gemini'
      return 'model-default'
    })
    
    const getDocumentIcon = computed(() => {
      const iconMap = {
        'pdf': 'fas fa-file-pdf',
        'txt': 'fas fa-file-alt',
        'csv': 'fas fa-file-csv',
        'site': 'fas fa-globe',
        'youtube': 'fab fa-youtube',
        '': 'fas fa-file'
      }
      
      // Try to determine icon from document type
      const lowerType = props.documentType.toLowerCase()
      return iconMap[lowerType] || iconMap['']
    })
    
    // Methods
    const handleSearch = () => {
      if (searchQuery.value.trim()) {
        emit('search', searchQuery.value.trim())
      }
    }
    
    const toggleUserMenu = () => {
      isUserMenuOpen.value = !isUserMenuOpen.value
    }
    
    const handleMenuItem = (action) => {
      emit('menu-action', action)
      isUserMenuOpen.value = false
    }
    
    const changeViewMode = (mode) => {
      currentViewMode.value = mode
      emit('view-mode-change', mode)
    }
    
    // Close user menu when clicking outside
    const handleClickOutside = (event) => {
      if (isUserMenuOpen.value) {
        const menu = document.querySelector('.user-menu')
        if (menu && !menu.contains(event.target)) {
          isUserMenuOpen.value = false
        }
      }
    }
    
    onMounted(() => {
      document.addEventListener('click', handleClickOutside)
    })
    
    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside)
    })
    
    return {
      searchQuery,
      isUserMenuOpen,
      currentViewMode,
      viewModes,
      getModelClass,
      getDocumentIcon,
      handleSearch,
      toggleUserMenu,
      handleMenuItem,
      changeViewMode
    }
  }
}
</script>

<style scoped>
.thanos-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
  padding: 0 1.5rem;
  background: linear-gradient(135deg, rgba(106, 27, 154, 0.95) 0%, rgba(74, 20, 140, 0.95) 100%);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 100;
}

.thanos-header.glass-effect {
  background: linear-gradient(135deg, rgba(106, 27, 154, 0.85) 0%, rgba(74, 20, 140, 0.85) 100%);
  backdrop-filter: blur(10px);
}

/* Brand and Logo */
.brand-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-container {
  position: relative;
  width: 40px;
  height: 40px;
}

.logo-element-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.logo-hexagon {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(187, 134, 252, 0.3);
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
}

.logo-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 80%;
  background: linear-gradient(135deg, #9c27b0 0%, #6a1b9a 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 15px rgba(156, 39, 176, 0.7);
}

.logo-circle i {
  color: #fff;
  font-size: 1.2rem;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

.logo-container.animated .logo-circle {
  animation: pulse 2s infinite ease-in-out;
}

.logo-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
}

.ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  border: 1px solid rgba(187, 134, 252, 0.5);
  opacity: 0;
  animation: ripple 3s infinite ease-out;
}

.ring-1 {
  animation-delay: 0s;
}

.ring-2 {
  animation-delay: 1s;
}

.ring-3 {
  animation-delay: 2s;
}

@keyframes ripple {
  0% {
    width: 80%;
    height: 80%;
    opacity: 0.7;
  }
  100% {
    width: 200%;
    height: 200%;
    opacity: 0;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.05);
    opacity: 0.8;
  }
}

.brand-text {
  display: flex;
  flex-direction: column;
}

.brand-text h1 {
  margin: 0;
  font-size: 1.6rem;
  font-weight: 800;
  color: #fff;
  letter-spacing: 0.5px;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.highlight {
  color: #bb86fc;
  position: relative;
}

.model-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.model-badge {
  font-size: 0.7rem;
  padding: 0.15rem 0.5rem;
  border-radius: 4px;
  font-weight: bold;
  letter-spacing: 0.5px;
  background: rgba(30, 10, 60, 0.5);
  color: #fff;
}

.model-badge.model-gpt4 {
  background: rgba(138, 43, 226, 0.6);
}

.model-badge.model-claude {
  background: rgba(0, 128, 128, 0.6);
}

.model-badge.model-gemini {
  background: rgba(65, 105, 225, 0.6);
}

.model-status {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-size: 0.7rem;
  color: #a5d6a7;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #4caf50;
  box-shadow: 0 0 5px rgba(76, 175, 80, 0.7);
  animation: blink 2s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Document Info */
.header-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.document-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  background: rgba(30, 10, 60, 0.3);
  border-radius: 6px;
  border: 1px solid rgba(156, 39, 176, 0.2);
}

.document-info i {
  color: #bb86fc;
  font-size: 1rem;
}

.document-title {
  color: #fff;
  font-weight: 600;
  font-size: 0.9rem;
  max-width: 250px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.document-type {
  background: rgba(156, 39, 176, 0.3);
  color: #e1bee7;
  font-size: 0.7rem;
  padding: 0.15rem 0.5rem;
  border-radius: 4px;
  font-weight: bold;
  text-transform: uppercase;
}

/* Header Controls */
.header-controls {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.search-container {
  position: relative;
  width: 250px;
}

.search-input {
  width: 100%;
  padding: 0.5rem 2.5rem 0.5rem 0.75rem;
  background: rgba(30, 10, 60, 0.3);
  border: 1px solid rgba(156, 39, 176, 0.3);
  border-radius: 6px;
  color: #e1bee7;
  font-size: 0.9rem;
}

.search-input:focus {
  outline: none;
  background: rgba(30, 10, 60, 0.5);
  border-color: rgba(156, 39, 176, 0.5);
  box-shadow: 0 0 0 2px rgba(156, 39, 176, 0.1);
}

.search-button {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 36px;
  background: none;
  border: none;
  color: #bb86fc;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-button:hover {
  color: #e1bee7;
}

.view-mode-controls {
  display: flex;
  gap: 0.3rem;
}

.view-mode-btn {
  background: rgba(30, 10, 60, 0.3);
  border: 1px solid rgba(156, 39, 176, 0.2);
  color: #e1bee7;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-mode-btn:hover {
  background: rgba(106, 27, 154, 0.4);
  border-color: rgba(156, 39, 176, 0.3);
}

.view-mode-btn.active {
  background: rgba(156, 39, 176, 0.4);
  color: #fff;
  border-color: rgba(156, 39, 176, 0.5);
  box-shadow: 0 0 0 2px rgba(156, 39, 176, 0.2);
}

.user-menu {
  position: relative;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #e1bee7;
  font-size: 0.85rem;
}

.user-stat i {
  color: #bb86fc;
}

.menu-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(156, 39, 176, 0.3);
  border: 2px solid rgba(187, 134, 252, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #e1bee7;
  overflow: hidden;
  transition: all 0.2s ease;
}

.user-avatar:hover {
  border-color: rgba(187, 134, 252, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 10px);
  right: 0;
  width: 240px;
  background: rgba(40, 20, 70, 0.95);
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(156, 39, 176, 0.3);
  backdrop-filter: blur(10px);
  z-index: 1000;
  overflow: hidden;
  animation: dropdown-appear 0.2s ease;
}

@keyframes dropdown-appear {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.menu-header {
  padding: 1rem;
  background: rgba(106, 27, 154, 0.3);
  border-bottom: 1px solid rgba(156, 39, 176, 0.2);
}

.user-info {
  text-align: left;
}

.user-name {
  font-weight: bold;
  color: #fff;
  margin-bottom: 0.25rem;
}

.user-email {
  font-size: 0.8rem;
  color: rgba(225, 190, 231, 0.8);
}

.menu-items {
  padding: 0.5rem 0;
}

.menu-item {
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #e1bee7;
  cursor: pointer;
  transition: all 0.2s ease;
}

.menu-item:hover {
  background: rgba(156, 39, 176, 0.2);
}

.menu-item i {
  width: 16px;
  color: #bb86fc;
}

.menu-divider {
  height: 1px;
  background: rgba(156, 39, 176, 0.2);
  margin: 0.5rem 0;
}

.menu-item.logout {
  color: #ef9a9a;
}

.menu-item.logout i {
  color: #ef9a9a;
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
  .header-center {
    display: none;
  }
  
  .search-container {
    width: 200px;
  }
}

@media (max-width: 768px) {
  .thanos-header {
    padding: 0 1rem;
  }
  
  .model-info,
  .user-stat,
  .view-mode-controls {
    display: none;
  }
  
  .brand-text h1 {
    font-size: 1.4rem;
  }
  
  .logo-container {
    width: 35px;
    height: 35px;
  }
  
  .search-container {
    width: 150px;
  }
}

@media (max-width: 480px) {
  .search-container {
    display: none;
  }
  
  .brand-text h1 {
    font-size: 1.2rem;
  }
}
</style>