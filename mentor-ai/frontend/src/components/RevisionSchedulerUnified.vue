<template>
  <div class="revision-scheduler-unified">
    <!-- Background Effects -->
    <div class="background-effects">
      <div class="gradient-bg"></div>
      <div class="floating-particles">
        <div v-for="i in 30" :key="`particle-${i}`" class="particle" :style="getParticleStyle(i)"></div>
      </div>
    </div>

    <!-- Main Container -->
    <div class="container">
      <!-- Header -->
      <header class="header">
        <div class="header-content">
          <div class="title-section">
            <h1>
              <font-awesome-icon icon="brain" class="brain-icon" />
              Sistema Inteligente de Revisões Espaçadas
            </h1>
            <p class="subtitle">Metodologia científica baseada na Curva de Esquecimento de Ebbinghaus</p>
          </div>
          <div class="stats-summary">
            <div class="stat-item">
              <span class="stat-value">{{ totalRevisoes }}</span>
              <span class="stat-label">Revisões Agendadas</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ taxaRetencao }}%</span>
              <span class="stat-label">Taxa de Retenção</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ diasEstudados }}</span>
              <span class="stat-label">Dias de Estudo</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ indiceConsistencia }}%</span>
              <span class="stat-label">Consistência</span>
            </div>
          </div>
        </div>
      </header>

      <!-- Quick Actions -->
      <div class="quick-actions">
        <button @click="activeView = 'registro'" :class="['action-btn', { active: activeView === 'registro' }]">
          <font-awesome-icon icon="plus-circle" />
          <span>Novo Registro</span>
        </button>
        <button @click="activeView = 'dashboard'" :class="['action-btn', { active: activeView === 'dashboard' }]">
          <font-awesome-icon icon="chart-line" />
          <span>Dashboard</span>
        </button>
        <button @click="activeView = 'calendario'" :class="['action-btn', { active: activeView === 'calendario' }]">
          <font-awesome-icon icon="calendar" />
          <span>Calendário</span>
        </button>
        <button @click="activeView = 'notificacoes'" :class="['action-btn', { active: activeView === 'notificacoes' }]">
          <font-awesome-icon icon="bell" />
          <span>Notificações</span>
          <span v-if="notificacoesPendentes > 0" class="badge">{{ notificacoesPendentes }}</span>
        </button>
        <button @click="showAIAssistant = true" class="action-btn ai-btn">
          <font-awesome-icon icon="robot" />
          <span>Assistente IA</span>
        </button>
      </div>

      <!-- Main Content -->
      <div class="main-content">
        <!-- Registro View -->
        <transition name="fade-slide">
          <div v-if="activeView === 'registro'" class="content-grid">
            <!-- Estudo Teórico Section -->
            <section class="study-section teorico-section">
              <div class="section-header">
                <h2>
                  <font-awesome-icon icon="book" />
                  Registro de Estudo Teórico
                </h2>
                <p>Registre seu primeiro contato com o conteúdo</p>
              </div>

              <form @submit.prevent="registrarEstudoTeorico" class="teorico-form">
                <div class="form-group">
                  <label>
                    <font-awesome-icon icon="stethoscope" />
                    Disciplina Médica
                  </label>
                  <select v-model="estudoTeorico.disciplina" required>
                    <option value="">Selecione a disciplina</option>
                    <option v-for="disc in disciplinasMedicas" :key="disc.id" :value="disc.id">
                      {{ disc.nome }}
                    </option>
                  </select>
                </div>

                <div class="form-group">
                  <label>
                    <font-awesome-icon icon="bookmark" />
                    Tópico Específico
                  </label>
                  <input 
                    v-model="estudoTeorico.topico" 
                    type="text" 
                    placeholder="Ex: Sistema Cardiovascular - Anatomia"
                    required
                  />
                </div>

                <div class="form-group">
                  <label>
                    <font-awesome-icon icon="calendar" />
                    Data do Estudo Teórico
                  </label>
                  <input 
                    v-model="estudoTeorico.data" 
                    type="date" 
                    :max="hoje"
                    required
                  />
                </div>

                <div class="form-group">
                  <label>
                    <font-awesome-icon icon="signal" />
                    Grau de Dificuldade
                  </label>
                  <div class="difficulty-selector">
                    <button 
                      type="button"
                      :class="['diff-option', { active: estudoTeorico.dificuldade === 'Fácil' }]"
                      @click="estudoTeorico.dificuldade = 'Fácil'"
                    >
                      <font-awesome-icon icon="smile" />
                      <span>Fácil</span>
                      <small>Primeiro contato em 2 dias</small>
                    </button>
                    <button 
                      type="button"
                      :class="['diff-option', { active: estudoTeorico.dificuldade === 'Difícil' }]"
                      @click="estudoTeorico.dificuldade = 'Difícil'"
                    >
                      <font-awesome-icon icon="frown" />
                      <span>Difícil</span>
                      <small>Primeiro contato em 1 dia</small>
                    </button>
                  </div>
                </div>

                <div class="form-preview" v-if="estudoTeorico.dificuldade">
                  <div class="preview-card">
                    <font-awesome-icon icon="calendar-check" class="preview-icon" />
                    <div>
                      <strong>Primeiro Contato com Questões:</strong>
                      <p>{{ calcularPrimeiroContato() }}</p>
                    </div>
                  </div>
                </div>

                <button type="submit" class="submit-btn">
                  <font-awesome-icon icon="plus-circle" />
                  Registrar Estudo Teórico
                </button>
              </form>
            </section>

            <!-- Revisão Prática Section -->
            <section class="study-section pratica-section">
              <div class="section-header">
                <h2>
                  <font-awesome-icon icon="tasks" />
                  Registro de Revisão Prática
                </h2>
                <p>Registre seu desempenho em blocos de questões (20-30)</p>
              </div>

              <form @submit.prevent="registrarRevisaoPratica" class="pratica-form">
                <div class="form-group">
                  <label>
                    <font-awesome-icon icon="bookmark" />
                    Matéria para Revisão
                  </label>
                  <select v-model="revisaoPratica.materiaId" required @change="onMateriaChange">
                    <option value="">Selecione a matéria</option>
                    <optgroup label="Primeiro Contato Pendente">
                      <option v-for="estudo in estudosTeoricosPendentes" :key="`pendente-${estudo.id}`" :value="estudo.id">
                        {{ getDisciplinaNome(estudo.disciplina) }} - {{ estudo.topico }} ({{ formatDate(estudo.primeiroContato) }})
                      </option>
                    </optgroup>
                    <optgroup label="Revisões Agendadas">
                      <option v-for="revisao in revisoesAgendadas" :key="`revisao-${revisao.id}`" :value="`rev-${revisao.id}`">
                        {{ revisao.materia }} - Revisão {{ revisao.numeroRevisao }} ({{ formatDate(revisao.proximaRevisao) }})
                      </option>
                    </optgroup>
                  </select>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <label>
                      <font-awesome-icon icon="clipboard-list" />
                      Total de Questões
                    </label>
                    <input 
                      v-model.number="revisaoPratica.totalQuestoes" 
                      type="number" 
                      min="20"
                      max="30"
                      placeholder="20-30"
                      required
                    />
                  </div>

                  <div class="form-group">
                    <label>
                      <font-awesome-icon icon="check-circle" />
                      Questões Corretas
                    </label>
                    <input 
                      v-model.number="revisaoPratica.acertos" 
                      type="number" 
                      min="0"
                      :max="revisaoPratica.totalQuestoes"
                      required
                    />
                  </div>
                </div>

                <div class="performance-preview" v-if="percentualCalculado !== null">
                  <div class="performance-meter">
                    <div 
                      class="meter-fill" 
                      :style="{ 
                        width: percentualCalculado + '%',
                        background: getPerformanceColor(percentualCalculado)
                      }"
                    ></div>
                    <span class="meter-label">{{ percentualCalculado }}%</span>
                  </div>
                  
                  <div class="next-revision-preview">
                    <font-awesome-icon icon="clock" />
                    <div>
                      <strong>Próxima Revisão:</strong>
                      <p>{{ proximaRevisaoTexto }}</p>
                      <small class="revision-explanation">{{ getExplicacaoIntervalo(percentualCalculado) }}</small>
                    </div>
                  </div>
                </div>

                <button type="submit" class="submit-btn" :disabled="!percentualCalculado">
                  <font-awesome-icon icon="chart-line" />
                  Registrar Desempenho
                </button>
              </form>
            </section>
          </div>
        </transition>

        <!-- Dashboard View -->
        <transition name="fade-slide">
          <div v-if="activeView === 'dashboard'" class="dashboard-view">
            <div class="dashboard-grid">
              <!-- Performance Overview -->
              <div class="dashboard-card overview-card">
                <h3>
                  <font-awesome-icon icon="chart-pie" />
                  Visão Geral de Desempenho
                </h3>
                <div class="overview-stats">
                  <div class="overview-stat">
                    <span class="stat-label">Média Geral</span>
                    <span class="stat-value" :style="{ color: getPerformanceColor(mediaDesempenho) }">
                      {{ mediaDesempenho }}%
                    </span>
                  </div>
                  <div class="overview-stat">
                    <span class="stat-label">Questões Resolvidas</span>
                    <span class="stat-value">{{ totalQuestoes }}</span>
                  </div>
                  <div class="overview-stat">
                    <span class="stat-label">Taxa de Acerto</span>
                    <span class="stat-value">{{ taxaAcerto }}%</span>
                  </div>
                </div>
              </div>

              <!-- Distribuição por Disciplina -->
              <div class="dashboard-card distribution-card">
                <h3>
                  <font-awesome-icon icon="book-medical" />
                  Distribuição por Disciplina
                </h3>
                <div class="discipline-list">
                  <div v-for="dist in distribuicaoDisciplinas" :key="dist.disciplina" class="discipline-item">
                    <div class="discipline-info">
                      <span class="discipline-name">{{ dist.nome }}</span>
                      <span class="discipline-count">{{ dist.revisoes }} revisões</span>
                    </div>
                    <div class="discipline-bar">
                      <div 
                        class="bar-fill" 
                        :style="{ 
                          width: dist.percentual + '%',
                          background: dist.cor
                        }"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Padrões de Estudo -->
              <div class="dashboard-card patterns-card">
                <h3>
                  <font-awesome-icon icon="brain" />
                  Padrões de Estudo
                </h3>
                <div class="patterns-grid">
                  <div class="pattern-item">
                    <font-awesome-icon icon="sun" class="pattern-icon morning" />
                    <span class="pattern-label">Manhã</span>
                    <span class="pattern-value">{{ padroesPorPeriodo.manha }}%</span>
                  </div>
                  <div class="pattern-item">
                    <font-awesome-icon icon="cloud-sun" class="pattern-icon afternoon" />
                    <span class="pattern-label">Tarde</span>
                    <span class="pattern-value">{{ padroesPorPeriodo.tarde }}%</span>
                  </div>
                  <div class="pattern-item">
                    <font-awesome-icon icon="moon" class="pattern-icon night" />
                    <span class="pattern-label">Noite</span>
                    <span class="pattern-value">{{ padroesPorPeriodo.noite }}%</span>
                  </div>
                </div>
              </div>

              <!-- Curva de Retenção -->
              <div class="dashboard-card retention-card">
                <h3>
                  <font-awesome-icon icon="chart-line" />
                  Curva de Retenção
                </h3>
                <div class="retention-chart">
                  <canvas ref="retentionChart"></canvas>
                </div>
              </div>
            </div>

            <!-- Grades de Revisão -->
            <div class="revision-tables">
              <div class="table-tabs">
                <button 
                  v-for="tab in ['teorico', 'pratico', 'concluidas']" 
                  :key="tab"
                  @click="activeTab = tab"
                  :class="['tab-btn', { active: activeTab === tab }]"
                >
                  {{ getTabLabel(tab) }}
                </button>
              </div>

              <!-- Grade Teórico -->
              <div v-if="activeTab === 'teorico'" class="revision-table">
                <table>
                  <thead>
                    <tr>
                      <th>Disciplina</th>
                      <th>Tópico</th>
                      <th>Data Estudo</th>
                      <th>Dificuldade</th>
                      <th>Primeiro Contato</th>
                      <th>Status</th>
                      <th>Ações</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="estudo in estudosTeoricosFiltrados" :key="estudo.id">
                      <td>
                        <span class="discipline-badge" :style="{ background: getDisciplinaCor(estudo.disciplina) }">
                          {{ getDisciplinaNome(estudo.disciplina) }}
                        </span>
                      </td>
                      <td class="subject-cell">{{ estudo.topico }}</td>
                      <td>{{ formatDate(estudo.data) }}</td>
                      <td>
                        <span :class="['badge', 'badge-' + estudo.dificuldade.toLowerCase()]">
                          {{ estudo.dificuldade }}
                        </span>
                      </td>
                      <td class="date-cell">{{ formatDate(estudo.primeiroContato) }}</td>
                      <td>
                        <span :class="['status', getStatus(estudo.primeiroContato)]">
                          {{ getStatusText(estudo.primeiroContato) }}
                        </span>
                      </td>
                      <td>
                        <div class="action-buttons">
                          <button @click="iniciarRevisao(estudo)" class="action-btn primary" title="Iniciar Revisão">
                            <font-awesome-icon icon="play" />
                          </button>
                          <button @click="adiarRevisao(estudo)" class="action-btn warning" title="Adiar">
                            <font-awesome-icon icon="clock" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- Grade Prático -->
              <div v-if="activeTab === 'pratico'" class="revision-table">
                <table>
                  <thead>
                    <tr>
                      <th>Disciplina</th>
                      <th>Tópico</th>
                      <th>Revisão #</th>
                      <th>Data</th>
                      <th>Desempenho</th>
                      <th>Intervalo</th>
                      <th>Próxima</th>
                      <th>Ações</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="revisao in revisoesPraticasFiltradas" :key="revisao.id">
                      <td>
                        <span class="discipline-badge" :style="{ background: getDisciplinaCor(revisao.disciplina) }">
                          {{ getDisciplinaNome(revisao.disciplina) }}
                        </span>
                      </td>
                      <td class="subject-cell">{{ revisao.topico }}</td>
                      <td>
                        <span class="revision-number">{{ revisao.numeroRevisao }}</span>
                      </td>
                      <td>{{ formatDate(revisao.data) }}</td>
                      <td>
                        <div class="performance-cell">
                          <div class="mini-meter">
                            <div 
                              class="mini-fill" 
                              :style="{ 
                                width: revisao.percentual + '%',
                                background: getPerformanceColor(revisao.percentual)
                              }"
                            ></div>
                          </div>
                          <span>{{ revisao.percentual }}%</span>
                        </div>
                      </td>
                      <td>{{ revisao.intervalo }} dias</td>
                      <td class="date-cell">
                        <span :class="['next-date', getStatus(revisao.proximaRevisao)]">
                          {{ formatDate(revisao.proximaRevisao) }}
                        </span>
                      </td>
                      <td>
                        <div class="action-buttons">
                          <button @click="iniciarProximaRevisao(revisao)" class="action-btn primary" title="Iniciar">
                            <font-awesome-icon icon="play" />
                          </button>
                          <button @click="adiarRevisaoPratica(revisao)" class="action-btn warning" title="Adiar">
                            <font-awesome-icon icon="clock" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- Grade Concluídas -->
              <div v-if="activeTab === 'concluidas'" class="revision-table">
                <table>
                  <thead>
                    <tr>
                      <th>Disciplina</th>
                      <th>Tópico</th>
                      <th>Total Revisões</th>
                      <th>Média Desempenho</th>
                      <th>Última Revisão</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="concluida in revisoesConcluidas" :key="concluida.id">
                      <td>
                        <span class="discipline-badge" :style="{ background: getDisciplinaCor(concluida.disciplina) }">
                          {{ getDisciplinaNome(concluida.disciplina) }}
                        </span>
                      </td>
                      <td class="subject-cell">{{ concluida.topico }}</td>
                      <td>{{ concluida.totalRevisoes }}</td>
                      <td>
                        <div class="performance-cell">
                          <span :style="{ color: getPerformanceColor(concluida.mediaDesempenho) }">
                            {{ concluida.mediaDesempenho }}%
                          </span>
                        </div>
                      </td>
                      <td>{{ formatDate(concluida.ultimaRevisao) }}</td>
                      <td>
                        <span class="status completed">
                          <font-awesome-icon icon="check-circle" /> Dominado
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </transition>

        <!-- Calendário View -->
        <transition name="fade-slide">
          <div v-if="activeView === 'calendario'" class="calendar-view">
            <div class="calendar-container">
              <div class="calendar-header">
                <button @click="previousMonth" class="nav-btn">
                  <font-awesome-icon icon="chevron-left" />
                </button>
                <h3>{{ currentMonthYear }}</h3>
                <button @click="nextMonth" class="nav-btn">
                  <font-awesome-icon icon="chevron-right" />
                </button>
              </div>
              
              <div class="calendar-legend">
                <div class="legend-item">
                  <span class="legend-dot teorico"></span>
                  <span>Primeiro Contato</span>
                </div>
                <div class="legend-item">
                  <span class="legend-dot pratico"></span>
                  <span>Revisão Prática</span>
                </div>
                <div class="legend-item">
                  <span class="legend-dot vencido"></span>
                  <span>Atrasado</span>
                </div>
              </div>

              <div class="calendar-grid">
                <div v-for="day in weekDays" :key="day" class="weekday">{{ day }}</div>
                <div 
                  v-for="(day, index) in calendarDays" 
                  :key="index"
                  :class="['calendar-day', { 
                    'other-month': !day.currentMonth,
                    'today': day.isToday,
                    'has-revision': day.revisions.length > 0
                  }]"
                  @click="showDayDetails(day)"
                >
                  <span class="day-number">{{ day.date }}</span>
                  <div v-if="day.revisions.length > 0" class="day-revisions">
                    <div 
                      v-for="(rev, idx) in day.revisions.slice(0, 3)" 
                      :key="idx"
                      :class="['revision-indicator', rev.type, { overdue: rev.overdue }]"
                      :title="rev.materia"
                    >
                      <font-awesome-icon :icon="rev.type === 'teorico' ? 'book' : 'tasks'" />
                    </div>
                    <span v-if="day.revisions.length > 3" class="more-indicator">
                      +{{ day.revisions.length - 3 }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </transition>

        <!-- Notificações View -->
        <transition name="fade-slide">
          <div v-if="activeView === 'notificacoes'" class="notifications-view">
            <div class="notifications-header">
              <h2>
                <font-awesome-icon icon="bell" />
                Central de Notificações
              </h2>
              <button @click="marcarTodasComoLidas" class="mark-all-btn" v-if="notificacoes.length > 0">
                Marcar todas como lidas
              </button>
            </div>

            <div class="notifications-list" v-if="notificacoes.length > 0">
              <div 
                v-for="notif in notificacoesOrdenadas" 
                :key="notif.id"
                :class="['notification-item', notif.tipo, { unread: !notif.lida }]"
              >
                <div class="notif-icon">
                  <font-awesome-icon :icon="getNotificationIcon(notif.tipo)" />
                </div>
                <div class="notif-content">
                  <h4>{{ notif.titulo }}</h4>
                  <p>{{ notif.mensagem }}</p>
                  <span class="notif-time">{{ formatRelativeTime(notif.data) }}</span>
                </div>
                <div class="notif-actions">
                  <button @click="executarAcaoNotificacao(notif)" class="notif-action-btn primary">
                    {{ notif.acaoPrincipal }}
                  </button>
                  <button @click="dispensarNotificacao(notif)" class="notif-action-btn secondary">
                    Dispensar
                  </button>
                </div>
              </div>
            </div>

            <div v-else class="empty-notifications">
              <font-awesome-icon icon="bell-slash" class="empty-icon" />
              <p>Nenhuma notificação pendente</p>
            </div>
          </div>
        </transition>
      </div>
    </div>

    <!-- AI Assistant Modal -->
    <transition name="modal">
      <div v-if="showAIAssistant" class="modal-overlay" @click.self="showAIAssistant = false">
        <div class="modal-content ai-assistant">
          <div class="modal-header">
            <h3>
              <font-awesome-icon icon="robot" />
              Assistente IA de Revisões
            </h3>
            <button @click="showAIAssistant = false" class="close-btn">
              <font-awesome-icon icon="times" />
            </button>
          </div>
          
          <div class="ai-chat">
            <div class="chat-messages" ref="chatContainer">
              <div v-for="msg in aiMessages" :key="msg.id" :class="['chat-message', msg.type]">
                <div class="message-content">{{ msg.content }}</div>
              </div>
            </div>
            
            <div class="chat-input">
              <input 
                v-model="aiInput" 
                @keyup.enter="sendAIMessage"
                placeholder="Pergunte sobre suas revisões, desempenho ou estratégias de estudo..."
              />
              <button @click="sendAIMessage" :disabled="!aiInput.trim()">
                <font-awesome-icon icon="paper-plane" />
              </button>
            </div>
            
            <div class="ai-suggestions">
              <p>Sugestões:</p>
              <button 
                v-for="sugestao in sugestoesAI" 
                :key="sugestao"
                @click="aiInput = sugestao; sendAIMessage()"
                class="suggestion-btn"
              >
                {{ sugestao }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </transition>

    <!-- Day Details Modal -->
    <transition name="modal">
      <div v-if="selectedDay" class="modal-overlay" @click.self="selectedDay = null">
        <div class="modal-content day-details">
          <div class="modal-header">
            <h3>Revisões para {{ formatDate(selectedDayDate) }}</h3>
            <button @click="selectedDay = null" class="close-btn">
              <font-awesome-icon icon="times" />
            </button>
          </div>
          
          <div class="day-revisions-list">
            <div v-for="rev in selectedDay.revisions" :key="rev.id" class="day-revision-item">
              <div class="revision-type-icon" :class="rev.type">
                <font-awesome-icon :icon="rev.type === 'teorico' ? 'book' : 'tasks'" />
              </div>
              <div class="revision-details">
                <h4>{{ rev.materia }}</h4>
                <p>{{ rev.topico }}</p>
                <span class="revision-meta">
                  {{ rev.type === 'teorico' ? 'Primeiro Contato' : `Revisão #${rev.numeroRevisao}` }}
                </span>
              </div>
              <button @click="iniciarRevisaoDoDia(rev)" class="start-revision-btn">
                Iniciar
              </button>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { Chart, registerables } from 'chart.js';

Chart.register(...registerables);

export default {
  name: 'RevisionSchedulerUnified',
  
  data() {
    return {
      // Views e navegação
      activeView: 'registro',
      activeTab: 'teorico',
      showAIAssistant: false,
      selectedDay: null,
      
      // Estado do formulário teórico
      estudoTeorico: {
        disciplina: '',
        topico: '',
        data: '',
        dificuldade: ''
      },
      
      // Estado do formulário prático
      revisaoPratica: {
        materiaId: '',
        totalQuestoes: 30,
        acertos: 0
      },
      
      // Dados armazenados
      estudosTeoricos: [],
      revisoesPraticas: [],
      notificacoes: [],
      
      // Calendário
      currentMonth: new Date(),
      
      // AI Assistant
      aiMessages: [
        {
          id: 1,
          type: 'assistant',
          content: 'Olá! Sou seu assistente de revisões. Posso ajudá-lo a otimizar seus estudos, analisar seu desempenho e sugerir estratégias personalizadas. Como posso ajudar?'
        }
      ],
      aiInput: '',
      
      // Disciplinas médicas
      disciplinasMedicas: [
        { id: 'anatomia', nome: 'Anatomia', cor: '#e74c3c' },
        { id: 'fisiologia', nome: 'Fisiologia', cor: '#3498db' },
        { id: 'patologia', nome: 'Patologia', cor: '#9b59b6' },
        { id: 'farmacologia', nome: 'Farmacologia', cor: '#2ecc71' },
        { id: 'microbiologia', nome: 'Microbiologia', cor: '#f39c12' },
        { id: 'imunologia', nome: 'Imunologia', cor: '#e67e22' },
        { id: 'bioquimica', nome: 'Bioquímica', cor: '#1abc9c' },
        { id: 'genetica', nome: 'Genética', cor: '#34495e' },
        { id: 'semiologia', nome: 'Semiologia', cor: '#16a085' },
        { id: 'clinica', nome: 'Clínica Médica', cor: '#27ae60' }
      ],
      
      // Chart instance
      retentionChart: null
    }
  },
  
  computed: {
    hoje() {
      return new Date().toISOString().split('T')[0];
    },
    
    percentualCalculado() {
      if (this.revisaoPratica.totalQuestoes > 0) {
        return Math.round((this.revisaoPratica.acertos / this.revisaoPratica.totalQuestoes) * 100);
      }
      return null;
    },
    
    proximaRevisaoTexto() {
      if (this.percentualCalculado === null) return '';
      const dias = this.calcularIntervaloRevisao(this.percentualCalculado);
      return `Em ${dias} dias (${this.addDays(new Date(), dias).toLocaleDateString('pt-BR')})`;
    },
    
    estudosTeoricosPendentes() {
      return this.estudosTeoricos.filter(e => !e.concluido && new Date(e.primeiroContato) <= new Date());
    },
    
    revisoesAgendadas() {
      const hoje = new Date();
      hoje.setHours(0, 0, 0, 0);
      
      return this.revisoesPraticas
        .filter(r => !r.concluida && new Date(r.proximaRevisao) <= hoje)
        .map(r => ({
          ...r,
          materia: this.getMateriaNome(r.materiaId)
        }));
    },
    
    estudosTeoricosFiltrados() {
      return this.estudosTeoricos.filter(e => !e.concluido);
    },
    
    revisoesPraticasFiltradas() {
      return this.revisoesPraticas.filter(r => !r.concluida);
    },
    
    revisoesConcluidas() {
      const concluidas = {};
      
      this.revisoesPraticas
        .filter(r => r.concluida)
        .forEach(r => {
          const key = `${r.disciplina}-${r.topico}`;
          if (!concluidas[key]) {
            concluidas[key] = {
              id: key,
              disciplina: r.disciplina,
              topico: r.topico,
              totalRevisoes: 0,
              somaDesempenho: 0,
              ultimaRevisao: r.data
            };
          }
          concluidas[key].totalRevisoes++;
          concluidas[key].somaDesempenho += r.percentual;
          if (new Date(r.data) > new Date(concluidas[key].ultimaRevisao)) {
            concluidas[key].ultimaRevisao = r.data;
          }
        });
      
      return Object.values(concluidas).map(c => ({
        ...c,
        mediaDesempenho: Math.round(c.somaDesempenho / c.totalRevisoes)
      }));
    },
    
    // Estatísticas
    totalRevisoes() {
      return this.estudosTeoricos.length + this.revisoesPraticas.length;
    },
    
    taxaRetencao() {
      if (this.revisoesPraticas.length === 0) return 0;
      const soma = this.revisoesPraticas.reduce((acc, r) => acc + r.percentual, 0);
      return Math.round(soma / this.revisoesPraticas.length);
    },
    
    diasEstudados() {
      const dias = new Set();
      this.estudosTeoricos.forEach(e => dias.add(new Date(e.data).toDateString()));
      this.revisoesPraticas.forEach(r => dias.add(new Date(r.data).toDateString()));
      return dias.size;
    },
    
    indiceConsistencia() {
      // Calcula a consistência baseada na frequência de estudos
      const hoje = new Date();
      const diasAtras30 = new Date(hoje.getTime() - 30 * 24 * 60 * 60 * 1000);
      
      const estudosUltimos30Dias = [...this.estudosTeoricos, ...this.revisoesPraticas]
        .filter(item => new Date(item.data) >= diasAtras30);
      
      return Math.min(Math.round((estudosUltimos30Dias.length / 30) * 100), 100);
    },
    
    mediaDesempenho() {
      if (this.revisoesPraticas.length === 0) return 0;
      const soma = this.revisoesPraticas.reduce((acc, r) => acc + r.percentual, 0);
      return Math.round(soma / this.revisoesPraticas.length);
    },
    
    totalQuestoes() {
      return this.revisoesPraticas.reduce((acc, r) => acc + r.totalQuestoes, 0);
    },
    
    taxaAcerto() {
      const totalAcertos = this.revisoesPraticas.reduce((acc, r) => acc + r.acertos, 0);
      return this.totalQuestoes > 0 ? Math.round((totalAcertos / this.totalQuestoes) * 100) : 0;
    },
    
    distribuicaoDisciplinas() {
      const dist = {};
      
      [...this.estudosTeoricos, ...this.revisoesPraticas].forEach(item => {
        const disc = item.disciplina;
        if (!dist[disc]) {
          dist[disc] = { 
            disciplina: disc, 
            revisoes: 0,
            nome: this.getDisciplinaNome(disc),
            cor: this.getDisciplinaCor(disc)
          };
        }
        dist[disc].revisoes++;
      });
      
      const total = Object.values(dist).reduce((acc, d) => acc + d.revisoes, 0);
      
      return Object.values(dist).map(d => ({
        ...d,
        percentual: Math.round((d.revisoes / total) * 100)
      })).sort((a, b) => b.revisoes - a.revisoes);
    },
    
    padroesPorPeriodo() {
      const periodos = { manha: 0, tarde: 0, noite: 0 };
      
      this.revisoesPraticas.forEach(r => {
        const hora = new Date(r.data).getHours();
        if (hora >= 6 && hora < 12) periodos.manha++;
        else if (hora >= 12 && hora < 18) periodos.tarde++;
        else periodos.noite++;
      });
      
      const total = this.revisoesPraticas.length || 1;
      
      return {
        manha: Math.round((periodos.manha / total) * 100),
        tarde: Math.round((periodos.tarde / total) * 100),
        noite: Math.round((periodos.noite / total) * 100)
      };
    },
    
    // Calendário
    currentMonthYear() {
      return this.currentMonth.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' });
    },
    
    weekDays() {
      return ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
    },
    
    calendarDays() {
      const year = this.currentMonth.getFullYear();
      const month = this.currentMonth.getMonth();
      const firstDay = new Date(year, month, 1);
      const lastDay = new Date(year, month + 1, 0);
      const startCalendar = new Date(firstDay);
      startCalendar.setDate(startCalendar.getDate() - firstDay.getDay());
      
      const days = [];
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      for (let i = 0; i < 42; i++) {
        const current = new Date(startCalendar);
        current.setDate(startCalendar.getDate() + i);
        
        const dayRevisions = this.getRevisionsForDate(current);
        
        days.push({
          date: current.getDate(),
          fullDate: new Date(current),
          currentMonth: current.getMonth() === month,
          isToday: current.getTime() === today.getTime(),
          revisions: dayRevisions
        });
      }
      
      return days;
    },
    
    selectedDayDate() {
      return this.selectedDay ? this.selectedDay.fullDate : null;
    },
    
    // Notificações
    notificacoesPendentes() {
      return this.notificacoes.filter(n => !n.lida).length;
    },
    
    notificacoesOrdenadas() {
      return [...this.notificacoes].sort((a, b) => {
        if (!a.lida && b.lida) return -1;
        if (a.lida && !b.lida) return 1;
        return new Date(b.data) - new Date(a.data);
      });
    },
    
    sugestoesAI() {
      return [
        'Qual meu desempenho geral?',
        'Quais matérias preciso revisar mais?',
        'Como melhorar minha taxa de retenção?',
        'Análise dos últimos 30 dias'
      ];
    },
    
    getTabLabel() {
      return (tab) => {
        const labels = {
          teorico: 'Estudos Teóricos',
          pratico: 'Revisões Práticas',
          concluidas: 'Concluídas'
        };
        return labels[tab] || tab;
      };
    }
  },
  
  methods: {
    // Cálculos principais
    calcularPrimeiroContato() {
      if (!this.estudoTeorico.data || !this.estudoTeorico.dificuldade) return '';
      
      const dataEstudo = new Date(this.estudoTeorico.data);
      const dias = this.estudoTeorico.dificuldade === 'Fácil' ? 2 : 1;
      
      return this.addDays(dataEstudo, dias).toLocaleDateString('pt-BR', {
        weekday: 'long',
        day: 'numeric',
        month: 'long'
      });
    },
    
    calcularIntervaloRevisao(percentual) {
      // Fórmula baseada na metodologia
      if (percentual <= 50) return 2;
      if (percentual <= 55) return 7;
      if (percentual <= 60) return 14;
      if (percentual <= 65) return 18;
      if (percentual <= 75) return 24;
      if (percentual <= 80) return 30;
      return 35;
    },
    
    getExplicacaoIntervalo(percentual) {
      if (percentual <= 50) return 'Desempenho baixo - revisão urgente necessária';
      if (percentual <= 55) return 'Desempenho regular - reforço semanal';
      if (percentual <= 60) return 'Desempenho médio - revisão quinzenal';
      if (percentual <= 65) return 'Bom desempenho - consolidação em 18 dias';
      if (percentual <= 75) return 'Muito bom - revisão em 24 dias';
      if (percentual <= 80) return 'Excelente - revisão mensal';
      return 'Domínio do conteúdo - manutenção em 35 dias';
    },
    
    // Registro de dados
    registrarEstudoTeorico() {
      const dataEstudo = new Date(this.estudoTeorico.data);
      const diasAte = this.estudoTeorico.dificuldade === 'Fácil' ? 2 : 1;
      const primeiroContato = this.addDays(dataEstudo, diasAte);
      
      const novoEstudo = {
        id: Date.now(),
        ...this.estudoTeorico,
        primeiroContato: primeiroContato,
        concluido: false,
        criadoEm: new Date()
      };
      
      this.estudosTeoricos.push(novoEstudo);
      this.criarNotificacao(
        'Estudo Teórico Registrado',
        `Primeiro contato com questões agendado para ${this.formatDate(primeiroContato)}`,
        'info',
        novoEstudo.id
      );
      
      this.salvarDados();
      this.atualizarGraficos();
      
      // Limpar formulário
      this.estudoTeorico = {
        disciplina: '',
        topico: '',
        data: '',
        dificuldade: ''
      };
      
      this.$toast?.success('Estudo teórico registrado com sucesso!');
    },
    
    registrarRevisaoPratica() {
      const isRevisao = this.revisaoPratica.materiaId.startsWith('rev-');
      let disciplina, topico, numeroRevisao = 1;
      
      if (isRevisao) {
        // É uma revisão de uma prática anterior
        const revisaoAnterior = this.revisoesPraticas.find(r => r.id === parseInt(this.revisaoPratica.materiaId.replace('rev-', '')));
        disciplina = revisaoAnterior.disciplina;
        topico = revisaoAnterior.topico;
        numeroRevisao = revisaoAnterior.numeroRevisao + 1;
      } else {
        // É o primeiro contato após estudo teórico
        const estudo = this.estudosTeoricos.find(e => e.id === parseInt(this.revisaoPratica.materiaId));
        disciplina = estudo.disciplina;
        topico = estudo.topico;
        estudo.concluido = true;
      }
      
      const intervalo = this.calcularIntervaloRevisao(this.percentualCalculado);
      const proximaRevisao = this.addDays(new Date(), intervalo);
      
      const novaRevisao = {
        id: Date.now(),
        materiaId: this.revisaoPratica.materiaId,
        disciplina: disciplina,
        topico: topico,
        data: new Date(),
        totalQuestoes: this.revisaoPratica.totalQuestoes,
        acertos: this.revisaoPratica.acertos,
        percentual: this.percentualCalculado,
        intervalo: intervalo,
        proximaRevisao: proximaRevisao,
        numeroRevisao: numeroRevisao,
        concluida: false
      };
      
      this.revisoesPraticas.push(novaRevisao);
      
      // Criar notificação para próxima revisão
      this.criarNotificacao(
        'Próxima Revisão Agendada',
        `Revisão de "${topico}" agendada para ${this.formatDate(proximaRevisao)}`,
        'success',
        novaRevisao.id
      );
      
      this.salvarDados();
      this.atualizarGraficos();
      
      // Limpar formulário
      this.revisaoPratica = {
        materiaId: '',
        totalQuestoes: 30,
        acertos: 0
      };
      
      this.$toast?.success('Revisão prática registrada! Próxima revisão agendada.');
    },
    
    // Ações
    iniciarRevisao(estudo) {
      this.activeView = 'registro';
      this.revisaoPratica.materiaId = estudo.id;
    },
    
    iniciarProximaRevisao(revisao) {
      this.activeView = 'registro';
      this.revisaoPratica.materiaId = `rev-${revisao.id}`;
    },
    
    iniciarRevisaoDoDia(revisao) {
      this.selectedDay = null;
      if (revisao.type === 'teorico') {
        const estudo = this.estudosTeoricos.find(e => e.id === revisao.id);
        this.iniciarRevisao(estudo);
      } else {
        const rev = this.revisoesPraticas.find(r => r.id === revisao.id);
        this.iniciarProximaRevisao(rev);
      }
    },
    
    adiarRevisao(estudo) {
      estudo.primeiroContato = this.addDays(new Date(estudo.primeiroContato), 1);
      this.salvarDados();
      this.$toast?.info('Revisão adiada para amanhã');
    },
    
    adiarRevisaoPratica(revisao) {
      revisao.proximaRevisao = this.addDays(new Date(revisao.proximaRevisao), 1);
      this.salvarDados();
      this.$toast?.info('Revisão adiada para amanhã');
    },
    
    showDayDetails(day) {
      if (day.revisions.length > 0) {
        this.selectedDay = day;
      }
    },
    
    // Notificações
    criarNotificacao(titulo, mensagem, tipo, relatedId) {
      const notificacao = {
        id: Date.now(),
        titulo,
        mensagem,
        tipo,
        data: new Date(),
        lida: false,
        relatedId,
        acaoPrincipal: tipo === 'info' ? 'Ver' : 'Iniciar'
      };
      
      this.notificacoes.unshift(notificacao);
      this.salvarDados();
    },
    
    marcarTodasComoLidas() {
      this.notificacoes.forEach(n => n.lida = true);
      this.salvarDados();
    },
    
    executarAcaoNotificacao(notif) {
      notif.lida = true;
      
      // Encontrar e iniciar a revisão relacionada
      if (notif.relatedId) {
        const estudo = this.estudosTeoricos.find(e => e.id === notif.relatedId);
        const revisao = this.revisoesPraticas.find(r => r.id === notif.relatedId);
        
        if (estudo) {
          this.iniciarRevisao(estudo);
        } else if (revisao) {
          this.iniciarProximaRevisao(revisao);
        }
      }
      
      this.salvarDados();
    },
    
    dispensarNotificacao(notif) {
      const index = this.notificacoes.indexOf(notif);
      if (index > -1) {
        this.notificacoes.splice(index, 1);
        this.salvarDados();
      }
    },
    
    getNotificationIcon(tipo) {
      const icons = {
        info: 'info-circle',
        success: 'check-circle',
        warning: 'exclamation-triangle',
        error: 'times-circle'
      };
      return icons[tipo] || 'bell';
    },
    
    // AI Assistant
    sendAIMessage() {
      if (!this.aiInput.trim()) return;
      
      // Adicionar mensagem do usuário
      this.aiMessages.push({
        id: Date.now(),
        type: 'user',
        content: this.aiInput
      });
      
      const userInput = this.aiInput.toLowerCase();
      this.aiInput = '';
      
      // Simular resposta da IA
      setTimeout(() => {
        let response = '';
        
        if (userInput.includes('desempenho')) {
          response = `Seu desempenho geral está em ${this.mediaDesempenho}% com ${this.totalRevisoes} revisões realizadas. 
                     Sua taxa de retenção é de ${this.taxaRetencao}% e você mantém uma consistência de ${this.indiceConsistencia}% nos estudos.`;
        } else if (userInput.includes('matéria') || userInput.includes('disciplina')) {
          const top3 = this.distribuicaoDisciplinas.slice(0, 3);
          response = `Suas disciplinas mais estudadas são: ${top3.map(d => `${d.nome} (${d.revisoes} revisões)`).join(', ')}. `;
          
          if (this.mediaDesempenho < 70) {
            response += 'Recomendo focar nas disciplinas com menor desempenho para melhorar sua média geral.';
          }
        } else if (userInput.includes('retenção') || userInput.includes('melhorar')) {
          response = `Para melhorar sua taxa de retenção:
1. Mantenha consistência nas revisões (atual: ${this.indiceConsistencia}%)
2. Não pule as revisões agendadas
3. Estude em blocos de 25-30 questões
4. Revise imediatamente os erros
5. Use técnicas de memorização ativa`;
        } else if (userInput.includes('30 dias') || userInput.includes('análise')) {
          response = `Nos últimos 30 dias:
- Dias estudados: ${this.diasEstudados}
- Questões resolvidas: ${this.totalQuestoes}
- Taxa de acerto: ${this.taxaAcerto}%
- Melhor período: ${this.padroesPorPeriodo.manha > this.padroesPorPeriodo.tarde ? 'Manhã' : 'Tarde'}`;
        } else {
          response = 'Posso ajudá-lo com análises de desempenho, sugestões de estudo, estatísticas detalhadas e estratégias personalizadas. O que gostaria de saber?';
        }
        
        this.aiMessages.push({
          id: Date.now(),
          type: 'assistant',
          content: response
        });
        
        this.$nextTick(() => {
          this.scrollChatToBottom();
        });
      }, 1000);
    },
    
    scrollChatToBottom() {
      if (this.$refs.chatContainer) {
        this.$refs.chatContainer.scrollTop = this.$refs.chatContainer.scrollHeight;
      }
    },
    
    // Utilitários
    addDays(date, days) {
      const result = new Date(date);
      result.setDate(result.getDate() + days);
      return result;
    },
    
    formatDate(date) {
      if (!date) return '';
      return new Date(date).toLocaleDateString('pt-BR');
    },
    
    formatRelativeTime(date) {
      const now = new Date();
      const diff = now - new Date(date);
      const minutes = Math.floor(diff / 60000);
      const hours = Math.floor(minutes / 60);
      const days = Math.floor(hours / 24);
      
      if (days > 0) return `${days} dia${days > 1 ? 's' : ''} atrás`;
      if (hours > 0) return `${hours} hora${hours > 1 ? 's' : ''} atrás`;
      if (minutes > 0) return `${minutes} minuto${minutes > 1 ? 's' : ''} atrás`;
      return 'Agora mesmo';
    },
    
    getStatus(date) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const compareDate = new Date(date);
      compareDate.setHours(0, 0, 0, 0);
      
      if (compareDate < today) return 'overdue';
      if (compareDate.getTime() === today.getTime()) return 'today';
      return 'upcoming';
    },
    
    getStatusText(date) {
      const status = this.getStatus(date);
      if (status === 'overdue') return 'Atrasado';
      if (status === 'today') return 'Hoje';
      return 'Agendado';
    },
    
    getPerformanceColor(percentual) {
      if (percentual >= 80) return '#10b981';
      if (percentual >= 70) return '#3b82f6';
      if (percentual >= 60) return '#f59e0b';
      return '#ef4444';
    },
    
    getDisciplinaNome(disciplinaId) {
      const disc = this.disciplinasMedicas.find(d => d.id === disciplinaId);
      return disc ? disc.nome : disciplinaId;
    },
    
    getDisciplinaCor(disciplinaId) {
      const disc = this.disciplinasMedicas.find(d => d.id === disciplinaId);
      return disc ? disc.cor : '#64748b';
    },
    
    getMateriaNome(materiaId) {
      if (typeof materiaId === 'string' && materiaId.startsWith('rev-')) {
        const revisao = this.revisoesPraticas.find(r => r.id === parseInt(materiaId.replace('rev-', '')));
        return revisao ? `${this.getDisciplinaNome(revisao.disciplina)} - ${revisao.topico}` : '';
      }
      
      const estudo = this.estudosTeoricos.find(e => e.id === parseInt(materiaId));
      return estudo ? `${this.getDisciplinaNome(estudo.disciplina)} - ${estudo.topico}` : '';
    },
    
    getRevisionsForDate(date) {
      const revisions = [];
      const dateStr = date.toDateString();
      const hoje = new Date();
      hoje.setHours(0, 0, 0, 0);
      
      // Verificar estudos teóricos
      this.estudosTeoricos.forEach(estudo => {
        if (!estudo.concluido && new Date(estudo.primeiroContato).toDateString() === dateStr) {
          revisions.push({
            id: estudo.id,
            type: 'teorico',
            materia: this.getDisciplinaNome(estudo.disciplina),
            topico: estudo.topico,
            overdue: new Date(estudo.primeiroContato) < hoje
          });
        }
      });
      
      // Verificar revisões práticas
      this.revisoesPraticas.forEach(revisao => {
        if (!revisao.concluida && new Date(revisao.proximaRevisao).toDateString() === dateStr) {
          revisions.push({
            id: revisao.id,
            type: 'pratico',
            materia: this.getDisciplinaNome(revisao.disciplina),
            topico: revisao.topico,
            numeroRevisao: revisao.numeroRevisao,
            overdue: new Date(revisao.proximaRevisao) < hoje
          });
        }
      });
      
      return revisions;
    },
    
    getParticleStyle(index) {
      return {
        left: `${Math.random() * 100}%`,
        top: `${Math.random() * 100}%`,
        animationDelay: `${Math.random() * 10}s`,
        animationDuration: `${20 + Math.random() * 30}s`
      };
    },
    
    onMateriaChange() {
      // Reset questões quando mudar a matéria
      this.revisaoPratica.acertos = 0;
    },
    
    // Navegação do calendário
    previousMonth() {
      this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() - 1);
    },
    
    nextMonth() {
      this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() + 1);
    },
    
    // Gráficos
    atualizarGraficos() {
      if (this.activeView === 'dashboard' && this.$refs.retentionChart) {
        this.renderRetentionChart();
      }
    },
    
    renderRetentionChart() {
      const ctx = this.$refs.retentionChart?.getContext('2d');
      if (!ctx) return;
      
      // Destruir gráfico anterior se existir
      if (this.retentionChart) {
        this.retentionChart.destroy();
      }
      
      // Preparar dados para o gráfico
      const ultimas10Revisoes = this.revisoesPraticas
        .slice(-10)
        .map((r, index) => ({
          x: index + 1,
          y: r.percentual
        }));
      
      this.retentionChart = new Chart(ctx, {
        type: 'line',
        data: {
          datasets: [{
            label: 'Desempenho (%)',
            data: ultimas10Revisoes,
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            x: {
              type: 'linear',
              title: {
                display: true,
                text: 'Número da Revisão'
              }
            },
            y: {
              beginAtZero: true,
              max: 100,
              title: {
                display: true,
                text: 'Desempenho (%)'
              }
            }
          },
          plugins: {
            legend: {
              display: false
            }
          }
        }
      });
    },
    
    // Persistência de dados
    salvarDados() {
      const dados = {
        estudosTeoricos: this.estudosTeoricos,
        revisoesPraticas: this.revisoesPraticas,
        notificacoes: this.notificacoes
      };
      localStorage.setItem('revisionSchedulerUnified', JSON.stringify(dados));
    },
    
    carregarDados() {
      const dados = localStorage.getItem('revisionSchedulerUnified');
      if (dados) {
        const parsed = JSON.parse(dados);
        this.estudosTeoricos = parsed.estudosTeoricos || [];
        this.revisoesPraticas = parsed.revisoesPraticas || [];
        this.notificacoes = parsed.notificacoes || [];
      }
      
      // Verificar e criar notificações para revisões pendentes
      this.verificarRevisoesPendentes();
    },
    
    verificarRevisoesPendentes() {
      const hoje = new Date();
      hoje.setHours(0, 0, 0, 0);
      
      // Verificar estudos teóricos
      this.estudosTeoricos
        .filter(e => !e.concluido && new Date(e.primeiroContato) <= hoje)
        .forEach(e => {
          const notifExiste = this.notificacoes.some(n => n.relatedId === e.id && !n.lida);
          if (!notifExiste) {
            this.criarNotificacao(
              'Primeiro Contato Pendente',
              `Hora de fazer questões sobre "${e.topico}"`,
              'warning',
              e.id
            );
          }
        });
      
      // Verificar revisões práticas
      this.revisoesPraticas
        .filter(r => !r.concluida && new Date(r.proximaRevisao) <= hoje)
        .forEach(r => {
          const notifExiste = this.notificacoes.some(n => n.relatedId === r.id && !n.lida);
          if (!notifExiste) {
            this.criarNotificacao(
              'Revisão Pendente',
              `Revisão #${r.numeroRevisao} de "${r.topico}" está agendada para hoje`,
              'warning',
              r.id
            );
          }
        });
    }
  },
  
  mounted() {
    this.carregarDados();
    
    // Atualizar gráficos quando mudar para dashboard
    this.$watch('activeView', (newView) => {
      if (newView === 'dashboard') {
        this.$nextTick(() => {
          this.atualizarGraficos();
        });
      }
    });
  },
  
  beforeUnmount() {
    if (this.retentionChart) {
      this.retentionChart.destroy();
    }
  }
}
</script>

<style scoped>
/* Reset e Variáveis CSS */
:root {
  --primary: #6366f1;
  --secondary: #8b5cf6;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --info: #3b82f6;
  --dark: #1e293b;
  --darker: #0f172a;
  --light: #f8fafc;
  --text: #94a3b8;
  --text-light: #cbd5e1;
  --border: #334155;
  --shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

* {
  box-sizing: border-box;
}

/* Container Principal */
.revision-scheduler-unified {
  min-height: 100vh;
  background: var(--darker);
  color: var(--light);
  position: relative;
  overflow-x: hidden;
}

/* Efeitos de Fundo */
.background-effects {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.gradient-bg {
  position: absolute;
  inset: 0;
  background: 
    radial-gradient(circle at 20% 30%, rgba(99, 102, 241, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
}

.floating-particles {
  position: absolute;
  inset: 0;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--primary);
  border-radius: 50%;
  opacity: 0.3;
  animation: float-particle 20s infinite ease-in-out;
}

.particle:nth-child(even) {
  background: var(--secondary);
  animation-duration: 25s;
}

.particle:nth-child(3n) {
  background: var(--info);
  animation-duration: 30s;
}

@keyframes float-particle {
  0%, 100% {
    transform: translateY(0) translateX(0) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 0.3;
    transform: scale(1);
  }
  90% {
    opacity: 0.3;
  }
  50% {
    transform: translateY(-200px) translateX(100px) scale(1);
  }
}

/* Container */
.container {
  position: relative;
  z-index: 1;
  max-width: 1600px;
  margin: 0 auto;
  padding: 2rem;
}

/* Header */
.header {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border);
  border-radius: 1rem;
  padding: 2.5rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.title-section h1 {
  font-size: 2.5rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 1rem;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.brain-icon {
  color: var(--primary);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.subtitle {
  color: var(--text);
  margin: 0.5rem 0 0;
  font-size: 1.125rem;
}

.stats-summary {
  display: flex;
  gap: 3rem;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 2.5rem;
  font-weight: bold;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stat-label {
  display: block;
  font-size: 0.875rem;
  color: var(--text);
  margin-top: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Quick Actions */
.quick-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.action-btn {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border);
  color: var(--text-light);
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  position: relative;
}

.action-btn:hover {
  background: rgba(99, 102, 241, 0.2);
  border-color: var(--primary);
  color: var(--light);
  transform: translateY(-2px);
}

.action-btn.active {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-color: transparent;
  color: white;
}

.action-btn.ai-btn {
  background: linear-gradient(135deg, var(--secondary), var(--primary));
  border: none;
  color: white;
}

.action-btn.ai-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--danger);
  color: white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-weight: bold;
}

/* Main Content */
.main-content {
  min-height: 600px;
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

/* Study Sections */
.study-section {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
}

.study-section:hover {
  border-color: var(--primary);
  box-shadow: 0 15px 40px rgba(99, 102, 241, 0.2);
}

.section-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border);
}

.section-header h2 {
  font-size: 1.75rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--light);
}

.section-header p {
  color: var(--text);
  margin: 0.5rem 0 0;
}

/* Forms */
.teorico-form,
.pratica-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.form-group label {
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-light);
}

.form-group input,
.form-group select {
  background: var(--dark);
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  padding: 0.875rem;
  color: var(--light);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  background: rgba(99, 102, 241, 0.05);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* Difficulty Selector */
.difficulty-selector {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.diff-option {
  background: var(--dark);
  border: 2px solid var(--border);
  border-radius: 0.75rem;
  padding: 1.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.diff-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent, rgba(99, 102, 241, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.diff-option:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
}

.diff-option:hover::before {
  opacity: 1;
}

.diff-option.active {
  border-color: var(--primary);
  background: rgba(99, 102, 241, 0.1);
}

.diff-option span {
  font-weight: 600;
  position: relative;
  z-index: 1;
}

.diff-option small {
  font-size: 0.75rem;
  color: var(--text);
  position: relative;
  z-index: 1;
}

/* Preview Cards */
.form-preview,
.performance-preview {
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid var(--primary);
  border-radius: 0.75rem;
  padding: 1.25rem;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.preview-card {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.preview-icon {
  font-size: 2.5rem;
  color: var(--primary);
}

/* Performance Meter */
.performance-meter {
  position: relative;
  height: 2.5rem;
  background: var(--dark);
  border-radius: 1.25rem;
  overflow: hidden;
  margin-bottom: 1rem;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

.meter-fill {
  position: absolute;
  height: 100%;
  transition: width 0.5s ease, background 0.5s ease;
  box-shadow: 0 0 20px currentColor;
}

.meter-label {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1rem;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.next-revision-preview {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.revision-explanation {
  display: block;
  color: var(--text);
  font-style: italic;
}

/* Submit Button */
.submit-btn {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  border: none;
  border-radius: 0.75rem;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  position: relative;
  overflow: hidden;
}

.submit-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
}

.submit-btn:hover:not(:disabled)::before {
  width: 300px;
  height: 300px;
}

.submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Dashboard View */
.dashboard-view {
  animation: fadeIn 0.3s ease;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.dashboard-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  border-color: var(--primary);
  box-shadow: 0 15px 40px rgba(99, 102, 241, 0.2);
}

.dashboard-card h3 {
  font-size: 1.25rem;
  margin: 0 0 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--light);
}

/* Overview Stats */
.overview-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  text-align: center;
}

.overview-stat {
  padding: 1rem;
  background: var(--dark);
  border-radius: 0.5rem;
}

.overview-stat .stat-label {
  display: block;
  font-size: 0.75rem;
  color: var(--text);
  margin-bottom: 0.5rem;
}

.overview-stat .stat-value {
  font-size: 1.75rem;
  font-weight: bold;
}

/* Discipline Distribution */
.discipline-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.discipline-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.discipline-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.discipline-name {
  font-weight: 500;
  color: var(--light);
}

.discipline-count {
  font-size: 0.875rem;
  color: var(--text);
}

.discipline-bar {
  height: 8px;
  background: var(--dark);
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  transition: width 0.5s ease;
}

/* Study Patterns */
.patterns-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.pattern-item {
  text-align: center;
  padding: 1rem;
  background: var(--dark);
  border-radius: 0.5rem;
}

.pattern-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.pattern-icon.morning {
  color: #f59e0b;
}

.pattern-icon.afternoon {
  color: #3b82f6;
}

.pattern-icon.night {
  color: #8b5cf6;
}

.pattern-label {
  display: block;
  font-size: 0.875rem;
  color: var(--text);
  margin-bottom: 0.25rem;
}

.pattern-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--light);
}

/* Retention Chart */
.retention-chart {
  height: 250px;
  position: relative;
}

.retention-chart canvas {
  max-height: 100%;
}

/* Revision Tables */
.revision-tables {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: var(--shadow);
}

.table-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border);
  padding-bottom: 1rem;
}

.tab-btn {
  background: transparent;
  border: none;
  color: var(--text);
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  font-size: 1rem;
}

.tab-btn::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.tab-btn:hover {
  color: var(--light);
}

.tab-btn.active {
  color: var(--primary);
}

.tab-btn.active::after {
  transform: scaleX(1);
}

/* Tables */
.revision-table {
  overflow-x: auto;
  margin-top: 2rem;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th {
  background: var(--dark);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  border-bottom: 2px solid var(--border);
  color: var(--text-light);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

td {
  padding: 1rem;
  border-bottom: 1px solid var(--border);
  vertical-align: middle;
}

tr {
  transition: background 0.3s ease;
}

tr:hover {
  background: rgba(99, 102, 241, 0.05);
}

/* Discipline Badge */
.discipline-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Status Badges */
.badge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 600;
}

.badge-fácil {
  background: rgba(16, 185, 129, 0.2);
  color: var(--success);
}

.badge-difícil {
  background: rgba(239, 68, 68, 0.2);
  color: var(--danger);
}

/* Status */
.status {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.status.overdue {
  background: rgba(239, 68, 68, 0.2);
  color: var(--danger);
}

.status.today {
  background: rgba(245, 158, 11, 0.2);
  color: var(--warning);
}

.status.upcoming {
  background: rgba(99, 102, 241, 0.2);
  color: var(--primary);
}

.status.completed {
  background: rgba(16, 185, 129, 0.2);
  color: var(--success);
}

/* Performance Cell */
.performance-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.mini-meter {
  flex: 1;
  max-width: 100px;
  height: 8px;
  background: var(--dark);
  border-radius: 4px;
  overflow: hidden;
}

.mini-fill {
  height: 100%;
  transition: width 0.5s ease;
}

/* Revision Number */
.revision-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: var(--primary);
  color: white;
  border-radius: 50%;
  font-weight: bold;
  font-size: 0.875rem;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.action-btn.primary {
  background: var(--primary);
  color: white;
  border: none;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn.warning {
  background: var(--warning);
  color: white;
  border: none;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Calendar View */
.calendar-view {
  animation: fadeIn 0.3s ease;
}

.calendar-container {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: var(--shadow);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.calendar-header h3 {
  font-size: 1.5rem;
  margin: 0;
  text-transform: capitalize;
  color: var(--light);
}

.nav-btn {
  background: var(--dark);
  border: 1px solid var(--border);
  color: var(--light);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  border-color: var(--primary);
  color: var(--primary);
  background: rgba(99, 102, 241, 0.1);
}

.calendar-legend {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text);
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-dot.teorico {
  background: var(--primary);
}

.legend-dot.pratico {
  background: var(--secondary);
}

.legend-dot.vencido {
  background: var(--danger);
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: var(--border);
  border: 1px solid var(--border);
  border-radius: 0.75rem;
  overflow: hidden;
}

.weekday {
  background: var(--dark);
  padding: 1rem;
  text-align: center;
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--text-light);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.calendar-day {
  background: rgba(30, 41, 59, 0.5);
  min-height: 100px;
  padding: 0.75rem;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
}

.calendar-day:hover {
  background: rgba(99, 102, 241, 0.1);
}

.calendar-day.other-month {
  opacity: 0.3;
}

.calendar-day.today {
  background: rgba(99, 102, 241, 0.2);
}

.calendar-day.today .day-number {
  font-weight: bold;
  color: var(--primary);
}

.calendar-day.has-revision {
  cursor: pointer;
}

.day-number {
  font-weight: 500;
  font-size: 0.875rem;
  color: var(--light);
}

.day-revisions {
  display: flex;
  gap: 0.25rem;
  margin-top: 0.5rem;
  flex-wrap: wrap;
  align-items: center;
}

.revision-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  color: white;
  transition: all 0.3s ease;
}

.revision-indicator.teorico {
  background: var(--primary);
}

.revision-indicator.pratico {
  background: var(--secondary);
}

.revision-indicator.overdue {
  background: var(--danger);
  animation: pulse-danger 2s infinite;
}

@keyframes pulse-danger {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(239, 68, 68, 0);
  }
}

.more-indicator {
  font-size: 0.75rem;
  color: var(--text);
  padding: 0.25rem 0.5rem;
  background: var(--dark);
  border-radius: 9999px;
}

/* Notifications View */
.notifications-view {
  animation: fadeIn 0.3s ease;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.notifications-header h2 {
  font-size: 1.75rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.mark-all-btn {
  background: var(--dark);
  border: 1px solid var(--border);
  color: var(--text-light);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mark-all-btn:hover {
  border-color: var(--primary);
  color: var(--primary);
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.notification-item {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border);
  border-radius: 0.75rem;
  padding: 1.5rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  transition: all 0.3s ease;
}

.notification-item.unread {
  border-color: var(--primary);
  background: rgba(99, 102, 241, 0.05);
}

.notification-item:hover {
  transform: translateX(5px);
  box-shadow: var(--shadow);
}

.notif-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.notification-item.info .notif-icon {
  background: rgba(59, 130, 246, 0.2);
  color: var(--info);
}

.notification-item.success .notif-icon {
  background: rgba(16, 185, 129, 0.2);
  color: var(--success);
}

.notification-item.warning .notif-icon {
  background: rgba(245, 158, 11, 0.2);
  color: var(--warning);
}

.notification-item.error .notif-icon {
  background: rgba(239, 68, 68, 0.2);
  color: var(--danger);
}

.notif-content {
  flex: 1;
}

.notif-content h4 {
  margin: 0 0 0.5rem;
  color: var(--light);
  font-size: 1.125rem;
}

.notif-content p {
  margin: 0 0 0.5rem;
  color: var(--text);
  line-height: 1.5;
}

.notif-time {
  font-size: 0.75rem;
  color: var(--text);
}

.notif-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.notif-action-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
  font-weight: 500;
}

.notif-action-btn.primary {
  background: var(--primary);
  color: white;
}

.notif-action-btn.secondary {
  background: var(--dark);
  color: var(--text-light);
  border: 1px solid var(--border);
}

.notif-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.empty-notifications {
  text-align: center;
  padding: 4rem;
}

.empty-icon {
  font-size: 4rem;
  color: var(--text);
  margin-bottom: 1rem;
}

.empty-notifications p {
  color: var(--text);
  font-size: 1.125rem;
}

/* Modal Overlay */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-content {
  background: var(--dark);
  border: 1px solid var(--border);
  border-radius: 1rem;
  width: 100%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow);
}

.modal-content.ai-assistant {
  max-width: 700px;
}

.modal-content.day-details {
  max-width: 500px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border);
}

.modal-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--light);
}

.close-btn {
  background: transparent;
  border: none;
  color: var(--text);
  font-size: 1.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--light);
}

/* AI Chat */
.ai-chat {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 1.5rem;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 1rem;
  padding-right: 0.5rem;
}

.chat-message {
  margin-bottom: 1rem;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.chat-message.user {
  text-align: right;
}

.chat-message.user .message-content {
  background: var(--primary);
  color: white;
  margin-left: auto;
}

.chat-message.assistant .message-content {
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid var(--primary);
}

.message-content {
  display: inline-block;
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  max-width: 80%;
  line-height: 1.5;
}

.chat-input {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.chat-input input {
  flex: 1;
  background: var(--darker);
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  padding: 0.75rem;
  color: var(--light);
  font-size: 1rem;
}

.chat-input input:focus {
  outline: none;
  border-color: var(--primary);
}

.chat-input button {
  background: var(--primary);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chat-input button:hover:not(:disabled) {
  background: var(--secondary);
  transform: translateY(-2px);
}

.chat-input button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.ai-suggestions {
  border-top: 1px solid var(--border);
  padding-top: 1rem;
}

.ai-suggestions p {
  margin: 0 0 0.5rem;
  font-size: 0.875rem;
  color: var(--text);
}

.suggestion-btn {
  background: var(--darker);
  border: 1px solid var(--border);
  color: var(--text-light);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  margin: 0.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.suggestion-btn:hover {
  border-color: var(--primary);
  color: var(--primary);
  background: rgba(99, 102, 241, 0.1);
}

/* Day Details */
.day-revisions-list {
  padding: 1.5rem;
  max-height: 400px;
  overflow-y: auto;
}

.day-revision-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid var(--border);
  border-radius: 0.75rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.day-revision-item:hover {
  border-color: var(--primary);
  background: rgba(99, 102, 241, 0.05);
}

.revision-type-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
  flex-shrink: 0;
}

.revision-type-icon.teorico {
  background: var(--primary);
}

.revision-type-icon.pratico {
  background: var(--secondary);
}

.revision-details {
  flex: 1;
}

.revision-details h4 {
  margin: 0 0 0.25rem;
  color: var(--light);
}

.revision-details p {
  margin: 0 0 0.25rem;
  color: var(--text);
  font-size: 0.875rem;
}

.revision-meta {
  font-size: 0.75rem;
  color: var(--text);
  font-style: italic;
}

.start-revision-btn {
  background: var(--primary);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.start-revision-btn:hover {
  background: var(--secondary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--dark);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text);
}

/* Transitions */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-from .modal-content,
.modal-leave-to .modal-content {
  transform: scale(0.9);
}

/* Responsive */
@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stats-summary {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .stat-item {
    min-width: 100px;
  }
  
  .quick-actions {
    justify-content: center;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .difficulty-selector {
    grid-template-columns: 1fr;
  }
  
  .table-tabs {
    flex-wrap: wrap;
  }
  
  .revision-table {
    font-size: 0.875rem;
  }
  
  th, td {
    padding: 0.5rem;
  }
  
  .calendar-legend {
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .calendar-day {
    min-height: 70px;
    padding: 0.5rem;
  }
  
  .patterns-grid {
    grid-template-columns: 1fr;
  }
  
  .overview-stats {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .title-section h1 {
    font-size: 1.75rem;
  }
  
  .action-btn {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }
  
  .action-buttons {
    flex-direction: column;
    width: 100%;
  }
  
  .action-btn.primary,
  .action-btn.warning {
    width: 100%;
  }
}
</style>