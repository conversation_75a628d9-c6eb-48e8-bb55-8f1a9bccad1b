<template>
  <div class="thanos-container">
    <!-- Premium Hero Header -->
    <header class="thanos-hero">
      <!-- Hero Content -->
      <div class="hero-content">
        <div class="hero-wrapper">
          <!-- Main Title Section -->
          <div class="hero-title-section">
            <div class="title-content">
              <h1 class="hero-title">
                <span class="title-main">Thanos</span>
                <span class="title-ai">AI</span>
              </h1>
              <p class="hero-subtitle">
                Seu assistente de estudos com inteligência artificial avançada
              </p>
            </div>
          </div>
          
          <!-- Enhanced Search -->
          <div class="hero-search">
            <div class="search-wrapper">
              <div class="search-icon-container">
                <font-awesome-icon icon="fa-solid fa-brain" />
              </div>
              <textarea 
                v-model="userInput"
                placeholder="Digite sua pergunta, tópico ou comando..."
                class="search-input"
                rows="1"
                @keydown.enter.prevent="sendMessage"
              ></textarea>
              <button v-if="userInput.trim()" @click="clearInput" class="search-clear">
                <font-awesome-icon icon="fa-solid fa-times" />
              </button>
            </div>
          </div>
          
          <!-- Stats Cards -->
          <div class="stats-showcase">
            <div class="stat-card">
              <div class="stat-icon">
                <font-awesome-icon icon="fa-solid fa-comments" />
              </div>
              <div class="stat-content">
                <span class="stat-number">{{ chatSessions.length }}</span>
                <span class="stat-label">Conversas</span>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">
                <font-awesome-icon icon="fa-solid fa-brain" />
              </div>
              <div class="stat-content">
                <span class="stat-number">{{ isInitialized ? 'ON' : 'OFF' }}</span>
                <span class="stat-label">IA Inicializada</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
      <!-- Chat Section -->
      <section class="chat-section">
        <div class="section-header">
          <div class="section-title">
            <div class="title-content">
              <font-awesome-icon icon="fa-solid fa-comments" class="section-icon" />
              <span class="title-text">Conversa <span class="highlight">Ativa</span></span>
            </div>
          </div>
        </div>

        <!-- Chat Interface -->
        <div class="chat-interface">
          <!-- Chat History -->
          <div class="chat-history" ref="chatContainer">
            <div v-if="!isInitialized" class="welcome-message">
              <div class="welcome-icon">
                <font-awesome-icon icon="fa-solid fa-brain" />
              </div>
              <h3>Bem-vindo ao Thanos AI</h3>
              <p>Para começar, você pode fazer uma pergunta diretamente ou inicializar com um documento.</p>
            </div>

            <div
              v-for="(msg, index) in currentMessages"
              :key="index"
              :class="['message', msg.sender]"
            >
              <div class="message-avatar">
                <font-awesome-icon 
                  :icon="msg.sender === 'user' ? 'fa-solid fa-user' : 'fa-solid fa-brain'" 
                />
              </div>
              <div class="message-content">
                <div class="message-text" v-html="msg.text.replace(/\\n/g, '<br>')"></div>
                <small class="message-meta">{{ formatTimestamp(msg.timestamp) }}</small>
              </div>
            </div>

            <div v-if="isLoading" class="typing-indicator">
              <div class="typing-avatar">
                <font-awesome-icon icon="fa-solid fa-brain" />
              </div>
              <div class="typing-content">
                <div class="typing-dots">
                  <div class="typing-dot"></div>
                  <div class="typing-dot"></div>
                  <div class="typing-dot"></div>
                </div>
                <span class="typing-text">Thanos está pensando...</span>
              </div>
            </div>
          </div>

          <!-- Chat Input -->
          <div class="chat-input-area">
            <div class="input-wrapper">
              <button 
                v-if="!isInitialized"
                @click="showInitModal = true"
                class="init-trigger-button"
              >
                <font-awesome-icon icon="fa-solid fa-rocket" />
                <span>Inicializar IA</span>
              </button>
              
              <button @click="sendMessage" :disabled="isLoading || !userInput.trim()" class="send-button">
                <font-awesome-icon :icon="isLoading ? 'fa-spinner fa-spin' : 'fa-paper-plane'" />
              </button>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- Simple Modal for Initialization -->
    <div v-if="showInitModal" class="modal-overlay" @click="showInitModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h2>Inicializar Thanos AI</h2>
          <button @click="showInitModal = false" class="modal-close">
            <font-awesome-icon icon="fa-solid fa-times" />
          </button>
        </div>
        
        <div class="modal-body">
          <div class="form-group">
            <label>Provedor:</label>
            <select v-model="selectedProvider" class="form-select">
              <option>OpenAI</option>
              <option>Groq</option>
              <option>Anthropic</option>
            </select>
          </div>

          <div class="form-group">
            <label>API Key:</label>
            <input
              type="password"
              v-model="apiKey"
              placeholder="Sua API key aqui"
              class="form-input"
            />
          </div>
        </div>

        <div class="modal-footer">
          <button @click="showInitModal = false" class="action-button secondary">
            Cancelar
          </button>
          <button
            @click="initializeThanos"
            :disabled="isLoading || !apiKey"
            class="action-button primary"
          >
            <font-awesome-icon :icon="isLoading ? 'fa-spinner fa-spin' : 'fa-rocket'" />
            <span>{{ isLoading ? 'Inicializando...' : 'Inicializar' }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useStore } from 'vuex'

export default {
  name: 'ThanosFixed',
  setup() {
    const store = useStore()

    // Reactive data
    const userInput = ref('')
    const isLoading = ref(false)
    const showInitModal = ref(false)
    const selectedProvider = ref('OpenAI')
    const apiKey = ref('')
    const currentMessages = ref([])

    // Computed state
    const isInitialized = computed(() => store.getters['thanos/isInitialized'] || false)
    const chatSessions = computed(() => store.getters['thanos/allSessions'] || [])

    // Refs
    const chatContainer = ref(null)

    // Methods
    const formatTimestamp = (ts) => {
      if (!ts) return 'N/A'
      const date = new Date(ts)
      return date.toLocaleString()
    }

    const clearInput = () => {
      userInput.value = ''
    }

    const scrollToBottom = () => {
      nextTick(() => {
        const el = chatContainer.value
        if (el) el.scrollTop = el.scrollHeight
      })
    }

    const sendMessage = async () => {
      if (!userInput.value.trim()) return

      const content = userInput.value.trim()
      userInput.value = ''
      isLoading.value = true

      // Add user message
      currentMessages.value.push({
        sender: 'user',
        text: content,
        timestamp: new Date().toISOString()
      })

      scrollToBottom()

      try {
        // Simulate AI response
        setTimeout(() => {
          currentMessages.value.push({
            sender: 'ai',
            text: `Esta é uma resposta simulada para: "${content}". O Thanos AI está em desenvolvimento!`,
            timestamp: new Date().toISOString()
          })
          isLoading.value = false
          scrollToBottom()
        }, 1500)
      } catch (error) {
        console.error('Erro ao enviar mensagem:', error)
        isLoading.value = false
      }
    }

    const initializeThanos = async () => {
      if (!apiKey.value) {
        alert('Por favor, forneça uma API key válida.')
        return
      }

      isLoading.value = true
      
      try {
        // Simulate initialization
        setTimeout(() => {
          currentMessages.value.push({
            sender: 'ai',
            text: 'Thanos AI inicializado com sucesso! Como posso ajudar com seus estudos hoje?',
            timestamp: new Date().toISOString()
          })
          
          showInitModal.value = false
          isLoading.value = false
          scrollToBottom()
        }, 2000)
      } catch (error) {
        console.error('Erro ao inicializar:', error)
        isLoading.value = false
      }
    }

    // Lifecycle
    onMounted(() => {
      scrollToBottom()
    })

    return {
      userInput,
      isLoading,
      showInitModal,
      selectedProvider,
      apiKey,
      currentMessages,
      isInitialized,
      chatSessions,
      chatContainer,
      formatTimestamp,
      clearInput,
      sendMessage,
      initializeThanos
    }
  }
}
</script>

<style scoped>
/* Container */
.thanos-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  position: relative;
  overflow-x: hidden;
}

/* Premium Hero Section */
.thanos-hero {
  position: relative;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  overflow: hidden;
}

/* Hero Content */
.hero-content {
  position: relative;
  z-index: 10;
  text-align: center;
  max-width: 1000px;
  padding: 2.5rem 2rem;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(25px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.25);
}

.hero-wrapper {
  max-width: 700px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.hero-title-section {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.title-content {
  position: relative;
  z-index: 15;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin: 0;
  letter-spacing: -1px;
  line-height: 1.1;
}

.title-main {
  background: linear-gradient(45deg, #fff, #4ecdc4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-ai {
  background: linear-gradient(45deg, #4ecdc4, #667eea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 8px 30px rgba(78, 205, 196, 0.3);
}

.hero-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.75);
  font-weight: 400;
  line-height: 1.5;
  margin: 0;
  max-width: 400px;
}

/* Hero Search */
.hero-search {
  position: relative;
  width: 100%;
}

.search-wrapper {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
  display: flex;
  align-items: stretch;
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  padding: 6px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.search-wrapper:focus-within {
  border-color: rgba(78, 205, 196, 0.5);
  box-shadow: 0 0 30px rgba(78, 205, 196, 0.2);
}

.search-icon-container {
  padding: 1rem 1.2rem;
  color: rgba(255, 255, 255, 0.5);
  font-size: 1rem;
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  padding: 1rem 0.8rem;
  border: none;
  background: transparent;
  color: white;
  font-size: 1rem;
  outline: none;
  resize: none;
  min-height: 20px;
  max-height: 100px;
  font-family: inherit;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.search-clear {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 1rem 1.2rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.search-clear:hover {
  color: white;
  transform: scale(1.1);
}

/* Stats Showcase */
.stats-showcase {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.stat-card {
  background: rgba(255, 255, 255, 0.04);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 1.5rem 1.8rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 140px;
  flex: 0 0 auto;
}

.stat-card:hover {
  transform: translateY(-6px);
  background: rgba(255, 255, 255, 0.06);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
  border-color: rgba(78, 205, 196, 0.15);
}

.stat-icon {
  font-size: 2rem;
  background: linear-gradient(45deg, #4ecdc4, #667eea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.8rem;
  position: relative;
  z-index: 2;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-number {
  display: block;
  font-size: 2.2rem;
  font-weight: 800;
  background: linear-gradient(45deg, #4ecdc4, #667eea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.3rem;
  line-height: 1;
}

.stat-label {
  color: rgba(255, 255, 255, 0.65);
  font-size: 0.85rem;
  font-weight: 500;
  line-height: 1.2;
}

/* Main Content */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  position: relative;
  z-index: 5;
}

/* Section Headers */
.section-header {
  margin-bottom: 3rem;
  position: relative;
  padding: 2rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 1.2rem;
  padding: 1.2rem 2.5rem;
  margin: 0 0 1.5rem 0;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 50px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
  font-size: 1.1rem;
  min-height: 58px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.title-content {
  display: flex;
  align-items: center;
  gap: 1.2rem;
  position: relative;
  z-index: 2;
}

.section-icon {
  font-size: 1.4rem;
  transition: all 0.3s ease;
}

.title-text {
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.title-text .highlight {
  font-weight: 600;
}

/* Chat Interface */
.chat-interface {
  background: rgba(255, 255, 255, 0.04);
  backdrop-filter: blur(25px);
  border-radius: 25px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  height: 70vh;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

/* Chat History */
.chat-history {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 15px;
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  scroll-behavior: smooth;
}

.chat-history::-webkit-scrollbar {
  width: 6px;
}

.chat-history::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.chat-history::-webkit-scrollbar-thumb {
  background: rgba(78, 205, 196, 0.3);
  border-radius: 3px;
}

/* Welcome Message */
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  background: rgba(78, 205, 196, 0.1);
  border-radius: 20px;
  padding: 3rem 2rem;
  margin: 2rem 0;
  color: rgba(255, 255, 255, 0.9);
  border: 1px dashed rgba(78, 205, 196, 0.3);
}

.welcome-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  color: #4ecdc4;
}

.welcome-message h3 {
  margin: 0.5rem 0 1.5rem;
  color: white;
  font-size: 1.8rem;
  font-weight: 700;
}

/* Messages */
.message {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  animation: message-appear 0.3s ease-out;
}

.message.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 1.2rem;
}

.message.user .message-avatar {
  background: linear-gradient(135deg, #4ecdc4, #667eea);
  color: white;
}

.message.ai .message-avatar {
  background: rgba(78, 205, 196, 0.2);
  color: #4ecdc4;
  border: 2px solid rgba(78, 205, 196, 0.3);
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-text {
  padding: 1rem 1.5rem;
  border-radius: 20px;
  line-height: 1.6;
  word-wrap: break-word;
  position: relative;
}

.message.user .message-text {
  background: linear-gradient(135deg, #4ecdc4, #667eea);
  color: white;
  border-bottom-right-radius: 5px;
}

.message.ai .message-text {
  background: rgba(255, 255, 255, 0.06);
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom-left-radius: 5px;
}

.message-meta {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 0.5rem;
  padding: 0 1.5rem;
}

@keyframes message-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-bottom: 1rem;
}

.typing-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(78, 205, 196, 0.2);
  color: #4ecdc4;
  border: 2px solid rgba(78, 205, 196, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.typing-content {
  background: rgba(255, 255, 255, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 1rem 1.5rem;
  border-bottom-left-radius: 5px;
}

.typing-dots {
  display: flex;
  gap: 0.3rem;
  margin-bottom: 0.3rem;
}

.typing-dot {
  width: 8px;
  height: 8px;
  background: #4ecdc4;
  border-radius: 50%;
  animation: typing-animation 1.5s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: 0s; }
.typing-dot:nth-child(2) { animation-delay: 0.2s; }
.typing-dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing-animation {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

.typing-text {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
}

/* Chat Input */
.chat-input-area {
  display: flex;
  gap: 1rem;
}

.input-wrapper {
  display: flex;
  gap: 1rem;
  width: 100%;
}

.init-trigger-button,
.send-button {
  padding: 1rem 2rem;
  border: none;
  border-radius: 15px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.init-trigger-button {
  background: linear-gradient(135deg, #4ecdc4, #667eea);
  color: white;
  flex: 1;
}

.send-button {
  background: linear-gradient(135deg, #4ecdc4, #667eea);
  color: white;
}

.init-trigger-button:hover,
.send-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.modal-content {
  background: linear-gradient(145deg, rgba(15, 15, 35, 0.95), rgba(26, 26, 46, 0.95));
  border-radius: 20px;
  border: 1px solid rgba(78, 205, 196, 0.2);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  backdrop-filter: blur(25px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.modal-header {
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
}

.modal-close {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 0.5rem;
  font-size: 1.2rem;
  transition: all 0.2s ease;
}

.modal-close:hover {
  color: white;
  transform: scale(1.1);
}

.modal-body {
  padding: 2rem;
}

.modal-footer {
  padding: 1rem 2rem 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* Form Elements */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.form-group label {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  font-size: 0.9rem;
}

.form-select,
.form-input {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 10px;
  color: white;
  font-size: 0.9rem;
  outline: none;
  transition: all 0.3s ease;
}

.form-select:focus,
.form-input:focus {
  border-color: rgba(78, 205, 196, 0.5);
  box-shadow: 0 0 20px rgba(78, 205, 196, 0.2);
}

.action-button {
  padding: 0.8rem 1.2rem;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  justify-content: center;
  font-size: 0.9rem;
}

.action-button.primary {
  background: linear-gradient(135deg, #4ecdc4, #667eea);
  color: white;
}

.action-button.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-button:hover {
  transform: translateY(-2px);
}

.action-button.primary:hover {
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
}

.action-button.secondary:hover {
  background: rgba(255, 255, 255, 0.15);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content {
    padding: 2rem 1.5rem;
    margin: 1rem;
    max-width: 90vw;
  }
  
  .hero-wrapper {
    gap: 1.5rem;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .search-wrapper {
    max-width: 400px;
  }
  
  .stats-showcase {
    gap: 1rem;
  }
  
  .stat-card {
    padding: 1.2rem 1.4rem;
    min-width: 120px;
  }
  
  .main-content {
    padding: 1rem;
  }
  
  .chat-interface {
    height: 60vh;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .stat-number {
    font-size: 1.8rem;
  }
  
  .modal-content {
    width: 95%;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1.5rem;
  }
}
</style>