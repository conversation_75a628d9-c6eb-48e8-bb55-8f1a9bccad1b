<template>
  <div class="modal-overlay" @click.self="$emit('close')">
    <div class="modal-content">
      <div class="modal-header">
        <div class="header-left">
          <div class="event-icon" :style="{ backgroundColor: eventColor }">
            <i :class="getEventIcon"></i>
          </div>
          <div>
            <h2>{{ event.title }}</h2>
            <p class="event-datetime">
              <i class="far fa-calendar"></i>
              {{ formattedDate }}
              <span class="time-separator">•</span>
              <i class="far fa-clock"></i>
              {{ formattedTime }}
            </p>
          </div>
        </div>
        <button @click="$emit('close')" class="close-button">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body">
        <div class="detail-section">
          <h3>
            <i class="fas fa-info-circle"></i>
            Detalhes
          </h3>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="detail-label">Tipo</span>
              <span class="detail-value">
                <i :class="getEventIcon"></i>
                {{ event.type }}
              </span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Prioridade</span>
              <span class="detail-value priority-badge" :data-priority="event.priority?.toLowerCase()">
                {{ event.priority }}
              </span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Disciplina</span>
              <span class="detail-value" :style="{ color: subjectColor }">
                <i class="fas fa-book"></i>
                {{ subjectName }}
              </span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Duração</span>
              <span class="detail-value">
                <i class="fas fa-hourglass-half"></i>
                {{ duration }}
              </span>
            </div>
          </div>
        </div>

        <div v-if="event.description" class="detail-section">
          <h3>
            <i class="fas fa-align-left"></i>
            Descrição
          </h3>
          <p class="description">{{ event.description }}</p>
        </div>

        <div v-if="event.progress !== undefined" class="detail-section">
          <h3>
            <i class="fas fa-chart-line"></i>
            Progresso
          </h3>
          <div class="progress-container">
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: event.progress + '%', backgroundColor: progressColor }"
              ></div>
            </div>
            <span class="progress-text">{{ event.progress }}%</span>
          </div>
        </div>

        <div class="detail-section stats-section">
          <h3>
            <i class="fas fa-chart-bar"></i>
            Estatísticas
          </h3>
          <div class="stats-grid">
            <div class="stat-card">
              <i class="fas fa-calendar-check"></i>
              <div class="stat-info">
                <span class="stat-value">{{ completedCount }}</span>
                <span class="stat-label">Revisões Completas</span>
              </div>
            </div>
            <div class="stat-card">
              <i class="fas fa-fire"></i>
              <div class="stat-info">
                <span class="stat-value">{{ streakDays }}</span>
                <span class="stat-label">Dias de Sequência</span>
              </div>
            </div>
            <div class="stat-card">
              <i class="fas fa-clock"></i>
              <div class="stat-info">
                <span class="stat-value">{{ totalHours }}h</span>
                <span class="stat-label">Tempo Total</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button @click="$emit('edit', event)" class="action-button edit-button">
          <i class="fas fa-edit"></i>
          Editar
        </button>
        <button @click="handleDelete" class="action-button delete-button">
          <i class="fas fa-trash"></i>
          Excluir
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { format, differenceInMinutes } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export default {
  name: 'EventDetail',
  props: {
    event: {
      type: Object,
      required: true
    },
    subjects: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    eventColor() {
      if (this.event.subject) {
        const subject = this.subjects.find(s => s.id === this.event.subject);
        return subject ? subject.color : '#667eea';
      }
      const colors = {
        'Alta': '#ff4757',
        'Média': '#ffa502',
        'Baixa': '#2ed573'
      };
      return colors[this.event.priority] || '#667eea';
    },
    
    getEventIcon() {
      const icons = {
        'Revisão': 'fas fa-book-reader',
        'Prova': 'fas fa-file-alt',
        'Trabalho': 'fas fa-briefcase',
        'Apresentação': 'fas fa-presentation',
        'Exercício': 'fas fa-pencil-alt'
      };
      return icons[this.event.type] || 'fas fa-calendar';
    },
    
    formattedDate() {
      return format(new Date(this.event.start), "EEEE, dd 'de' MMMM 'de' yyyy", { locale: ptBR });
    },
    
    formattedTime() {
      const start = format(new Date(this.event.start), 'HH:mm');
      const end = format(new Date(this.event.end), 'HH:mm');
      return `${start} - ${end}`;
    },
    
    duration() {
      const minutes = differenceInMinutes(new Date(this.event.end), new Date(this.event.start));
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      if (hours > 0) {
        return `${hours}h ${mins}min`;
      }
      return `${mins}min`;
    },
    
    subjectName() {
      const subject = this.subjects.find(s => s.id === this.event.subject);
      return subject ? subject.name : 'Sem disciplina';
    },
    
    subjectColor() {
      const subject = this.subjects.find(s => s.id === this.event.subject);
      return subject ? subject.color : '#667eea';
    },
    
    progressColor() {
      const progress = this.event.progress || 0;
      if (progress >= 80) return '#2ed573';
      if (progress >= 60) return '#3742fa';
      if (progress >= 40) return '#ffa502';
      return '#ff4757';
    },
    
    completedCount() {
      // Mock data - in real app, fetch from store
      return Math.floor(Math.random() * 20) + 5;
    },
    
    streakDays() {
      // Mock data - in real app, calculate from user data
      return Math.floor(Math.random() * 30) + 1;
    },
    
    totalHours() {
      // Mock data - in real app, calculate from events
      return Math.floor(Math.random() * 50) + 10;
    }
  },
  methods: {
    handleDelete() {
      if (confirm(`Deseja excluir "${this.event.title}"?`)) {
        this.$emit('delete', this.event);
        this.$emit('close');
      }
    }
  }
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background: #0f1724;
  border-radius: 16px;
  width: 90%;
  max-width: 700px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 2rem;
  background: rgba(30, 41, 59, 0.5);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex: 1;
}

.event-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.modal-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.75rem;
  color: #e4e6eb;
}

.event-datetime {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #94a3b8;
  font-size: 0.875rem;
  margin: 0;
}

.time-separator {
  color: #475569;
}

.close-button {
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  transform: scale(1.05);
}

.modal-body {
  padding: 2rem;
  overflow-y: auto;
  max-height: calc(90vh - 220px);
}

.detail-section {
  margin-bottom: 2rem;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h3 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.125rem;
  color: #e4e6eb;
  margin: 0 0 1rem 0;
}

.detail-section h3 i {
  color: #6366f1;
  font-size: 1rem;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.detail-value {
  font-size: 1rem;
  color: #e4e6eb;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.priority-badge {
  padding: 0.375rem 0.875rem;
  border-radius: 999px;
  font-size: 0.875rem;
  font-weight: 500;
  width: fit-content;
}

.priority-badge[data-priority="alta"] {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.priority-badge[data-priority="média"] {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.priority-badge[data-priority="baixa"] {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.description {
  color: #94a3b8;
  line-height: 1.6;
  margin: 0;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: rgba(148, 163, 184, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 1.125rem;
  font-weight: 600;
  color: #e4e6eb;
  min-width: 50px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 12px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.stat-card i {
  font-size: 1.5rem;
  color: #6366f1;
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #e4e6eb;
}

.stat-label {
  font-size: 0.75rem;
  color: #94a3b8;
}

.modal-footer {
  display: flex;
  gap: 1rem;
  padding: 1.5rem 2rem;
  background: rgba(30, 41, 59, 0.3);
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.action-button {
  flex: 1;
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.edit-button {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.edit-button:hover {
  background: rgba(99, 102, 241, 0.2);
  transform: translateY(-1px);
}

.delete-button {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.delete-button:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: translateY(-1px);
}

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}
</style>