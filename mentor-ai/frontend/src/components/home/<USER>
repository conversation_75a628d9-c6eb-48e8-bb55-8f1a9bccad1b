<template>
  <div class="activity-timeline">
    <div class="timeline-item" v-for="activity in activities" :key="activity.id">
      <div class="timeline-icon">
        <font-awesome-icon :icon="`fa-solid ${activity.icon}`" />
      </div>
      <div class="timeline-content">
        <h4>{{ activity.title }}</h4>
        <p>{{ activity.description }}</p>
        <span class="timeline-time">{{ activity.time }}</span>
        <router-link v-if="activity.link" :to="activity.link" class="timeline-link">
          Ver mais <font-awesome-icon icon="fa-arrow-right" />
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ActivityTimeline',
  props: {
    activities: {
      type: Array,
      default: () => []
    }
  }
};
</script>

<style scoped>
.activity-timeline {
  position: relative;
  padding-left: 3rem;
}

.activity-timeline::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--color-border);
}

.timeline-item {
  position: relative;
  margin-bottom: 2rem;
  padding-left: 1.5rem;
}

.timeline-icon {
  position: absolute;
  left: -3rem;
  width: 32px;
  height: 32px;
  background: var(--color-bg-secondary);
  border: 2px solid var(--color-border);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
}

.timeline-content {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 1.2rem;
}

.timeline-content h4 {
  margin: 0 0 0.5rem 0;
  color: var(--color-text);
}

.timeline-content p {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
}

.timeline-time {
  font-size: 0.8rem;
  color: var(--color-text-secondary);
}

.timeline-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  color: var(--color-primary);
  text-decoration: none;
  font-size: 0.9rem;
}

.timeline-link:hover {
  text-decoration: underline;
}
</style> 