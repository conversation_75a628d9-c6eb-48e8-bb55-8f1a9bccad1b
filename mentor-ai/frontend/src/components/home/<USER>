<template>
  <div class="weekly-chart">
    <h3>Progress<PERSON></h3>
    <div class="chart-placeholder">
      <p>Gráfico de progresso semanal</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WeeklyChart',
  props: {
    data: {
      type: Array,
      default: () => []
    }
  }
};
</script>

<style scoped>
.weekly-chart {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 1.5rem;
}

.weekly-chart h3 {
  margin: 0 0 1rem 0;
  color: var(--color-text);
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  color: var(--color-text-secondary);
}
</style> 