<template>
  <div class="performance-details">
    <div class="detail-card" v-for="(metric, index) in metrics" :key="index">
      <div class="detail-icon">
        <font-awesome-icon :icon="`fa-solid ${metric.icon}`" />
      </div>
      <div class="detail-info">
        <div class="detail-label">{{ metric.label }}</div>
        <div class="detail-value">{{ metric.value }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PerformanceDetails',
  props: {
    metrics: {
      type: Array,
      default: () => []
    }
  }
};
</script>

<style scoped>
.performance-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.detail-card {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 1.2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.detail-icon {
  width: 36px;
  height: 36px;
  background: rgba(66, 185, 131, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
}

.detail-label {
  font-size: 0.85rem;
  color: var(--color-text-secondary);
  margin-bottom: 0.2rem;
}

.detail-value {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--color-text);
}
</style> 