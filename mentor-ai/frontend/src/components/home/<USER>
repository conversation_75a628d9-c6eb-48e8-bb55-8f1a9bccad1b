<template>
  <div class="resources-grid">
    <div class="resource-card" v-for="resource in resources" :key="resource.id">
      <div class="resource-icon">
        <font-awesome-icon :icon="`fa-solid ${resource.icon}`" />
      </div>
      <div class="resource-content">
        <h4>{{ resource.title }}</h4>
        <p>{{ resource.description }}</p>
        <div class="resource-tags">
          <span class="tag" v-for="(tag, index) in resource.tags" :key="index">
            {{ tag }}
          </span>
        </div>
        <div class="resource-footer">
          <span class="duration">
            <font-awesome-icon icon="fa-clock" />
            {{ resource.duration }}
          </span>
          <button class="resource-link">
            Acessar <font-awesome-icon icon="fa-arrow-right" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ResourcesGrid',
  props: {
    resources: {
      type: Array,
      default: () => []
    }
  }
};
</script>

<style scoped>
.resources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
}

.resource-card {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.resource-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.resource-icon {
  width: 48px;
  height: 48px;
  background: rgba(66, 185, 131, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--color-primary);
  margin-bottom: 1rem;
}

.resource-content h4 {
  margin: 0 0 0.5rem 0;
  color: var(--color-text);
}

.resource-content p {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  line-height: 1.5;
}

.resource-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.tag {
  background: rgba(66, 185, 131, 0.1);
  color: var(--color-primary);
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  font-size: 0.8rem;
}

.resource-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.duration {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
}

.resource-link {
  background: var(--color-primary);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.resource-link:hover {
  background: var(--color-primary-dark);
  transform: translateY(-2px);
}
</style> 