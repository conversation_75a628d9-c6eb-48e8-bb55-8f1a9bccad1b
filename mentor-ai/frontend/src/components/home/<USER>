<template>
  <div class="getting-started-steps">
    <div class="steps-list">
      <div 
        class="step-item" 
        v-for="(step, index) in steps" 
        :key="step.id"
        :class="{ completed: step.completed }"
      >
        <div class="step-number">
          <font-awesome-icon v-if="step.completed" icon="fa-check" />
          <span v-else>{{ index + 1 }}</span>
        </div>
        <div class="step-content">
          <h4>{{ step.title }}</h4>
          <p>{{ step.description }}</p>
          <router-link v-if="!step.completed" :to="step.link" class="step-link">
            Começar <font-awesome-icon icon="fa-arrow-right" />
          </router-link>
        </div>
      </div>
    </div>
    <button class="skip-button" @click="$emit('skip')">
      Pular tutorial
    </button>
  </div>
</template>

<script>
export default {
  name: 'GettingStartedSteps',
  props: {
    steps: {
      type: Array,
      default: () => []
    }
  }
};
</script>

<style scoped>
.getting-started-steps {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 2rem;
}

.steps-list {
  margin-bottom: 1.5rem;
}

.step-item {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.step-item:hover {
  background: rgba(0, 0, 0, 0.1);
}

.step-number {
  width: 32px;
  height: 32px;
  background: var(--color-border);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: var(--color-text);
  flex-shrink: 0;
}

.step-item.completed .step-number {
  background: var(--color-primary);
  color: white;
}

.step-content {
  flex: 1;
}

.step-content h4 {
  margin: 0 0 0.3rem 0;
  color: var(--color-text);
}

.step-content p {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
}

.step-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-primary);
  text-decoration: none;
  font-size: 0.9rem;
}

.step-link:hover {
  text-decoration: underline;
}

.skip-button {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.skip-button:hover {
  background: rgba(0, 0, 0, 0.1);
}
</style> 