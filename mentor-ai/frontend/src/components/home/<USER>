<template>
  <div class="ai-assistant-widget">
    <div class="ai-header">
      <div class="ai-icon">
        <font-awesome-icon icon="fa-robot" />
        <span class="status-dot" :class="{ active: !loading }"></span>
      </div>
      <div class="ai-title">
        <h3>Assistente IA</h3>
        <span class="status-text">
          {{ loading ? 'Processando...' : 'Pronto para ajudar' }}
        </span>
      </div>
    </div>

    <div class="ai-display">
      <div class="message-area">
        <div class="message-content" v-if="!loading">
          <p>{{ message }}</p>
        </div>
        <div class="loading-animation" v-else>
          <div class="typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
      
      <div class="suggestions-area" v-if="suggestions.length > 0 && !loading">
        <button
          v-for="(suggestion, index) in suggestions"
          :key="index"
          class="suggestion-chip"
          @click="$emit('suggestion-click', suggestion.action)"
        >
          <span>{{ suggestion.text }}</span>
          <font-awesome-icon icon="fa-chevron-right" />
        </button>
      </div>
    </div>

    <div class="ai-interaction">
      <div class="input-wrapper">
        <input
          type="text"
          class="ai-input"
          placeholder="Pergunte algo ao assistente..."
          v-model="inputText"
          @keyup.enter="sendMessage"
        />
        <button class="send-button" @click="sendMessage">
          <font-awesome-icon icon="fa-paper-plane" />
        </button>
      </div>
    </div>

    <div class="ai-actions">
      <router-link to="/plano-estudo" class="action-link">
        <font-awesome-icon icon="fa-book" />
        <span>Plano de Estudos</span>
      </router-link>
      <router-link to="/ia/second-brain" class="action-link">
        <font-awesome-icon icon="fa-brain" />
        <span>Second Brain</span>
      </router-link>
      <router-link to="/progress-dashboard" class="action-link">
        <font-awesome-icon icon="fa-chart-simple" />
        <span>Progresso</span>
      </router-link>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue';

export default {
  name: 'AIAssistantWidget',
  props: {
    message: {
      type: String,
      required: true
    },
    suggestions: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const inputText = ref('');

    const sendMessage = () => {
      if (inputText.value.trim()) {
        emit('send-message', inputText.value);
        inputText.value = '';
      }
    };

    return {
      inputText,
      sendMessage
    };
  }
};
</script>

<style scoped>
.ai-assistant-widget {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: 16px;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.ai-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.ai-icon {
  position: relative;
  width: 48px;
  height: 48px;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #8b5cf6;
}

.status-dot {
  position: absolute;
  top: 0;
  right: 0;
  width: 12px;
  height: 12px;
  background: #ef4444;
  border-radius: 50%;
  border: 2px solid var(--color-bg-secondary);
  transform: translate(50%, -50%);
}

.status-dot.active {
  background: #10b981;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
  100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
}

.ai-title h3 {
  margin: 0;
  font-size: 1.2rem;
  color: var(--color-text);
}

.status-text {
  font-size: 0.85rem;
  color: var(--color-text-secondary);
}

.ai-display {
  margin-bottom: 1.5rem;
}

.message-area {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  padding: 1rem;
  min-height: 60px;
  display: flex;
  align-items: center;
}

.message-content p {
  margin: 0;
  color: var(--color-text);
  line-height: 1.6;
}

.loading-animation {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  background: var(--color-text-secondary);
  border-radius: 50%;
  animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-10px); }
}

.suggestions-area {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
}

.suggestion-chip {
  background: rgba(139, 92, 246, 0.1);
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #8b5cf6;
  cursor: pointer;
  transition: all 0.3s ease;
}

.suggestion-chip:hover {
  background: rgba(139, 92, 246, 0.2);
  transform: translateY(-2px);
}

.ai-interaction {
  margin-bottom: 1.5rem;
}

.input-wrapper {
  display: flex;
  gap: 0.5rem;
}

.ai-input {
  flex: 1;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  color: var(--color-text);
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.ai-input:focus {
  outline: none;
  border-color: #8b5cf6;
}

.send-button {
  background: #8b5cf6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.send-button:hover {
  background: #7c3aed;
  transform: translateY(-2px);
}

.ai-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.action-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  color: var(--color-text);
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.action-link:hover {
  background: rgba(139, 92, 246, 0.2);
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .ai-assistant-widget {
    padding: 1.5rem;
  }
  
  .ai-actions {
    flex-wrap: wrap;
  }
  
  .action-link {
    flex: 1;
    justify-content: center;
    min-width: 120px;
  }
}
</style> 