<template>
  <div class="achievements-grid">
    <div class="achievement-card" v-for="achievement in achievements" :key="achievement.id">
      <div class="achievement-icon">
        <font-awesome-icon :icon="`fa-solid ${achievement.icon}`" />
      </div>
      <div class="achievement-content">
        <h4>{{ achievement.title }}</h4>
        <p>{{ achievement.description }}</p>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: achievement.progress + '%' }"></div>
        </div>
        <span class="progress-text">{{ achievement.progress }}% completo</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AchievementsGrid',
  props: {
    achievements: {
      type: Array,
      default: () => []
    }
  }
};
</script>

<style scoped>
.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.achievement-card {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.achievement-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.achievement-icon {
  width: 48px;
  height: 48px;
  background: rgba(251, 191, 36, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #fbbf24;
  margin-bottom: 1rem;
}

.achievement-content h4 {
  margin: 0 0 0.5rem 0;
  color: var(--color-text);
}

.achievement-content p {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
}

.progress-bar {
  height: 8px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #fbbf24, #f59e0b);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.85rem;
  color: var(--color-text-secondary);
}
</style> 