<template>
  <div class="progress-stats">
    <div class="stat-card" v-for="stat in stats" :key="stat.id">
      <div class="stat-icon">
        <font-awesome-icon :icon="`fa-solid ${stat.icon}`" />
      </div>
      <div class="stat-content">
        <div class="stat-value">
          {{ stat.value }}{{ stat.suffix }}
        </div>
        <div class="stat-title">{{ stat.title }}</div>
        <div class="stat-description">{{ stat.description }}</div>
      </div>
      <div class="stat-trend" :class="stat.trendType">
        <font-awesome-icon :icon="stat.trendType === 'positive' ? 'fa-arrow-up' : 'fa-arrow-down'" />
        <span>{{ stat.trend }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProgressStats',
  props: {
    stats: {
      type: Array,
      required: true
    }
  }
};
</script>

<style scoped>
.progress-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 1.5rem;
  position: relative;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 40px;
  height: 40px;
  background: rgba(66, 185, 131, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-text);
  margin-bottom: 0.3rem;
}

.stat-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: 0.2rem;
}

.stat-description {
  font-size: 0.85rem;
  color: var(--color-text-secondary);
}

.stat-trend {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-weight: 500;
}

.stat-trend.positive {
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.stat-trend.negative {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}
</style> 