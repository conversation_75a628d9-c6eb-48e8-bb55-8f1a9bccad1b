import { createRouter, createWebHistory } from 'vue-router';
import store from '../store';
import SecondBrainView from '@/views/SecondBrainView.vue';
import FlashcardsView from '@/views/FlashcardsView.vue';
import QuestionsView from '@/views/QuestionsView.vue';

// Importações síncronas para evitar problemas de hidratação
import LoginPage from '../components/Login.vue';
import Register from '../components/Register.vue';
import HomePage from '../components/Home.vue';
import CalendarView from '../components/CalendarView.vue';
import AboutPage from '../components/About.vue';
import UltraNotesResumes from '../components/UltraNotesResumes.vue';
import StudyStatusCard from '../components/StudyStatusCard.vue';
import Profile from '@/components/Profile.vue';
import UnifiedResourcesPage from '@/components/UnifiedResourcesPage.vue';
import ProvasSimulados from '@/components/ProvasSimulados.vue';
import RevisionSchedulerUpdated from '../components/RevisionSchedulerUpdated.vue';
import Settings from '../components/Settings.vue';
import UltraCentralIA from '@/components/UltraCentralIA.vue';
import RoundAI from '@/components/RoundAI.vue';

// Importações dos novos componentes AI Tools

// Importações dos novos componentes APEX-MedQuest Pro
import ExamesView from '@/views/ExamesView.vue';

import Help from '@/components/Help.vue';
// import ProgressDashboard from '@/components/ProgressDashboard.vue';
import PerformanceView from '@/views/PerformanceView.vue';

const routes = [
  {
    path: '/login',
    name: 'LoginPage',
    component: LoginPage,
    meta: { requiresGuest: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: { requiresGuest: true }
  },
  {
    path: '/',
    name: 'HomePage',
    component: HomePage,
    meta: { requiresAuth: true }
  },
  {
    path: '/calendar',
    name: 'Calendar',
    component: CalendarView,
    meta: { requiresAuth: true }
  },
  {
    path: '/about',
    name: 'About',
    component: AboutPage,
    meta: { requiresAuth: true }
  },
  {
    path: '/resumos-notas',
    name: 'ResumosENotas',
    component: UltraNotesResumes,
    meta: {
      requiresAuth: true,
      title: 'Notas & Resumos - Sistema de Mentoria de Estudos'
    }
  },
  {
    path: '/resumos-notas/editor',
    name: 'NotesEditor',
    component: () => import('@/components/notes/NotesEditor.vue'),
    meta: {
      requiresAuth: true,
      title: 'Editor Avançado de Notas'
    }
  },
  {
    path: '/resumos-notas/ai-resumos',
    name: 'AIResumes',
    component: () => import('@/components/notes/AIResumes.vue'),
    meta: {
      requiresAuth: true,
      title: 'Gerador de Resumos com IA'
    }
  },
  {
    path: '/resumos-notas/mapas-mentais',
    name: 'MindMaps',
    component: () => import('@/components/notes/MindMaps.vue'),
    meta: {
      requiresAuth: true,
      title: 'Mapas Mentais Interativos'
    }
  },
  {
    path: '/resumos-notas/templates',
    name: 'NotesTemplates',
    component: () => import('@/components/notes/NotesTemplates.vue'),
    meta: {
      requiresAuth: true,
      title: 'Biblioteca de Templates'
    }
  },
  {
    path: '/resumos-notas/insights',
    name: 'NotesInsights',
    component: () => import('@/components/notes/NotesInsights.vue'),
    meta: {
      requiresAuth: true,
      title: 'Análise e Insights'
    }
  },
  {
    path: '/resumos-notas/colaboracao',
    name: 'NotesCollaboration',
    component: () => import('@/components/notes/NotesCollaboration.vue'),
    meta: {
      requiresAuth: true,
      title: 'Colaboração e Compartilhamento'
    }
  },
  {
    path: '/resumos-notas/organizador',
    name: 'NotesOrganizer',
    component: () => import('@/components/notes/NotesOrganizer.vue'),
    meta: {
      requiresAuth: true,
      title: 'Organizador Inteligente de Notas'
    }
  },
  {
    path: '/resumos-notas/exportar',
    name: 'NotesExport',
    component: () => import('@/components/notes/NotesExport.vue'),
    meta: {
      requiresAuth: true,
      title: 'Central de Exportação de Notas'
    }
  },
  {
    path: '/resumos-notas/historico',
    name: 'NotesRevisionHistory',
    component: () => import('@/components/notes/NotesRevisionHistory.vue'),
    meta: {
      requiresAuth: true,
      title: 'Histórico de Revisões'
    }
  },
  {
    path: '/resumos-notas/ia-avancada',
    name: 'NotesAIEnhanced',
    component: () => import('@/components/notes/NotesAIEnhanced.vue'),
    meta: {
      requiresAuth: true,
      title: 'IA Avançada para Notas'
    }
  },
  {
    path: '/recursos',
    name: 'Resources',
    component: UnifiedResourcesPage,
    meta: { 
      requiresAuth: true,
      title: 'Biblioteca de Recursos - Materiais de Estudo'
    }
  },
  {
    path: '/recursos/:id',
    name: 'ResourceDetails',
    component: () => import('../components/ResourceDetails.vue')
  },
  {
    path: '/study-status',
    name: 'StudyStatusCard',
    component: StudyStatusCard,
    meta: { requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: {
      requiresAuth: true
    }
  },
  {
    path: '/provas-simulados',
    name: 'ProvasSimulados',
    component: ProvasSimulados,
    meta: { requiresAuth: true }
  },
  {
    path: '/progress-dashboard',
    name: 'ProgressDashboard',
    component: PerformanceView,
    meta: { 
      requiresAuth: true, 
      title: 'Dashboard de Desempenho' 
    }
  },
  {
    path: '/revision-scheduler',
    name: 'RevisionScheduler',
    component: RevisionSchedulerUpdated,
    meta: { 
      requiresAuth: true,
      title: 'Sistema de Revisões Espaçadas - Metodologia Científica'
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    meta: { requiresAuth: true }
  },
  {
    path: '/help',
    name: 'Help',
    component: Help,
    meta: { 
      requiresAuth: true,
      title: 'Central de Ajuda - Sophos Academy'
    }
  },
  {
    path: '/ia',
    name: 'AIMenu',
    component: UltraCentralIA,
    meta: { 
      requiresAuth: true,
      title: 'Engines Central - Neural Command Center'
    }
  },
  {
    path: '/ia/second-brain',
    name: 'SecondBrainIA',
    component: SecondBrainView,
    meta: { requiresAuth: true }
  },
  {
    path: '/ia/round-ai',
    name: 'RoundAI',
    component: RoundAI,
    meta: { requiresAuth: true }
  },
  {
    path: '/ia/flashcards',
    name: 'FlashcardsAI',
    component: FlashcardsView,
    meta: { requiresAuth: true }
  },
  {
    path: '/ia/questions',
    name: 'QuestionsAI',
    component: QuestionsView,
    meta: { requiresAuth: true }
  },
  {
    path: '/exames',
    name: 'Exames',
    component: ExamesView,
    meta: { requiresAuth: true }
  },
  {
    path: '/neural-visualizer',
    name: 'NeuralVisualizer',
    component: () => import('@/components/NeuralRetentionVisualizer.vue'),
    meta: {
      requiresAuth: true
    }
  },
  // Thanos AI removido temporariamente
  // {
  //   path: '/thanos',
  //   name: 'Thanos',
  //   component: Thanos,
  //   meta: {
  //     requiresAuth: true,
  //     title: 'Oracle - Assistente de Estudos Médicos com IA'
  //   }
  // },
  // Thanos Ultra removido temporariamente
  // {
  //   path: '/thanos-ultra',
  //   name: 'ThanosUltra',
  //   component: () => import('../components/ThanosUltra.vue'),
  //   meta: { 
  //     requiresAuth: true,
  //     title: 'Thanos Ultra - Advanced Document Processing & AI'
  //   }
  // },
  {
    path: '/pomodoro',
    name: 'Pomodoro',
    component: () => import('../views/PomodoroView.vue'),
    meta: { 
      requiresAuth: true,
      title: 'Ultra Pomodoro Timer - Maximize sua Produtividade'
    }
  },
  {
    path: '/plano-estudo',
    name: 'StudyPlan',
    component: () => import('../components/StudyPlanPageFinal.vue'),
    meta: { 
      requiresAuth: true,
      title: 'Plano de Estudo - Organize sua Jornada de Aprendizado'
    }
  },
  {
    path: '/plano-estudo/novo',
    name: 'StudyPlanCreate',
    component: () => import('../components/StudyPlanPage.vue'),
    meta: { 
      requiresAuth: true,
      title: 'Criar Novo Plano de Estudo'
    }
  },
  { path: '/:pathMatch(.*)*', redirect: '/' }
];

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
});

// Guarda de navegação simplificada
router.beforeEach((to, from, next) => {
  const isAuthenticated = store.getters['auth/isAuthenticated'];

  if (to.meta.requiresAuth && !isAuthenticated) {
    next('/login');
  } else if (to.meta.requiresGuest && isAuthenticated) {
    next('/');
  } else {
    next();
  }
});

export default router;
