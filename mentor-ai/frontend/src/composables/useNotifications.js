import { ref, computed } from 'vue'
import { useStore } from 'vuex'

/**
 * Composable para gerenciar notificações do sistema
 * Suporta notificações em tempo real, agendadas e personalizadas
 */
export function useNotifications() {
  const store = useStore()
  
  // Estado local
  const state = ref({
    notifications: [],
    settings: {
      enabled: true,
      sound: true,
      desktop: true,
      smartScheduling: true,
      doNotDisturb: {
        enabled: false,
        startTime: '22:00',
        endTime: '08:00'
      }
    },
    isInitialized: false
  })

  /**
   * Inicializa o sistema de notificações
   */
  const initialize = async () => {
    if (state.value.isInitialized) return
    
    try {
      // Carregar configurações do usuário
      // Em um ambiente real, isso viria do backend
      const userSettings = {
        enabled: true,
        sound: true,
        desktop: true,
        smartScheduling: true,
        doNotDisturb: {
          enabled: false,
          startTime: '22:00',
          endTime: '08:00'
        }
      }
      
      state.value.settings = userSettings
      
      // Verificar permissões para notificações desktop
      if (state.value.settings.desktop) {
        await requestNotificationPermission()
      }
      
      // Carregar notificações pendentes
      // Em um ambiente real, isso viria do backend
      const pendingNotifications = [
        {
          id: 'notification-1',
          title: 'Revisão Agendada',
          body: 'Você tem uma revisão de Anatomia agendada para hoje',
          type: 'reminder',
          read: false,
          createdAt: new Date(Date.now() - 1000 * 60 * 60),
          scheduledFor: new Date(Date.now() + 1000 * 60 * 60)
        },
        {
          id: 'notification-2',
          title: 'Nova Conquista',
          body: 'Parabéns! Você completou 7 dias de estudo consecutivos',
          type: 'achievement',
          read: false,
          createdAt: new Date(Date.now() - 1000 * 60 * 30)
        }
      ]
      
      state.value.notifications = pendingNotifications
      state.value.isInitialized = true
      
      // Iniciar verificação periódica de notificações agendadas
      startNotificationChecker()
    } catch (error) {
      console.error('Erro ao inicializar notificações:', error)
    }
  }

  /**
   * Solicita permissão para notificações desktop
   * @returns {boolean} Verdadeiro se permissão concedida
   */
  const requestNotificationPermission = async () => {
    if (!('Notification' in window)) {
      console.log('Este navegador não suporta notificações desktop')
      return false
    }
    
    if (Notification.permission === 'granted') {
      return true
    }
    
    if (Notification.permission !== 'denied') {
      const permission = await Notification.requestPermission()
      return permission === 'granted'
    }
    
    return false
  }

  /**
   * Inicia verificador periódico de notificações agendadas
   */
  const startNotificationChecker = () => {
    // Verificar a cada minuto
    setInterval(() => {
      checkScheduledNotifications()
    }, 60000)
    
    // Verificar imediatamente
    checkScheduledNotifications()
  }

  /**
   * Verifica notificações agendadas para exibição
   */
  const checkScheduledNotifications = () => {
    const now = new Date()
    
    state.value.notifications.forEach(notification => {
      if (
        notification.scheduledFor && 
        !notification.displayed && 
        new Date(notification.scheduledFor) <= now
      ) {
        showNotification(notification)
        notification.displayed = true
      }
    })
  }

  /**
   * Exibe uma notificação
   * @param {Object} notification Notificação a ser exibida
   */
  const showNotification = (notification) => {
    // Verificar se notificações estão habilitadas
    if (!state.value.settings.enabled) return
    
    // Verificar modo não perturbe
    if (isInDoNotDisturbPeriod()) {
      // Agendar para depois do período de não perturbe
      scheduleAfterDoNotDisturb(notification)
      return
    }
    
    // Notificação desktop
    if (state.value.settings.desktop && Notification.permission === 'granted') {
      const desktopNotification = new Notification(notification.title, {
        body: notification.body,
        icon: getNotificationIcon(notification.type)
      })
      
      desktopNotification.onclick = () => {
        window.focus()
        markAsRead(notification.id)
      }
    }
    
    // Notificação in-app
    // Implementar lógica para exibir notificação na interface
    
    // Som de notificação
    if (state.value.settings.sound) {
      playNotificationSound(notification.type)
    }
  }

  /**
   * Agenda uma notificação
   * @param {Object} notification Notificação a ser agendada
   */
  const scheduleNotification = (notification) => {
    // Gerar ID único
    const id = `notification-${Date.now()}-${Math.floor(Math.random() * 1000)}`
    
    // Criar objeto de notificação
    const newNotification = {
      id,
      title: notification.title,
      body: notification.body,
      type: notification.type || 'general',
      read: false,
      createdAt: new Date(),
      scheduledFor: notification.scheduledFor || new Date(),
      displayed: false,
      data: notification.data || {}
    }
    
    // Adicionar à lista
    state.value.notifications.push(newNotification)
    
    // Em um ambiente real, salvaríamos no backend
    // await store.dispatch('notifications/addNotification', newNotification)
    
    // Verificar se deve ser exibida imediatamente
    if (!notification.scheduledFor || new Date(notification.scheduledFor) <= new Date()) {
      showNotification(newNotification)
      newNotification.displayed = true
    }
    
    return id
  }

  /**
   * Marca uma notificação como lida
   * @param {string} id ID da notificação
   */
  const markAsRead = (id) => {
    const notification = state.value.notifications.find(n => n.id === id)
    
    if (notification) {
      notification.read = true
      
      // Em um ambiente real, salvaríamos no backend
      // await store.dispatch('notifications/markAsRead', id)
    }
  }

  /**
   * Marca todas as notificações como lidas
   */
  const markAllAsRead = () => {
    state.value.notifications.forEach(notification => {
      notification.read = true
    })
    
    // Em um ambiente real, salvaríamos no backend
    // await store.dispatch('notifications/markAllAsRead')
  }

  /**
   * Remove uma notificação
   * @param {string} id ID da notificação
   */
  const removeNotification = (id) => {
    const index = state.value.notifications.findIndex(n => n.id === id)
    
    if (index !== -1) {
      state.value.notifications.splice(index, 1)
      
      // Em um ambiente real, salvaríamos no backend
      // await store.dispatch('notifications/removeNotification', id)
    }
  }

  /**
   * Atualiza as configurações de notificação
   * @param {Object} settings Novas configurações
   */
  const updateSettings = (settings) => {
    state.value.settings = { ...state.value.settings, ...settings }
    
    // Em um ambiente real, salvaríamos no backend
    // await store.dispatch('notifications/updateSettings', state.value.settings)
    
    // Se notificações desktop foram habilitadas, solicitar permissão
    if (settings.desktop && !settings.desktop !== state.value.settings.desktop) {
      requestNotificationPermission()
    }
  }

  /**
   * Verifica se o momento atual está no período de não perturbe
   * @returns {boolean} Verdadeiro se estiver no período de não perturbe
   */
  const isInDoNotDisturbPeriod = () => {
    const dnd = state.value.settings.doNotDisturb
    
    if (!dnd.enabled) return false
    
    const now = new Date()
    const currentHour = now.getHours()
    const currentMinute = now.getMinutes()
    
    const startParts = dnd.startTime.split(':').map(Number)
    const endParts = dnd.endTime.split(':').map(Number)
    
    const startHour = startParts[0]
    const startMinute = startParts[1] || 0
    const endHour = endParts[0]
    const endMinute = endParts[1] || 0
    
    // Converter para minutos desde meia-noite
    const currentTime = currentHour * 60 + currentMinute
    const startTime = startHour * 60 + startMinute
    const endTime = endHour * 60 + endMinute
    
    if (startTime <= endTime) {
      // Período não cruza meia-noite
      return currentTime >= startTime && currentTime <= endTime
    } else {
      // Período cruza meia-noite
      return currentTime >= startTime || currentTime <= endTime
    }
  }

  /**
   * Agenda uma notificação para após o período de não perturbe
   * @param {Object} notification Notificação a ser agendada
   */
  const scheduleAfterDoNotDisturb = (notification) => {
    const dnd = state.value.settings.doNotDisturb
    const endParts = dnd.endTime.split(':').map(Number)
    
    const endHour = endParts[0]
    const endMinute = endParts[1] || 0
    
    const now = new Date()
    const scheduledTime = new Date(now)
    
    scheduledTime.setHours(endHour)
    scheduledTime.setMinutes(endMinute)
    
    // Se o horário de término já passou hoje, agendar para amanhã
    if (scheduledTime <= now) {
      scheduledTime.setDate(scheduledTime.getDate() + 1)
    }
    
    notification.scheduledFor = scheduledTime
  }

  /**
   * Obtém o ícone para um tipo de notificação
   * @param {string} type Tipo de notificação
   * @returns {string} URL do ícone
   */
  const getNotificationIcon = (type) => {
    const icons = {
      reminder: '/icons/reminder.png',
      achievement: '/icons/achievement.png',
      system: '/icons/system.png',
      general: '/icons/notification.png'
    }
    
    return icons[type] || icons.general
  }

  /**
   * Reproduz som de notificação
   * @param {string} type Tipo de notificação
   */
  const playNotificationSound = (type) => {
    const sounds = {
      reminder: '/sounds/reminder.mp3',
      achievement: '/sounds/achievement.mp3',
      system: '/sounds/system.mp3',
      general: '/sounds/notification.mp3'
    }
    
    const sound = new Audio(sounds[type] || sounds.general)
    sound.play().catch(error => {
      console.log('Erro ao reproduzir som de notificação:', error)
    })
  }

  // Computed properties
  const unreadCount = computed(() => {
    return state.value.notifications.filter(n => !n.read).length
  })

  const recentNotifications = computed(() => {
    return state.value.notifications
      .filter(n => !n.scheduledFor || n.displayed)
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, 5)
  })

  return {
    // Métodos
    initialize,
    scheduleNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    updateSettings,
    
    // Computed
    unreadCount,
    recentNotifications,
    
    // Estado
    notifications: computed(() => state.value.notifications),
    settings: computed(() => state.value.settings)
  }
}