import { ref, computed } from 'vue'
import { useStore } from 'vuex'

/**
 * Composable para funcionalidades de gamificação
 * Gerencia níveis, conquistas, XP e outras mecânicas de gamificação
 */
export function useGamification() {
  const store = useStore()
  
  // Estado local
  const state = ref({
    userLevel: 1,
    currentXP: 0,
    nextLevelXP: 100,
    achievements: [],
    streak: 0,
    bestStreak: 0,
    lastXPGain: 0,
    challenges: []
  })

  // Níveis e requisitos de XP
  const levelRequirements = [
    0,      // Nível 1
    100,    // Nível 2
    250,    // Nível 3
    500,    // Nível 4
    1000,   // Nível 5
    1750,   // Nível 6
    2750,   // Nível 7
    4000,   // Nível 8
    5500,   // Nível 9
    7500,   // Nível 10
    10000,  // Nível 11
    13000,  // Nível 12
    16500,  // Nível 13
    20500,  // Nível 14
    25000   // Nível 15
  ]

  /**
   * Inicializa os dados do usuário
   * Em um ambiente real, isso buscaria dados do backend
   */
  const initializeUserData = async () => {
    try {
      // Simular busca de dados do backend
      const userData = {
        level: 5,
        xp: 1200,
        achievements: [
          {
            id: 'first_study',
            title: 'Primeiro Estudo',
            description: 'Complete seu primeiro estudo',
            unlocked: true,
            date: new Date(Date.now() - 1000 * 60 * 60 * 24 * 10),
            icon: 'book',
            rarity: 'common'
          },
          {
            id: 'perfect_score',
            title: 'Nota Perfeita',
            description: 'Obtenha 100% em uma revisão',
            unlocked: true,
            date: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3),
            icon: 'star',
            rarity: 'rare'
          },
          {
            id: 'study_streak',
            title: 'Sequência de 7 dias',
            description: 'Estude por 7 dias consecutivos',
            unlocked: true,
            date: new Date(),
            icon: 'fire',
            rarity: 'rare',
            isNew: true
          }
        ],
        streak: 7,
        bestStreak: 15
      }
      
      // Atualizar estado local
      state.value.userLevel = userData.level
      state.value.currentXP = userData.xp
      state.value.nextLevelXP = levelRequirements[userData.level]
      state.value.achievements = userData.achievements
      state.value.streak = userData.streak
      state.value.bestStreak = userData.bestStreak
      
      // Inicializar desafios diários
      initializeDailyChallenges()
      
      return userData
    } catch (error) {
      console.error('Erro ao inicializar dados do usuário:', error)
      return null
    }
  }

  /**
   * Inicializa desafios diários
   */
  const initializeDailyChallenges = () => {
    state.value.challenges = [
      {
        id: 'daily_reviews',
        title: 'Complete 5 revisões',
        description: 'Complete 5 revisões hoje',
        current: 3,
        target: 5,
        reward: 50,
        icon: 'tasks',
        completed: false
      },
      {
        id: 'perfect_review',
        title: 'Revisão Perfeita',
        description: 'Complete uma revisão com 100% de acerto',
        current: 0,
        target: 1,
        reward: 75,
        icon: 'trophy',
        completed: false
      },
      {
        id: 'study_time',
        title: 'Tempo de Estudo',
        description: 'Estude por pelo menos 60 minutos hoje',
        current: 45,
        target: 60,
        reward: 30,
        icon: 'clock',
        completed: false
      }
    ]
  }

  /**
   * Atualiza a quantidade de XP do usuário
   * @param {number} amount Quantidade de XP a adicionar
   * @returns {Object} Informações sobre o ganho de XP e level up
   */
  const updateXP = (amount) => {
    if (amount <= 0) return { success: false }
    
    const previousLevel = state.value.userLevel
    state.value.currentXP += amount
    state.value.lastXPGain = amount
    
    // Verificar se subiu de nível
    let leveledUp = false
    while (
      state.value.userLevel < levelRequirements.length && 
      state.value.currentXP >= levelRequirements[state.value.userLevel]
    ) {
      state.value.userLevel++
      leveledUp = true
    }
    
    // Atualizar XP para próximo nível
    if (state.value.userLevel < levelRequirements.length) {
      state.value.nextLevelXP = levelRequirements[state.value.userLevel]
    } else {
      state.value.nextLevelXP = state.value.currentXP + 10000 // Para níveis além do definido
    }
    
    // Em um ambiente real, salvaríamos isso no backend
    // await store.dispatch('user/updateXP', { xp: state.value.currentXP, level: state.value.userLevel })
    
    return {
      success: true,
      amount,
      leveledUp,
      previousLevel,
      newLevel: state.value.userLevel
    }
  }

  /**
   * Verifica conquistas e desbloqueia novas
   * @param {string} action Ação que pode desbloquear conquistas
   * @returns {Array} Conquistas desbloqueadas
   */
  const checkAchievements = (action) => {
    const unlockedAchievements = []
    
    // Definição de todas as conquistas possíveis
    const allAchievements = [
      {
        id: 'first_study',
        title: 'Primeiro Estudo',
        description: 'Complete seu primeiro estudo',
        condition: (a) => a === 'theoretical_study',
        icon: 'book',
        rarity: 'common',
        reward: 50
      },
      {
        id: 'study_streak_7',
        title: 'Sequência de 7 dias',
        description: 'Estude por 7 dias consecutivos',
        condition: (a) => a === 'login' && state.value.streak >= 7,
        icon: 'fire',
        rarity: 'rare',
        reward: 100
      },
      {
        id: 'perfect_score',
        title: 'Nota Perfeita',
        description: 'Obtenha 100% em uma revisão',
        condition: (a) => a === 'high_performance' && a.score === 100,
        icon: 'star',
        rarity: 'rare',
        reward: 150
      },
      {
        id: 'knowledge_master',
        title: 'Mestre do Conhecimento',
        description: 'Atinja o nível 10',
        condition: (a) => a === 'level_up' && state.value.userLevel >= 10,
        icon: 'crown',
        rarity: 'epic',
        reward: 500
      }
    ]
    
    // Verificar conquistas que podem ser desbloqueadas
    allAchievements.forEach(achievement => {
      // Verificar se já está desbloqueada
      const alreadyUnlocked = state.value.achievements.some(a => a.id === achievement.id && a.unlocked)
      
      // Se não estiver desbloqueada e a condição for satisfeita
      if (!alreadyUnlocked && achievement.condition(action)) {
        // Adicionar conquista desbloqueada
        const newAchievement = {
          ...achievement,
          unlocked: true,
          date: new Date(),
          isNew: true
        }
        
        state.value.achievements.push(newAchievement)
        unlockedAchievements.push(newAchievement)
        
        // Adicionar XP da recompensa
        if (achievement.reward) {
          updateXP(achievement.reward)
        }
      }
    })
    
    // Em um ambiente real, salvaríamos isso no backend
    // if (unlockedAchievements.length > 0) {
    //   await store.dispatch('user/updateAchievements', state.value.achievements)
    // }
    
    return unlockedAchievements
  }

  /**
   * Atualiza o streak de dias consecutivos
   * @param {boolean} studied Indica se o usuário estudou hoje
   */
  const updateStreak = (studied = true) => {
    if (studied) {
      state.value.streak++
      
      // Atualizar melhor streak se necessário
      if (state.value.streak > state.value.bestStreak) {
        state.value.bestStreak = state.value.streak
      }
      
      // Verificar conquistas relacionadas a streaks
      checkAchievements('login')
    } else {
      // Resetar streak se não estudou
      state.value.streak = 0
    }
    
    // Em um ambiente real, salvaríamos isso no backend
    // await store.dispatch('user/updateStreak', { 
    //   streak: state.value.streak, 
    //   bestStreak: state.value.bestStreak 
    // })
  }

  /**
   * Atualiza o progresso de um desafio diário
   * @param {string} challengeId ID do desafio
   * @param {number} progress Progresso a adicionar
   */
  const updateChallenge = (challengeId, progress = 1) => {
    const challenge = state.value.challenges.find(c => c.id === challengeId)
    
    if (challenge && !challenge.completed) {
      challenge.current += progress
      
      // Verificar se completou o desafio
      if (challenge.current >= challenge.target) {
        challenge.completed = true
        challenge.current = challenge.target
        
        // Adicionar XP da recompensa
        if (challenge.reward) {
          updateXP(challenge.reward)
        }
      }
      
      // Em um ambiente real, salvaríamos isso no backend
      // await store.dispatch('user/updateChallenges', state.value.challenges)
    }
  }

  /**
   * Retorna o título do nível atual
   */
  const levelTitle = computed(() => {
    const titles = [
      'Iniciante',
      'Aprendiz',
      'Estudante',
      'Estudante Dedicado',
      'Estudante Avançado',
      'Especialista',
      'Mestre',
      'Sábio',
      'Erudito',
      'Gênio',
      'Iluminado'
    ]
    
    const index = Math.min(Math.floor(state.value.userLevel / 2), titles.length - 1)
    return titles[index]
  })

  /**
   * Calcula a porcentagem de progresso para o próximo nível
   */
  const xpProgress = computed(() => {
    const currentLevelXP = state.value.userLevel > 1 
      ? levelRequirements[state.value.userLevel - 1] 
      : 0
    
    const xpForNextLevel = state.value.nextLevelXP - currentLevelXP
    const xpProgress = state.value.currentXP - currentLevelXP
    
    return Math.round((xpProgress / xpForNextLevel) * 100)
  })

  return {
    // Métodos
    initializeUserData,
    updateXP,
    checkAchievements,
    updateStreak,
    updateChallenge,
    
    // Computed
    levelTitle,
    xpProgress,
    
    // Estado
    userLevel: computed(() => state.value.userLevel),
    currentXP: computed(() => state.value.currentXP),
    nextLevelXP: computed(() => state.value.nextLevelXP),
    achievements: computed(() => state.value.achievements),
    streak: computed(() => state.value.streak),
    bestStreak: computed(() => state.value.bestStreak),
    lastXPGain: computed(() => state.value.lastXPGain),
    challenges: computed(() => state.value.challenges)
  }
}