import { ref, computed } from 'vue'

/**
 * Composable para funcionalidades de assistente de IA
 * Fornece análises, recomendações e sugestões personalizadas
 */
export function useAIAssistant() {
  // Estado do assistente
  const state = ref({
    isProcessing: false,
    lastQuery: '',
    lastResponse: '',
    userPreferences: {
      learningStyle: 'visual',
      studyGoals: [],
      preferredSubjects: []
    },
    suggestions: []
  })

  /**
   * Gera uma sugestão baseada no contexto do usuário
   * @param {Object} context Contexto do usuário
   * @param {Array} context.recentActivity Atividades recentes do usuário
   * @param {Object} context.performance Dados de desempenho
   * @param {Object} context.schedule Agenda atual
   * @returns {Object} Sugestão personalizada
   */
  const getAISuggestion = async (context = {}) => {
    state.value.isProcessing = true
    
    try {
      // Simulação de processamento de IA
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const { recentActivity = [], performance = {}, schedule = {} } = context
      
      // Análise de padrões de estudo
      const suggestions = []
      
      // Verificar baixo desempenho em alguma matéria
      if (performance.lowPerformanceSubjects?.length > 0) {
        const subject = performance.lowPerformanceSubjects[0]
        suggestions.push({
          id: 'low_performance',
          type: 'review',
          priority: 'high',
          text: `Revisar ${subject.name} para melhorar desempenho`,
          action: 'review',
          actionParams: { subjectId: subject.id }
        })
      }
      
      // Verificar revisões atrasadas
      if (schedule.overdueReviews?.length > 0) {
        suggestions.push({
          id: 'overdue',
          type: 'review',
          priority: 'high',
          text: 'Completar revisões atrasadas',
          action: 'review_overdue'
        })
      }
      
      // Sugerir pausa se estiver estudando há muito tempo
      const studyTimeToday = recentActivity
        .filter(a => a.type === 'study' && isToday(new Date(a.timestamp)))
        .reduce((total, a) => total + a.duration, 0)
      
      if (studyTimeToday > 120) { // Mais de 2 horas
        suggestions.push({
          id: 'take_break',
          type: 'wellness',
          priority: 'medium',
          text: 'Fazer uma pausa de 15 minutos',
          action: 'schedule_break',
          actionParams: { duration: 15 }
        })
      }
      
      // Atualizar estado
      state.value.suggestions = suggestions
      state.value.isProcessing = false
      
      return suggestions.length > 0 ? suggestions[0] : null
    } catch (error) {
      console.error('Erro ao gerar sugestão de IA:', error)
      state.value.isProcessing = false
      return null
    }
  }

  /**
   * Analisa o desempenho do usuário e fornece insights
   * @param {Object} performanceData Dados de desempenho
   * @param {number} performanceData.score Pontuação (0-100)
   * @param {number} performanceData.previousScore Pontuação anterior
   * @param {Array} performanceData.answers Respostas do usuário
   * @returns {Object} Análise de desempenho
   */
  const analyzePerformance = (performanceData) => {
    const { score = 0, previousScore = 0, answers = [] } = performanceData
    
    // Calcular mudança percentual
    const percentChange = previousScore > 0 
      ? ((score - previousScore) / previousScore) * 100 
      : 0
    
    // Determinar nível de desempenho
    let level = 'poor'
    if (score >= 90) level = 'excellent'
    else if (score >= 75) level = 'good'
    else if (score >= 60) level = 'fair'
    
    // Identificar padrões de erro
    const errorPatterns = []
    if (answers.length > 0) {
      const incorrectAnswers = answers.filter(a => !a.correct)
      
      // Agrupar por categoria
      const errorsByCategory = incorrectAnswers.reduce((acc, answer) => {
        const category = answer.category || 'uncategorized'
        acc[category] = (acc[category] || 0) + 1
        return acc
      }, {})
      
      // Encontrar categoria com mais erros
      let maxErrors = 0
      let weakestCategory = ''
      
      Object.entries(errorsByCategory).forEach(([category, count]) => {
        if (count > maxErrors) {
          maxErrors = count
          weakestCategory = category
        }
      })
      
      if (weakestCategory) {
        errorPatterns.push({
          category: weakestCategory,
          count: maxErrors,
          percentage: Math.round((maxErrors / incorrectAnswers.length) * 100)
        })
      }
    }
    
    // Gerar mensagem de feedback
    let feedback = ''
    if (level === 'excellent') {
      feedback = 'Excelente desempenho! Continue assim.'
    } else if (level === 'good') {
      feedback = 'Bom trabalho! Você está no caminho certo.'
    } else if (level === 'fair') {
      feedback = 'Desempenho razoável. Foque nas áreas de dificuldade.'
    } else {
      feedback = 'Você precisa revisar este material com mais atenção.'
    }
    
    // Adicionar recomendação específica se houver padrão de erro
    if (errorPatterns.length > 0) {
      const weakCategory = errorPatterns[0].category
      feedback += ` Recomendamos focar em "${weakCategory}" onde você teve mais dificuldades.`
    }
    
    return {
      score,
      level,
      change: Math.round(percentChange),
      trend: percentChange >= 0 ? 'up' : 'down',
      errorPatterns,
      feedback,
      nextReviewRecommendation: calculateNextReviewRecommendation(score)
    }
  }

  /**
   * Calcula recomendação para próxima revisão baseada na pontuação
   * @param {number} score Pontuação (0-100)
   * @returns {Object} Recomendação de próxima revisão
   */
  const calculateNextReviewRecommendation = (score) => {
    let days = 1
    
    if (score >= 90) days = 7
    else if (score >= 80) days = 5
    else if (score >= 70) days = 3
    else if (score >= 60) days = 2
    
    const nextDate = new Date()
    nextDate.setDate(nextDate.getDate() + days)
    
    return {
      days,
      date: nextDate,
      formatted: nextDate.toLocaleDateString('pt-BR')
    }
  }

  /**
   * Verifica se uma data é hoje
   * @param {Date} date Data a verificar
   * @returns {boolean} Verdadeiro se for hoje
   */
  const isToday = (date) => {
    const today = new Date()
    return date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
  }

  /**
   * Atualiza as preferências do usuário
   * @param {Object} preferences Novas preferências
   */
  const updateUserPreferences = (preferences) => {
    state.value.userPreferences = { 
      ...state.value.userPreferences, 
      ...preferences 
    }
  }

  return {
    getAISuggestion,
    analyzePerformance,
    updateUserPreferences,
    isProcessing: computed(() => state.value.isProcessing),
    suggestions: computed(() => state.value.suggestions)
  }
}