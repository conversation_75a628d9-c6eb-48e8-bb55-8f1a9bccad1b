import { ref, computed } from 'vue'

/**
 * Composable para algoritmos de repetição espaçada
 * Implementa o algoritmo SM-2 modificado com fatores contextuais
 */
export function useSpacingAlgorithm() {
  // Configurações padrão
  const settings = ref({
    difficultyFactor: 2.5,
    intervalMultiplier: 1.0,
    minInterval: 1,
    maxInterval: 365
  })

  /**
   * Calcula o próximo intervalo de revisão baseado no desempenho e fatores contextuais
   * @param {Object} params Parâmetros para cálculo do intervalo
   * @param {number} params.previousInterval Intervalo anterior em dias
   * @param {number} params.performance Desempenho na revisão (0-100)
   * @param {number} params.complexity Complexidade do material (1-5)
   * @param {string} params.mood Estado emocional ('excellent', 'good', 'neutral', 'tired', 'stressed')
   * @param {number} params.energy Nível de energia (1-5)
   * @returns {number} Próximo intervalo em dias
   */
  const calculateNextInterval = (params) => {
    const { 
      previousInterval = 1, 
      performance = 80, 
      complexity = 3, 
      mood = 'neutral', 
      energy = 3 
    } = params
    
    // Convertendo performance para escala 0-1
    const performanceFactor = performance / 100
    
    // Fator de dificuldade baseado na complexidade (1-5)
    const difficultyModifier = 1 - ((complexity - 1) / 10)
    
    // Fator de estado emocional
    const moodFactors = {
      'excellent': 1.2,
      'good': 1.1,
      'neutral': 1.0,
      'tired': 0.9,
      'stressed': 0.8
    }
    const moodModifier = moodFactors[mood] || 1.0
    
    // Fator de energia
    const energyModifier = 0.8 + (energy * 0.05)
    
    // Cálculo do fator de facilidade (EF)
    const ef = 2.5 + (0.1 - (5 - 5 * performanceFactor) * (0.08 + (5 - 5 * performanceFactor) * 0.02))
    
    // Aplicando todos os modificadores
    const nextInterval = Math.round(
      previousInterval * ef * difficultyModifier * moodModifier * energyModifier * settings.value.intervalMultiplier
    )
    
    // Garantindo que o intervalo esteja dentro dos limites
    return Math.min(
      Math.max(nextInterval, settings.value.minInterval),
      settings.value.maxInterval
    )
  }

  /**
   * Gera pontos para uma curva de retenção de memória baseada no modelo de Ebbinghaus
   * @param {number} days Número de dias para gerar a curva
   * @param {number} complexity Complexidade do material (1-5)
   * @returns {Array} Array de pontos [x, y] para a curva
   */
  const getRetentionCurve = (days = 30, complexity = 3) => {
    const points = []
    const strength = 1 - ((complexity - 1) / 5) // Força inicial da memória (0.8 - 1.0)
    
    for (let i = 0; i <= days; i++) {
      // Modelo de Ebbinghaus simplificado: R = e^(-t/S)
      // onde R é retenção, t é tempo, S é força da memória
      const retention = Math.exp(-i / (strength * 10)) * 100
      points.push([i, Math.round(retention)])
    }
    
    return points
  }

  /**
   * Atualiza as configurações do algoritmo
   * @param {Object} newSettings Novas configurações
   */
  const updateSettings = (newSettings) => {
    settings.value = { ...settings.value, ...newSettings }
  }

  return {
    calculateNextInterval,
    getRetentionCurve,
    updateSettings,
    settings
  }
}