{"name": "mentor-ai-frontend-ultra", "version": "4.0.0", "description": "MentorAI Frontend Ultra Robust - Titan Final Edition", "private": true, "scripts": {"serve": "vue-cli-service serve --port 8082 --host 0.0.0.0", "build": "vue-cli-service build", "build:prod": "vue-cli-service build --mode production", "lint": "vue-cli-service lint --fix", "test": "vue-cli-service test:unit", "analyze": "vue-cli-service build --analyze"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.5.0", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.5.0", "@fortawesome/vue-fontawesome": "^3.0.0", "axios": "^1.6.0", "bootstrap": "^5.3.0", "bootstrap-vue-next": "^0.15.0", "chart.js": "^4.5.0", "core-js": "^3.35.0", "date-fns": "^4.1.0", "gsap": "^3.13.0", "howler": "^2.2.4", "html2pdf.js": "^0.10.3", "lodash": "^4.17.0", "marked": "^16.0.0", "moment": "^2.30.0", "pinia": "^3.0.3", "vue": "^3.4.0", "vue-chartjs": "^5.3.0", "vue-router": "^4.2.0", "vuedraggable": "^4.1.0", "vuex": "^4.1.0", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-standard": "^8.0.0", "eslint": "^8.0.0", "eslint-plugin-vue": "^9.0.0", "sass": "^1.69.0", "sass-loader": "^13.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}