from typing import AsyncGenerator, List, Dict, Any
import anthropic
from fastapi import HTTPException
import os
import logging
import json
from dotenv import load_dotenv
import asyncio

load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

class SecondBrainService:
    def __init__(self):
        api_key = os.getenv('ANTHROPIC_API_KEY')
        if not api_key:
            logger.error("ANTHROPIC_API_KEY not found in environment variables")
            raise ValueError("ANTHROPIC_API_KEY is required but not set in environment")
            
        self.client = anthropic.Anthropic(api_key=api_key)
        self.model = "claude-3-opus-20240229"
        self.system_prompt = """Você é um assistente pessoal, Mentor AI, especializado em agendamento de revisões espaçadas e organização de estudos. 
    Sua tarefa é ajudar a programar as próximas revisões baseadas no resultado das revisões anteriores feitas.
    
    Fórmula para calcular o intervalo de dias até a próxima revisão com base na porcentagem de acertos:
    - Até 50%: 2 dias
    - 51-55%: 7 dias
    - 56-60%: 14 dias
    - 61-65%: 18 dias
    - 66-75%: 24 dias
    - 76-80%: 30 dias
    - 81-90%: 45 dias
    - 91-100%: 60 dias
    
    Além disso, você também pode:
    1. Auxiliar na criação de cronogramas de estudo personalizados
    2. Sugerir técnicas de memorização e aprendizado
    3. Responder dúvidas sobre conteúdo médico e acadêmico
    4. Fornecer dicas de organização e produtividade
    5. Ajudar a priorizar tópicos de estudo baseado em dificuldade e importância
    
    Sempre seja encorajador, motivacional e forneça respostas práticas e aplicáveis."""

    async def get_chat_response(self, user_message: str) -> AsyncGenerator[str, None]:
        try:
            logger.info(f"Generating response for message: {user_message[:50]}...")
            
            message = await self.client.messages.create(
                model=self.model,
                max_tokens=4096,
                temperature=0.7,
                system=self.system_prompt,
                messages=[{
                    "role": "user",
                    "content": user_message
                }],
                stream=True
            )

            async for chunk in message:
                if chunk.type == "content_block_delta":
                    yield chunk.delta.text
                elif chunk.type == "message_stop":
                    break

        except anthropic.APIError as e:
            logger.error(f"Anthropic API error: {str(e)}")
            yield f"Desculpe, ocorreu um erro na API: {str(e)}"
        except anthropic.APIConnectionError as e:
            logger.error(f"Anthropic connection error: {str(e)}")
            yield "Desculpe, estou tendo dificuldades para me conectar ao serviço. Por favor, tente novamente em alguns instantes."
        except anthropic.APITimeoutError as e:
            logger.error(f"Anthropic timeout error: {str(e)}")
            yield "A resposta demorou muito tempo. Por favor, tente uma pergunta mais específica ou tente novamente mais tarde."
        except Exception as e:
            logger.error(f"Unexpected error generating response: {str(e)}")
            yield f"Ocorreu um erro inesperado: {str(e)}"

    async def get_chat_response_with_context(self, user_message: str, chat_history: List[Dict[str, Any]]) -> AsyncGenerator[str, None]:
        try:
            formatted_messages = []
            
            # Format previous messages for context
            for msg in chat_history[-10:]:  # Use last 10 messages for context
                formatted_messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
            
            # Add the current user message
            formatted_messages.append({
                "role": "user",
                "content": user_message
            })
            
            message = await self.client.messages.create(
                model=self.model,
                max_tokens=4096,
                temperature=0.7,
                system=self.system_prompt,
                messages=formatted_messages,
                stream=True
            )

            async for chunk in message:
                if chunk.type == "content_block_delta":
                    yield chunk.delta.text
                elif chunk.type == "message_stop":
                    break

        except Exception as e:
            logger.error(f"Error generating response with context: {str(e)}")
            yield f"Ocorreu um erro ao processar sua pergunta: {str(e)}"

    async def test_connection(self) -> AsyncGenerator[str, None]:
        test_message = "Teste de conexão - por favor confirme que está funcionando."
        try:
            message = await self.client.messages.create(
                model=self.model,
                max_tokens=100,
                temperature=0.7,
                system="Você é um assistente de estudos. Responda brevemente confirmando que está funcionando e pronto para ajudar com organização de estudos e revisões.",
                messages=[{"role": "user", "content": test_message}],
                stream=True
            )

            async for chunk in message:
                if chunk.type == "content_block_delta":
                    yield chunk.delta.text
                elif chunk.type == "message_stop":
                    break

        except Exception as e:
            logger.error(f"Connection test failed: {str(e)}")
            yield f"Connection test failed: {str(e)}"

# Singleton instance
second_brain_service = SecondBrainService() 