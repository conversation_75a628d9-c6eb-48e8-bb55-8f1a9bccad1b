"""
Serviço de Cache para o sistema MentorAI
Implementa estratégias de cache distribuído e otimizações de performance

Recursos implementados:
- Cache na memória com TTL (Time-to-Live)
- Cache persistente baseado em arquivo para documentos e embeddings
- Cache distribuído opcional com suporte a Redis
- Invalidação seletiva e limpeza automática
"""

import os
import time
import json
import pickle
import hashlib
import logging
from typing import Any, Dict, List, Optional, Tuple, Union, Callable
from functools import wraps
from datetime import datetime, timedelta
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor

# Configurar logging
logger = logging.getLogger("cache-service")
logger.setLevel(logging.INFO)

# Constantes
DEFAULT_TTL = 3600  # 1 hora em segundos
DEFAULT_MEMORY_SIZE = 1000  # Número máximo de itens em memória
CACHE_DIR = Path("./cache")  # Diretório para cache persistente
EMBEDDINGS_CACHE_DIR = Path("./embeddings_cache")  # Diretório para cache de embeddings
EXECUTOR = ThreadPoolExecutor(max_workers=2)  # Thread pool para operações de I/O

# Garantir que diretórios de cache existam
CACHE_DIR.mkdir(exist_ok=True)
EMBEDDINGS_CACHE_DIR.mkdir(exist_ok=True)

# Cache em memória (dict simples)
_memory_cache: Dict[str, Tuple[Any, float]] = {}

class CacheService:
    """Implementação do serviço de cache para o MentorAI"""
    
    @staticmethod
    def _generate_key(prefix: str, *args, **kwargs) -> str:
        """
        Gera uma chave de cache única com base nos argumentos
        
        Args:
            prefix: Prefixo para a chave (ex: 'thanos', 'flashcards')
            *args, **kwargs: Argumentos a serem incluídos na chave
            
        Returns:
            String de chave de cache
        """
        # Concatenar argumentos em uma única string
        key_parts = [str(arg) for arg in args]
        key_parts.extend([f"{k}={v}" for k, v in sorted(kwargs.items())])
        key_str = ":".join([prefix] + key_parts)
        
        # Gerar hash para chaves muito longas
        if len(key_str) > 250:
            return f"{prefix}:{hashlib.md5(key_str.encode()).hexdigest()}"
            
        return key_str
    
    @staticmethod
    def _is_redis_available() -> bool:
        """Verifica se Redis está disponível para cache distribuído"""
        try:
            import redis
            client = redis.Redis(host='localhost', port=6379, db=0)
            return client.ping()
        except (ImportError, Exception):
            return False
    
    @staticmethod
    def _get_redis_client():
        """Obtém cliente Redis se disponível"""
        try:
            import redis
            return redis.Redis(host='localhost', port=6379, db=0)
        except ImportError:
            return None
    
    @classmethod
    def get_from_memory(cls, key: str) -> Optional[Any]:
        """
        Obtém valor do cache em memória
        
        Args:
            key: Chave do item no cache
            
        Returns:
            Valor ou None se não encontrado ou expirado
        """
        if key not in _memory_cache:
            return None
            
        value, expiry = _memory_cache[key]
        
        # Verificar expiração
        if expiry < time.time():
            # Remover item expirado
            del _memory_cache[key]
            return None
            
        logger.debug(f"Cache hit (memory): {key}")
        return value
    
    @classmethod
    def set_in_memory(cls, key: str, value: Any, ttl: int = DEFAULT_TTL) -> None:
        """
        Armazena valor no cache em memória
        
        Args:
            key: Chave para o item
            value: Valor a armazenar
            ttl: Tempo de vida em segundos
        """
        # Calcular timestamp de expiração
        expiry = time.time() + ttl
        
        # Limpar cache se atingir o limite
        if len(_memory_cache) >= DEFAULT_MEMORY_SIZE:
            # Remover o item mais antigo
            cls._clean_memory_cache(int(DEFAULT_MEMORY_SIZE * 0.2))  # Remove 20% dos itens
            
        # Armazenar valor com tempo de expiração
        _memory_cache[key] = (value, expiry)
        logger.debug(f"Cache set (memory): {key}")
    
    @classmethod
    def _clean_memory_cache(cls, count: int = 1) -> None:
        """
        Remove os itens mais antigos ou expirados do cache em memória
        
        Args:
            count: Número de itens a remover
        """
        if not _memory_cache:
            return
            
        # Primeiro tenta remover itens expirados
        now = time.time()
        expired_keys = [k for k, (_, exp) in _memory_cache.items() if exp < now]
        
        # Se não tiver itens expirados suficientes, remove os mais antigos
        if len(expired_keys) < count:
            # Ordenar por tempo de expiração (os que expiram primeiro são os mais antigos)
            sorted_keys = sorted(_memory_cache.keys(), 
                              key=lambda k: _memory_cache[k][1])
            keys_to_remove = sorted_keys[:count - len(expired_keys)]
            expired_keys.extend(keys_to_remove)
        
        # Remover os keys selecionados
        for key in expired_keys[:count]:
            if key in _memory_cache:
                del _memory_cache[key]
    
    @classmethod
    def get_from_file(cls, key: str) -> Optional[Any]:
        """
        Obtém valor do cache em arquivo
        
        Args:
            key: Chave do item no cache
            
        Returns:
            Valor ou None se não encontrado ou expirado
        """
        # Sanitizar chave para uso como nome de arquivo
        file_key = hashlib.md5(key.encode()).hexdigest()
        file_path = CACHE_DIR / f"{file_key}.cache"
        
        if not file_path.exists():
            return None
            
        try:
            with open(file_path, "rb") as f:
                data = pickle.load(f)
                
            # Verificar expiração
            if data["expiry"] < time.time():
                # Remover arquivo expirado em background
                EXECUTOR.submit(lambda: file_path.unlink(missing_ok=True))
                return None
                
            logger.debug(f"Cache hit (file): {key}")
            return data["value"]
            
        except (pickle.PickleError, IOError, KeyError) as e:
            logger.warning(f"Error reading cache file: {e}")
            # Remover arquivo corrompido
            EXECUTOR.submit(lambda: file_path.unlink(missing_ok=True))
            return None
    
    @classmethod
    def set_in_file(cls, key: str, value: Any, ttl: int = DEFAULT_TTL) -> None:
        """
        Armazena valor no cache em arquivo
        
        Args:
            key: Chave para o item
            value: Valor a armazenar
            ttl: Tempo de vida em segundos
        """
        # Sanitizar chave para uso como nome de arquivo
        file_key = hashlib.md5(key.encode()).hexdigest()
        file_path = CACHE_DIR / f"{file_key}.cache"
        
        # Preparar dados com expiração
        data = {
            "key": key,
            "value": value,
            "created": time.time(),
            "expiry": time.time() + ttl
        }
        
        # Salvar em arquivo
        def _save_to_file():
            try:
                with open(file_path, "wb") as f:
                    pickle.dump(data, f)
                logger.debug(f"Cache set (file): {key}")
            except (pickle.PickleError, IOError) as e:
                logger.warning(f"Error writing cache file: {e}")
        
        # Executar em thread separada para não bloquear
        EXECUTOR.submit(_save_to_file)
    
    @classmethod
    def get(cls, key: str, default: Any = None) -> Any:
        """
        Obtém um valor do cache (verifica memória e arquivo)
        
        Args:
            key: Chave de cache
            default: Valor padrão se não encontrado
            
        Returns:
            Valor ou default
        """
        # Tentar obter da memória primeiro (mais rápido)
        value = cls.get_from_memory(key)
        if value is not None:
            return value
            
        # Tentar obter do arquivo
        value = cls.get_from_file(key)
        if value is not None:
            # Colocar também na memória para acelerar acessos futuros
            cls.set_in_memory(key, value)
            return value
            
        # Por último, tentar Redis se disponível
        if cls._is_redis_available():
            redis_client = cls._get_redis_client()
            if redis_client:
                try:
                    result = redis_client.get(key)
                    if result:
                        value = pickle.loads(result)
                        # Colocar também na memória
                        cls.set_in_memory(key, value)
                        return value
                except Exception as e:
                    logger.warning(f"Error getting from Redis: {e}")
        
        return default
    
    @classmethod
    def set(cls, key: str, value: Any, ttl: int = DEFAULT_TTL, persist: bool = False) -> None:
        """
        Armazena um valor no cache
        
        Args:
            key: Chave de cache
            value: Valor a armazenar
            ttl: Tempo de vida em segundos
            persist: Se deve persistir em arquivo
        """
        # Sempre armazenar em memória (mais rápido)
        cls.set_in_memory(key, value, ttl)
        
        # Se persist=True, gravar também em arquivo
        if persist:
            cls.set_in_file(key, value, ttl)
            
        # Se Redis disponível, armazenar também lá
        if cls._is_redis_available():
            redis_client = cls._get_redis_client()
            if redis_client:
                try:
                    serialized = pickle.dumps(value)
                    redis_client.setex(key, ttl, serialized)
                except Exception as e:
                    logger.warning(f"Error setting in Redis: {e}")
    
    @classmethod
    def delete(cls, key: str) -> None:
        """
        Remove um item do cache
        
        Args:
            key: Chave do item a remover
        """
        # Remover da memória
        if key in _memory_cache:
            del _memory_cache[key]
            
        # Remover do arquivo
        file_key = hashlib.md5(key.encode()).hexdigest()
        file_path = CACHE_DIR / f"{file_key}.cache"
        EXECUTOR.submit(lambda: file_path.unlink(missing_ok=True))
        
        # Remover do Redis
        if cls._is_redis_available():
            redis_client = cls._get_redis_client()
            if redis_client:
                try:
                    redis_client.delete(key)
                except Exception as e:
                    logger.warning(f"Error deleting from Redis: {e}")
    
    @classmethod
    def clear_all(cls) -> None:
        """Limpa todo o cache"""
        # Limpar cache em memória
        _memory_cache.clear()
        
        # Limpar cache em arquivo
        for file_path in CACHE_DIR.glob("*.cache"):
            EXECUTOR.submit(lambda: file_path.unlink(missing_ok=True))
            
        # Limpar Redis
        if cls._is_redis_available():
            redis_client = cls._get_redis_client()
            if redis_client:
                try:
                    redis_client.flushdb()
                except Exception as e:
                    logger.warning(f"Error clearing Redis: {e}")


# Implementações específicas para ThanosAI

class ThanosCache:
    """Implementação de cache específica para ThanosAI"""
    
    @staticmethod
    def save_embeddings_to_cache(document_id: str, chunks_with_embeddings: List[Dict]) -> bool:
        """
        Salva embeddings de documento em cache
        
        Args:
            document_id: ID do documento
            chunks_with_embeddings: Lista de chunks com embeddings
            
        Returns:
            True se sucesso, False caso contrário
        """
        file_path = EMBEDDINGS_CACHE_DIR / f"{document_id}.pkl"
        
        try:
            with open(file_path, "wb") as f:
                pickle.dump(chunks_with_embeddings, f)
            logger.info(f"Saved embeddings for document {document_id}")
            return True
        except Exception as e:
            logger.error(f"Error saving embeddings: {e}")
            return False
    
    @staticmethod
    def load_embeddings_from_cache(document_id: str) -> Optional[List[Dict]]:
        """
        Carrega embeddings de documento do cache
        
        Args:
            document_id: ID do documento
            
        Returns:
            Lista de chunks com embeddings ou None
        """
        file_path = EMBEDDINGS_CACHE_DIR / f"{document_id}.pkl"
        
        if not file_path.exists():
            return None
            
        try:
            with open(file_path, "rb") as f:
                chunks = pickle.load(f)
            logger.info(f"Loaded embeddings for document {document_id}")
            return chunks
        except Exception as e:
            logger.error(f"Error loading embeddings: {e}")
            return None
    
    @staticmethod
    def clear_document_embeddings(document_id: str) -> bool:
        """
        Remove embeddings de documento do cache
        
        Args:
            document_id: ID do documento
            
        Returns:
            True se sucesso, False caso contrário
        """
        file_path = EMBEDDINGS_CACHE_DIR / f"{document_id}.pkl"
        
        try:
            if file_path.exists():
                file_path.unlink()
                logger.info(f"Cleared embeddings for document {document_id}")
            return True
        except Exception as e:
            logger.error(f"Error clearing embeddings: {e}")
            return False
    
    @staticmethod
    def clear_all_embeddings() -> bool:
        """
        Remove todos os embeddings do cache
        
        Returns:
            True se sucesso, False caso contrário
        """
        try:
            for file_path in EMBEDDINGS_CACHE_DIR.glob("*.pkl"):
                file_path.unlink()
            logger.info("Cleared all embeddings")
            return True
        except Exception as e:
            logger.error(f"Error clearing all embeddings: {e}")
            return False


# Decorador para cache de funções
def cached(ttl: int = DEFAULT_TTL, key_prefix: str = None, persist: bool = False):
    """
    Decorador para adicionar cache a funções
    
    Args:
        ttl: Tempo de vida em segundos
        key_prefix: Prefixo opcional para a chave de cache
        persist: Se deve persistir o resultado em cache de arquivo
        
    Exemplo:
        @cached(ttl=3600, key_prefix="user_profile")
        async def get_user_profile(user_id: str):
            # Função que é custosa para executar
            return await database.get_user(user_id)
    """
    def decorator(func):
        # Determinar prefixo da chave
        prefix = key_prefix or func.__name__
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Gerar chave de cache
            cache_key = CacheService._generate_key(prefix, *args, **kwargs)
            
            # Verificar cache
            cached_value = CacheService.get(cache_key)
            if cached_value is not None:
                return cached_value
                
            # Executar função
            result = await func(*args, **kwargs)
            
            # Armazenar resultado em cache
            CacheService.set(cache_key, result, ttl, persist)
            
            return result
            
        return wrapper
    return decorator


# Exportar serviços e utilitários
cache_service = CacheService()
thanos_cache = ThanosCache()

# Inicializar limpeza periódica de caches
def _initialize_cache_cleanup():
    """Inicializa limpeza periódica de cache em background"""
    import atexit
    import threading
    
    def _cleanup_task():
        """Tarefa que executa periodicamente para limpar cache expirado"""
        while True:
            try:
                # Limpar cache em memória
                now = time.time()
                expired_keys = [k for k, (_, exp) in _memory_cache.items() if exp < now]
                for key in expired_keys:
                    if key in _memory_cache:
                        del _memory_cache[key]
                
                # Limpar arquivos de cache expirados
                for file_path in CACHE_DIR.glob("*.cache"):
                    try:
                        with open(file_path, "rb") as f:
                            data = pickle.load(f)
                        if data["expiry"] < now:
                            file_path.unlink()
                    except Exception:
                        # Se não conseguir ler, também remove
                        try:
                            file_path.unlink()
                        except Exception:
                            pass
            except Exception as e:
                logger.error(f"Error in cache cleanup: {e}")
                
            # Executar a cada hora
            time.sleep(3600)
    
    # Iniciar thread de limpeza
    cleanup_thread = threading.Thread(target=_cleanup_task, daemon=True)
    cleanup_thread.start()
    
    # Registrar para encerrar quando a aplicação terminar
    atexit.register(lambda: EXECUTOR.shutdown(wait=False))

# Iniciar limpeza de cache apenas se for o módulo principal
if __name__ == "__main__":
    _initialize_cache_cleanup()