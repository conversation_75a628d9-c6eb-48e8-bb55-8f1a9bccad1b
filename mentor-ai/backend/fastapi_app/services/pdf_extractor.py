"""
PDF Extractor Service for ThanosAI

This module provides functionality to extract text and structure from PDF files
for use in the ThanosAI's RAG system.
"""

import os
import logging
import tempfile
from pathlib import Path
from typing import List, Dict, Any, Optional, Union

# Initialize logger
logger = logging.getLogger("pdf-extractor")

class PDFExtractor:
    """Service for extracting text and structure from PDF documents."""
    
    def __init__(self):
        """Initialize the PDF extractor service."""
        # Check if PyPDF2 is available, otherwise use fallback
        try:
            import PyPDF2
            self.extractor_type = "PyPDF2"
            logger.info("Using PyPDF2 for PDF extraction")
        except ImportError:
            try:
                import fitz  # PyMuPDF
                self.extractor_type = "PyMuPDF"
                logger.info("Using PyMuPDF for PDF extraction")
            except ImportError:
                self.extractor_type = "fallback"
                logger.warning("PDF extraction libraries not found, using fallback method")
    
    def extract_text(self, file_path: Union[str, Path], **options) -> Dict[str, Any]:
        """
        Extract text from a PDF file.
        
        Args:
            file_path: Path to the PDF file
            options: Additional extraction options
                - extract_images: Whether to extract images (default: False)
                - preserve_layout: Whether to preserve layout (default: True)
                - extract_metadata: Whether to extract metadata (default: True)
                - quality: Extraction quality - 'high', 'medium', 'low' (default: 'medium')
        
        Returns:
            Dictionary containing:
                - text: Extracted text content
                - metadata: Document metadata if extracted
                - page_count: Number of pages
                - structure: Document structure information
        """
        # Set default options
        extract_images = options.get('extract_images', False)
        preserve_layout = options.get('preserve_layout', True)
        extract_metadata = options.get('extract_metadata', True)
        quality = options.get('quality', 'medium')
        
        logger.info(f"Extracting text from PDF: {file_path}")
        
        # Ensure file path is a string
        if isinstance(file_path, Path):
            file_path = str(file_path)
        
        try:
            # Use appropriate extractor based on available libraries
            if self.extractor_type == "PyPDF2":
                return self._extract_with_pypdf2(file_path, **options)
            elif self.extractor_type == "PyMuPDF":
                return self._extract_with_pymupdf(file_path, **options)
            else:
                return self._extract_fallback(file_path, **options)
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {e}")
            # Return minimal information in case of error
            return {
                "text": f"[Error extracting PDF content: {str(e)}]",
                "metadata": {},
                "page_count": 0,
                "structure": []
            }
    
    def _extract_with_pypdf2(self, file_path: str, **options) -> Dict[str, Any]:
        """Extract text using PyPDF2."""
        import PyPDF2
        
        extract_metadata = options.get('extract_metadata', True)
        
        with open(file_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            
            # Extract text from each page
            text = ""
            page_texts = []
            for i, page in enumerate(reader.pages):
                page_text = page.extract_text()
                text += page_text + "\n\n"
                page_texts.append({
                    "page_number": i + 1,
                    "text": page_text
                })
            
            # Extract metadata if requested
            metadata = {}
            if extract_metadata and reader.metadata:
                metadata = {
                    "title": reader.metadata.title,
                    "author": reader.metadata.author,
                    "creator": reader.metadata.creator,
                    "producer": reader.metadata.producer,
                    "subject": reader.metadata.subject
                }
                # Clean up None values
                metadata = {k: v for k, v in metadata.items() if v is not None}
            
            return {
                "text": text,
                "metadata": metadata,
                "page_count": len(reader.pages),
                "structure": page_texts
            }
    
    def _extract_with_pymupdf(self, file_path: str, **options) -> Dict[str, Any]:
        """Extract text using PyMuPDF (fitz)."""
        import fitz
        
        extract_images = options.get('extract_images', False)
        extract_metadata = options.get('extract_metadata', True)
        preserve_layout = options.get('preserve_layout', True)
        
        doc = fitz.open(file_path)
        
        # Extract text with layout preservation if requested
        text = ""
        page_texts = []
        
        for i in range(len(doc)):
            page = doc[i]
            if preserve_layout:
                page_text = page.get_text("text")
            else:
                page_text = page.get_text("text")  # Could use "blocks" for less layout preservation
            
            text += page_text + "\n\n"
            page_texts.append({
                "page_number": i + 1,
                "text": page_text
            })
        
        # Extract metadata if requested
        metadata = {}
        if extract_metadata:
            metadata = doc.metadata
        
        # Extract images if requested (basic implementation)
        images = []
        if extract_images:
            for i in range(len(doc)):
                page = doc[i]
                image_list = page.get_images(full=True)
                for img_index, img in enumerate(image_list):
                    xref = img[0]
                    base_image = doc.extract_image(xref)
                    images.append({
                        "page_number": i + 1,
                        "image_index": img_index,
                        "width": base_image["width"],
                        "height": base_image["height"]
                    })
        
        return {
            "text": text,
            "metadata": metadata,
            "page_count": len(doc),
            "structure": page_texts,
            "images": images if extract_images else []
        }
    
    def _extract_fallback(self, file_path: str, **options) -> Dict[str, Any]:
        """
        Fallback extraction when no PDF libraries are available.
        Attempts to use command-line tools like pdftotext if available.
        """
        # Try using pdftotext command-line tool
        try:
            import subprocess
            import tempfile
            
            with tempfile.NamedTemporaryFile(suffix='.txt') as temp:
                # Run pdftotext command
                result = subprocess.run(
                    ['pdftotext', '-layout', file_path, temp.name],
                    capture_output=True,
                    text=True,
                    check=False
                )
                
                if result.returncode == 0:
                    # Read the extracted text
                    with open(temp.name, 'r', encoding='utf-8') as f:
                        text = f.read()
                    
                    return {
                        "text": text,
                        "metadata": {},
                        "page_count": text.count('\f') + 1,  # Count form feeds plus 1
                        "structure": []
                    }
        except Exception as e:
            logger.error(f"Fallback extraction failed: {e}")
        
        # If everything fails, return placeholder
        return {
            "text": "[PDF text extraction is not available. Please install PyPDF2 or PyMuPDF]",
            "metadata": {},
            "page_count": 0,
            "structure": []
        }
    
    def chunk_text(self, text: str, chunk_size: int = 1000, chunk_overlap: int = 200) -> List[Dict[str, Any]]:
        """
        Split text into chunks for processing.
        
        Args:
            text: Text to chunk
            chunk_size: Maximum chunk size (in characters)
            chunk_overlap: Overlap between chunks (in characters)
            
        Returns:
            List of chunks with index and text
        """
        if not text:
            return []
        
        chunks = []
        start = 0
        
        while start < len(text):
            # Determine end position
            end = start + chunk_size
            if end >= len(text):
                end = len(text)
            else:
                # Try to find a good breaking point (newline or space)
                if end < len(text) and text[end] != '\n':
                    # Look for newline before the end
                    newline_pos = text.rfind('\n', start, end)
                    space_pos = text.rfind(' ', start, end)
                    
                    if newline_pos > start and newline_pos > start + chunk_size * 0.5:
                        end = newline_pos + 1
                    elif space_pos > start and space_pos > start + chunk_size * 0.5:
                        end = space_pos + 1
            
            # Extract chunk
            chunk_text = text[start:end].strip()
            
            if chunk_text:
                chunks.append({
                    "chunk_index": len(chunks),
                    "text": chunk_text,
                    "char_start": start,
                    "char_end": end
                })
            
            # Move to next chunk with overlap
            start = end - chunk_overlap if end < len(text) else len(text)
            
            # Avoid getting stuck in a loop
            if start >= end:
                break
        
        return chunks

# Create a singleton instance
pdf_extractor = PDFExtractor()