"""
RAG Service - Advanced Retrieval Augmented Generation
Provides document vectorization, embedding, and similarity search capabilities
"""

import numpy as np
import os
import pickle
import logging
from typing import List, Dict, Any, Optional, Tuple, Union
from pathlib import Path
from datetime import datetime

logger = logging.getLogger("rag-service")

# In production, use an actual embedding model like sentence-transformers
# Here we'll simulate embeddings for development
class RAGService:
    def __init__(self):
        """Initialize the RAG service with embedding capabilities"""
        self.embedding_cache = {}
        self.embedding_dimension = 768  # Simulated dimension
        self.cache_dir = Path("./embeddings_cache")
        self.cache_dir.mkdir(exist_ok=True)
        
        logger.info("RAG Service initialized")
        
    def _simulate_embedding(self, text: str) -> np.ndarray:
        """
        Simulate text embedding for development.
        In production, replace with real embedding model like sentence-transformers.
        """
        # Generate a random embedding vector that's deterministic for the same text
        # This allows similar texts to have similar embeddings
        np.random.seed(hash(text) % 2**32)
        embedding = np.random.randn(self.embedding_dimension)
        # Normalize to unit length (cosine similarity)
        return embedding / np.linalg.norm(embedding)
        
    def get_embedding(self, text: str) -> np.ndarray:
        """Get text embedding, using cache if available"""
        if text in self.embedding_cache:
            return self.embedding_cache[text]
            
        # Generate embedding
        embedding = self._simulate_embedding(text)
        
        # Cache the embedding
        self.embedding_cache[text] = embedding
        return embedding
        
    def embed_document_chunks(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Generate embeddings for document chunks
        
        Args:
            chunks: List of document chunks with 'text' key
            
        Returns:
            List of chunks with embeddings added
        """
        embedded_chunks = []
        
        for chunk in chunks:
            if 'text' not in chunk:
                logger.warning(f"Chunk missing 'text' key: {chunk}")
                continue
                
            # Generate embedding for chunk text
            embedding = self.get_embedding(chunk['text'])
            
            # Add embedding to chunk
            chunk_with_embedding = {
                **chunk,
                'embedding': embedding.tolist()
            }
            
            embedded_chunks.append(chunk_with_embedding)
            
        logger.info(f"Embedded {len(embedded_chunks)} document chunks")
        return embedded_chunks
        
    def retrieve_similar_chunks(
        self, 
        query: str, 
        chunks: List[Dict[str, Any]], 
        top_k: int = 3,
        similarity_threshold: float = 0.5
    ) -> List[Dict[str, Any]]:
        """
        Retrieve chunks similar to the query using cosine similarity
        
        Args:
            query: Query text
            chunks: List of document chunks with embeddings
            top_k: Maximum number of chunks to return
            similarity_threshold: Minimum similarity score to include
            
        Returns:
            List of most similar chunks with similarity scores
        """
        if not chunks:
            logger.warning("No chunks provided for similarity search")
            return []
            
        # Get query embedding
        query_embedding = self.get_embedding(query)
        
        # Calculate similarity scores
        similarities = []
        for i, chunk in enumerate(chunks):
            # Get or generate chunk embedding
            if 'embedding' in chunk:
                chunk_embedding = np.array(chunk['embedding'])
            else:
                chunk_embedding = self.get_embedding(chunk['text'])
            
            # Calculate cosine similarity
            similarity = np.dot(query_embedding, chunk_embedding)
            
            similarities.append({
                'chunk_index': i,
                'similarity': similarity,
                'text': chunk['text']
            })
        
        # Sort by similarity (descending)
        similarities.sort(key=lambda x: x['similarity'], reverse=True)
        
        # Filter by threshold and get top_k
        filtered_similarities = [
            s for s in similarities if s['similarity'] >= similarity_threshold
        ][:top_k]
        
        logger.info(f"Retrieved {len(filtered_similarities)} similar chunks for query")
        return filtered_similarities
    
    def enhance_prompt_with_context(
        self,
        query: str,
        similar_chunks: List[Dict[str, Any]],
        language: str = 'pt'
    ) -> str:
        """
        Enhance user query with relevant document chunks
        
        Args:
            query: Original user query
            similar_chunks: Similar document chunks with text and similarity
            language: User's preferred language
            
        Returns:
            Enhanced prompt for the AI model
        """
        if not similar_chunks:
            return query
            
        # Sort chunks by similarity (highest first)
        sorted_chunks = sorted(
            similar_chunks, 
            key=lambda x: x['similarity'], 
            reverse=True
        )
        
        # Format chunk texts
        context_texts = []
        for i, chunk in enumerate(sorted_chunks):
            chunk_text = chunk['text'].strip()
            # Add chunk index and similarity score
            context_texts.append(
                f"Chunk {i+1} (similarity: {chunk['similarity']:.2f}):\n{chunk_text}"
            )
            
        # Create introduction based on language
        if language == 'pt':
            intro = "Para responder à pergunta do usuário, considere os seguintes trechos relevantes do documento:"
            outro = f"\n\nPergunta do usuário: {query}\n\nResponda com base nos trechos fornecidos acima."
        else:
            intro = "To answer the user's question, consider the following relevant passages from the document:"
            outro = f"\n\nUser question: {query}\n\nAnswer based on the provided passages above."
            
        # Combine into enhanced prompt
        enhanced_prompt = f"{intro}\n\n{os.linesep.join(context_texts)}{outro}"
        
        return enhanced_prompt
        
    def save_embeddings_to_cache(self, document_id: str, chunks: List[Dict[str, Any]]) -> bool:
        """Save document chunks with embeddings to disk cache"""
        try:
            cache_path = self.cache_dir / f"{document_id}.pkl"
            with open(cache_path, 'wb') as f:
                pickle.dump(chunks, f)
            logger.info(f"Saved embeddings for document {document_id} to cache")
            return True
        except Exception as e:
            logger.error(f"Error saving embeddings to cache: {e}")
            return False
            
    def load_embeddings_from_cache(self, document_id: str) -> Optional[List[Dict[str, Any]]]:
        """Load document chunks with embeddings from disk cache"""
        try:
            cache_path = self.cache_dir / f"{document_id}.pkl"
            if not cache_path.exists():
                return None
                
            with open(cache_path, 'rb') as f:
                chunks = pickle.load(f)
            logger.info(f"Loaded embeddings for document {document_id} from cache")
            return chunks
        except Exception as e:
            logger.error(f"Error loading embeddings from cache: {e}")
            return None
            
# Create a singleton instance
rag_service = RAGService()